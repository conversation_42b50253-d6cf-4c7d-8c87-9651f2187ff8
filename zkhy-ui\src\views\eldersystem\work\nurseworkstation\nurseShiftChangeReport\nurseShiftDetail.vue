<template>
  <div class="wrapBox" v-loading="loading">
          <el-form :inline="true" :model="formRoom" label-width="100px" ref="formRef">    
              <div class="room_info_top">
                  <div class="title_room">
                      <h3>房间信息</h3>
                  </div>
                   <div class="room_form">
                      <el-row :gutter="24">
                          <el-col :span="8">
                              <el-form-item label="楼栋信息" prop="buildingId">
                                  <!-- <el-select v-model="formRoom.buildingId" style="width: 200px" @change="getFloorListData">
                                      <el-option v-for="item in buildingList" :key="item.value" :label="item.buildingName" :value="item.id" />
                                  </el-select> -->
                              </el-form-item>
                          </el-col>
                          <el-col :span="8">
                              <el-form-item label="楼栋层数" prop="floorId">
                                  <!-- <el-select v-model="formRoom.floorId" :disabled="!formRoom.buildingId" style="width: 200px" @change="getRoomListData">
                                      <el-option v-for="item in floorList" :key="item.value" :label="item.floorName" :value="item.id" />
                                  </el-select> -->
                              </el-form-item>
                          </el-col>
                          <el-col :span="8">
                            <el-form-item label="交接日期" prop="handoverDate">
                            </el-form-item>
                        </el-col>
                      </el-row>
                   </div>
              </div>
              <div class="room_info_top">
                  <div class="title_room">
                      <h3>人员交接信息</h3>
                  </div>
                   <div class="room_form">
                      <div class="title_room_h4">
                         <span>白班交接信息</span>
                      </div>
                      <el-row :gutter="24">
                          <el-col :span="8">
                             <el-form-item label="白班护士" prop="dayNurse">
                                      <!-- <el-select v-model="formRoom.dayNurse" style="width: 200px">
                                          <el-option v-for="item in nurseUser" :key="item.userId" :label="item.nickName" :value="item.nickName" />
                                      </el-select> -->

                                  </el-form-item>
                             </el-col>
                             <el-col :span="8">
                              <el-form-item label="交班班次" prop="dayHandoverTime">
                                  </el-form-item>
                             </el-col>
                             <el-col :span="8">
                              <el-form-item label="接班护士" prop="relievingNurse">
                                 
                                  </el-form-item>
                             </el-col>
                             <el-col :span="8">
                              <el-form-item label="交接人数" prop="dayTotalCount">
                              </el-form-item>
                             </el-col>
                             <el-col :span="8">
                              <el-form-item label="外出人数" prop="dayOutCount">
                              </el-form-item>
                             </el-col>
                             <el-col :span="8">
                              <el-form-item label="离院人数" prop="dayLeaveCount">
                              </el-form-item>
                             </el-col>
                             <el-col :span="8">
                              <el-form-item label="病危人数" prop="dayCriticalCount">
                              </el-form-item>
                             </el-col>
                             <el-col :span="8">
                              <el-form-item label="死亡人数" prop="dayDeathCount">
                              </el-form-item>
                             </el-col>
                      </el-row>
                   </div>
                   <div class="room_form">
                      <div class="title_room_h5">
                         <span><el-icon color="#FF00FF"><Moon /></el-icon>&nbsp;夜班交接信息</span>
                      </div>
                      <el-row :gutter="24">
                          <el-col :span="8">
                             <el-form-item label="夜班护士" prop="nightNurse">
                                    
                                  </el-form-item>
                             </el-col>
                             <el-col :span="8">
                              <el-form-item label="交接班次" prop="nightHandoverTime">
                                 
                                  </el-form-item>
                             </el-col>
                             <el-col :span="8">
                              <el-form-item label="交接人数" prop="nightTotalCount">
                              </el-form-item>
                             </el-col>
                             <el-col :span="8">
                              <el-form-item label="外出人数" prop="nightOutCount">
                              </el-form-item>
                             </el-col>
                             <el-col :span="8">
                              <el-form-item label="离院人数" prop="nightLeaveCount">
                              </el-form-item>
                             </el-col>
                             <el-col :span="8">
                              <el-form-item label="病危人数" prop="nightCriticalCount">
                              </el-form-item>
                             </el-col>
                             <el-col :span="8">
                              <el-form-item label="死亡人数" prop="nightDeathCount">
                              </el-form-item>
                             </el-col>
                      </el-row>
                   </div>
              </div>
              <div class="bottom_room_table">
                  <el-table :data="formRoom.tNurseHandoverBedList" style="width: 100%" border>
                    <el-table-column label="房间号" min-width="180" align="center">
                      <template #default="scope">
                         
                      </template>
                  </el-table-column>

                  <!-- 床位号 -->
                  <el-table-column label="床位号" width="180" align="center">
                      <template #default="scope">
                          
                      </template>
                  </el-table-column>

                  <!-- 老人姓名 -->
                  <el-table-column label="老人姓名" width="180" align="center">
                      <template #default="scope">
                         
                      </template>
                  </el-table-column>

                  <!-- 白班交接内容 -->
                  <el-table-column label="白班交接内容" min-width="300" align="center">
                      <template #default="scope">
                        
                      </template>
                  </el-table-column>

                  <!-- 夜班交接内容 -->
                  <el-table-column label="夜班交接内容" min-width="300" align="center">
                      <template #default="scope">
                          
                      </template>
                  </el-table-column>
                  </el-table>
              </div>
          </el-form>
           <div class="footer_btn">
              <el-button @click="cancelForm" type="primary" plain>返回 </el-button>
           </div>
  </div>
</template>

<script setup>
import { ElMessage,ElMessageBox } from 'element-plus';
import { getBuildingList, getFloorList, getRoomCardList } from '@/api/live/roommanage'
import { listRoom } from "@/api/roominfo/tLiveRoom";
import { listBed } from "@/api/roominfo/tLiveBed";
import {addNurseHandover,getRoleInfo,getOlderInfo} from '@/api/nurse/index'
import {
Delete
} from '@element-plus/icons-vue'
const {
  proxy
} = getCurrentInstance()
const router = useRouter()
const buildingList = ref([])//楼栋下拉列表
const floorList = ref([])//楼层下拉列表
const bedList = ref([])//床位下拉列表
const loading = ref(false)
const formRoom= ref({
  tNurseHandoverBedList: []  // 将床位数据整合到formRoom中
})
const nurseUser= ref([])
const rules = ref({
  buildingId: [
      { required: true, message: '请选择楼栋', trigger: 'change' },
  ],
  floorId: [
      { required: true, message: '请选择楼层', trigger: 'change' },
  ],
  handoverDate:  [
      { required: true, message: '请选择交接日期', trigger: 'change' },
  ],
  relievingNurse:[
      { required: true, message: '请选择接班护士', trigger: 'change' },
  ],
  dayHandoverTime:[
      { required: true, message: '请选择交接日期', trigger: 'change' },
  ],
  dayTotalCount:[
      { required: true, message: '请输入', trigger: 'blur' },
  ],
  dayOutCount:[
      { required: true, message: '请输入', trigger: 'blur' },
  ],
  dayLeaveCount:[
      { required: true, message: '请输入', trigger: 'blur' },
  ],
  dayCriticalCount:[
      { required: true, message: '请输入', trigger: 'blur' },
  ],
  dayDeathCount:[
      { required: true, message: '请输入', trigger: 'blur' },
  ],
  nightNurse:[{ required: true, message: '请选择夜班护士', trigger: 'change' },],

 nightHandoverTime:[
      { required: true, message: '请选择交接班次', trigger: 'change' },
  ],
  nightTotalCount:[
      { required: true, message: '请输入', trigger: 'blur' },
  ],
  nightOutCount:[
      { required: true, message: '请输入', trigger: 'blur' },
  ],
  nightLeaveCount:[
      { required: true, message: '请输入', trigger: 'blur' },
  ],
  nightCriticalCount:[
      { required: true, message: '请输入', trigger: 'blur' },
  ],
  nightDeathCount:[
      { required: true, message: '请输入', trigger: 'blur' },
  ],
  requiredSelect: { 
      required: true, 
      message: '请选择', 
      trigger: 'change' 
  },
  requiredInput: { 
      required: true, 
      message: '请输入', 
      trigger: 'blur' 
  }
})
const roomOptions = ref([]);
const getFloorListData =async (val) =>{    
  floorList.value = []
  formRoom.value.tNurseHandoverBedList = []
  formRoom.value.floorId = ""
  const res = await getFloorList(val)
  floorList.value = res.rows;
}
const getRoomListData =async (val) =>{
  roomOptions.value = []
  formRoom.value.tNurseHandoverBedList = [{
    roomId:'',
    bedNumber:'',
    elderName:'',
    handoverContent1:'',
    handoverContent2:''
  }]
  const roomsRes = await listRoom({ floorId: val })
  roomOptions.value = roomsRes.rows;
}
const handleRoomChange = async (row) => {
  row.bedNumber = '' // 清空之前选择的床位
  row.elderName = '' // 清空之前选择的老人
  const bedsRes = await listBed({ roomId: row.roomId })
  bedList.value = bedsRes.rows;
  row.roomName = roomOptions.value?.filter(b => b.id === row.roomId)[0]?.roomName || ''
}
const handleBedChange = async (row) => {
  row.elderName = '' // 清空之前选择的老人
  const bedId = bedList.value.filter(b => b.bedNumber === row.bedNumber)[0]?.id
  const elderRes = await getOlderInfo({ bedId: bedId })
  row.elderName = elderRes.rows[0]?.elderName || ''
  row.elderAge = elderRes.rows[0]?.age || ''
  row.elderGender = elderRes.rows[0]?.gender || ''
  row.bedId = bedId
}
//添加床位
const addBedHandoverDetail = () => {
  formRoom.value.tNurseHandoverBedList.push({
      roomId:'',
      bedNumber:'',
      elderName:'',
      handoverContent1:'',
      handoverContent2:''
  });
};
const deleteRow = (index) => {
  ElMessageBox.confirm('确认删除该条记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
  }).then(() => {
      formRoom.value.tNurseHandoverBedList.splice(index, 1);
      ElMessage.success('删除成功');
  });
};
const submitForm = () => {
  console.log(formRoom.value,'dayin')
  loading.value = true
  proxy.$refs["formRef"].validate(async (valid) => {
      if (valid) {            
       let nurseList = []
        formRoom.value.tNurseHandoverBedList.forEach(item=>{
           nurseList.push({
              roomId: item.roomId,
              roomName: item.roomName,
              bedNumber: item.bedNumber,
              bedId: item.bedId,
              elderName: item.elderName,
              elderAge: item.elderAge,
              elderGender: item.elderGender,
              handoverContent1: item.handoverContent1,
              handoverContent2: item.handoverContent2,
           })
        })
          let submitForm = {
              ...formRoom.value,
              buildingName:buildingList.value.find(b => b.id === formRoom.value.buildingId)?.buildingName || '',
              floorNumber:floorList.value.find(f => f.id === formRoom.value.floorId)?.floorNumber || '',
              tNurseHandoverBedList: nurseList || [],
          }
          console.log(submitForm,'ff888888888888888888')
          const res = await addNurseHandover(submitForm);
          if(res.code === 200){
              ElMessage.success('提交成功');  
              proxy.$tab.closeOpenPage();
              // 返回上一页面
              router.go(-1);
          }else{
              ElMessage.error(res.msg);
          }
          loading.value = false
      } else {
          loading.value = false
          ElMessage.error('请填写完整信息');
          return false;
      }
  });
}
const cancelForm = () => {
  proxy.$tab.closeOpenPage();
  // 返回上一页面
  router.go(-1);
}
const getAuthUser = async () => {
    const res = await getRoleInfo({roleKeys:['nurse'],pageSize:1000})
    nurseUser.value = res.rows || []
}
const getBuildingListData = async () => {    
  const res = await getBuildingList()
  buildingList.value = res.rows || []
}
function initRequest() {
  getBuildingListData()
  getAuthUser()
}
// 返回工作台
const goBack = () => {
router.push('/work/nurseworkstation')
}
onMounted(() => {
  initRequest()  
})
</script>

<style scoped>
.wrapBox{
  padding:10px;
}
.title_room {
  color: var(--el-color-primary);
  font-size: 15px;
  font-weight: 700;
  padding:10px 0;
  h3{
      font-weight: bold;
      font-size: 16px;
      color: #2c3e50;
      border-bottom: 1px solid #e0e7ef;
      padding-bottom: 8px;
  }
}
.room_info_top,.bottom_room_table{
  background: #f8f9fa;
  padding: 0px 24px 20px 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.title_room_h5 {
  font-size: 14px;
  color: #666666;
  padding-left: 8px;
  margin-bottom: 10px;
}

.title_room_h4 {
  font-size: 14px;
  color: #666666;
  padding-left: 25px;
  margin-bottom: 10px;
  position: relative;
  &::before {
      position: absolute;
      left: 10px;
      top: 6px;
      content: '';
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: rgb(235, 152, 10);
  }
}
.add_room_table{
  text-align: right;
}
.footer_btn{
  text-align: right;
  margin-top: 20px;
  padding-bottom: 20px;
}
.formRow{
  display: flex;
  background: #f8f9fa;
  justify-content: center;
  align-items: center;
  padding-top: 15px;
}
</style>