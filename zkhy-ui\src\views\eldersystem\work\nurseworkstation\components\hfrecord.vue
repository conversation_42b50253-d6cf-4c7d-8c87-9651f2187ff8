<template>
  <div class="hfrecords" v-loading="loading">
  <el-dialog
    v-model="dialogVisible"
    title="详情"
    width="800px"
    @close="handleClose"
  >
   <div class="title_room">
       房间信息
   </div>
    <div v-if="recordData" class="detail-content">
      <div class="room-info">
        <div class="info-left">
          <div class="info-item">
            <span class="label">房间号：</span>
            <span class="value">{{ recordData.roomNumber || '-'}}</span>
          </div>
          <div class="info-item">
            <span class="label">老人姓名：</span>
            <span class="value">{{ recordData.elderName || '-'}}</span>
          </div>
          <div class="info-item">
            <span class="label">床位号：</span>
            <span class="value">{{ recordData.roomNumber || '-' }}-{{ recordData.bedNumber || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">楼栋信息：</span>
            <span class="value">{{ recordData.buildingName || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">楼层层数：</span>
            <span class="value">{{ recordData.floorNumber || '-' }}层</span>
          </div>
        </div>
        <div class="info-right">
          <el-avatar shape="square" :size="100" :src="recordData.avatar" ></el-avatar>
        </div>
      </div>
      <div class="title_room">
        <span class="label">查房信息-{{ recordData.roundDate || '-' }}</span>
      </div>
      <!-- table区域 -->
      <div class="attachment-area">
         <el-table :data="recordData.visits" style="width: 100%" border>
          <el-table-column prop="roundCount" label="查房次数" width="80" align="center"></el-table-column>
          <el-table-column prop="roundTime" label="查房时间" min-width="100" align="center">
            <template #default="{row}">
              {{row.roundTime? row.roundTime[0]+'~'+ row.roundTime[1] : '-'  }}
            </template>
          </el-table-column>
          <el-table-column prop="roundName" label="查房人" min-width="100" align="center"></el-table-column>
          <el-table-column prop="roundContent" label="查房内容" min-width="180" align="center"></el-table-column>
         </el-table>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">返回</el-button>
        <el-button type="primary" @click="handlePrint">打印</el-button>
      </span>
    </template>
  </el-dialog>
    <!-- 打印预览弹框 -->
  <print-preview ref="prientRef"/>
</div>
</template>

<script setup>
import { ref } from 'vue'
import PrintPreview from './PrintPreview.vue'
import {getNurseCheckHuLiListHistoryDetail} from '@/api/nurseworkstation/index'
const prientRef = ref(null)
// 弹窗显示控制
const dialogVisible = ref(false)
const loading = ref(false)
const recordDataList = ref([])
// 打印预览弹窗控制
const printDialogVisible = ref(false)

// 记录数据
const recordData = ref(null)

// 打开弹窗并设置数据
const openDialog = (data) => {
  console.log('打印预览弹窗数据', data)
  loading.value = true
  getNurseCheckHuLiListHistoryDetail(data.id).then(res=>{
      recordData.value = res.data || []
      dialogVisible.value = true
  }).finally(()=>{
      loading.value = false
  })
 
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  recordData.value = null
}

// 打印功能
const handlePrint = () => {
  prientRef.value.openPrintDialog(recordData.value)
}



// 暴露方法和属性
defineExpose({
  openDialog
})
</script>

<style scoped>
.roomInfo_title{
  background: rgba(50, 109, 254, 1);
  height: 35px;
  line-height: 35px;
  color: #fff;
  padding-left: 10px;
  font-size: 14px;
  margin-bottom: 10px;
}
.room-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
  padding: 20px;
  /* background-color: #f5f7fa; */
  border-radius: 4px;
}
.title_room{
  font-weight: bold;
    font-size: 16px;
    margin-bottom: 16px;
    color: #2c3e50;
    border-bottom: 1px solid #e0e7ef;
    padding-bottom: 8px;
}

.info-left {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
}

.info-right {
  margin-left: 20px;
}

.info-item {
}
.room-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.info-left {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
}

.info-right {
  margin-left: 20px;
  display: flex;
  align-items: center;
}

.info-item {
  margin-bottom: 15px;
  line-height: 24px;
  flex-basis: 50%;
}

.info-item .label {
  font-weight: bold;
  margin-right: 10px;
  color: #606266;
}

.info-item .value {
  color: #333;
}

.visit-info {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.attachment-area {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.attachment-area h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #333;
}

.dialog-footer {
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 20px;
}
</style>