<template>
    <div class="consumables-detail">
    <!-- 返回工作台按钮 -->
    <el-button type="primary" @click="$router.back()">返回工作台</el-button>
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="耗材明细表" name="consumables">  
          <!-- 查询与重置 -->
          <el-form :inline="true" class="query-form" :model="searchForm" ref="searchFormRef1" label-width="100px">
            <el-form-item label="更换日期:" prop="date">
              <el-date-picker v-model="searchForm.date" type="date" placeholder="选择日期" style="width: 200px;"></el-date-picker>
            </el-form-item>
            <el-form-item label="楼栋信息" prop="buildingId">
              <el-select v-model="searchForm.buildingId" placeholder="全部" style="width: 200px;">
                <el-option label="全部" value=""></el-option>                
                <el-option v-for="item in buildingList" :key="item.value" :label="item.buildingName" :value="item.id" /> 
              </el-select>
            </el-form-item>
            <el-form-item label="房间号" prop="roomNumber">
              <el-input v-model="searchForm.roomNumber" placeholder="请输入"  style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item label="老人姓名" prop="elderName">
              <el-input v-model="searchForm.elderName" placeholder="请输入"  style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="searchForm.projectName" placeholder="请输入" style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item label="操作人" prop="operator">
              <el-input v-model="searchForm.projectName" placeholder="请输入" style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
              <el-button @click="handleReset"  icon="Refresh">重置</el-button>
            </el-form-item>
          </el-form>
  
          <!-- 表格 -->
          <el-table :data="tableData1" border style="width: 100%">
            <el-table-column prop="sequence" label="序号" width="80" align="center"></el-table-column>
            <el-table-column prop="serviceDate" label="服务日期" width="120"  align="center"></el-table-column>
            <el-table-column prop="elderName" label="老人姓名" width="120"  align="center"></el-table-column>
            <el-table-column prop="roomNumber" label="房间号" width="80"  align="center"></el-table-column>
            <el-table-column prop="bedNumber" label="床位号" width="80"  align="center"></el-table-column>
            <el-table-column prop="buildingInfo" label="楼栋信息" width="120"  align="center"></el-table-column>
            <el-table-column prop="serviceItem" label="服务项目" width="120"  align="center"></el-table-column>
            <el-table-column prop="quantity" label="数量" width="80"  align="center"></el-table-column>
            <el-table-column prop="price" label="价格" width="80"  align="center"></el-table-column>
            <el-table-column prop="operator" label="操作人" width="120"  align="center"></el-table-column>
            <el-table-column prop="recordTime" label="记录时间" width="140"  align="center"></el-table-column>
            <el-table-column prop="remark" label="备注"  align="center" min-width="120"></el-table-column>
            <el-table-column prop="operator" label="操作" min-width="120" align="center" fixed="right"> 
                <template #default="scope">
                    <el-button link type="primary" @click="handleDetail(scope.row)">详情</el-button>
                    <el-button link type="primary" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>
          </el-table>
          <div class="pagination-container">
          <el-pagination
            background
            v-model:current-page="searchForm.pageNum"
            v-model:page-size="searchForm.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
        </el-tab-pane>
        <el-tab-pane label="耗材费用汇总表" name="consumablesSummary">
          <!-- 耗材费用汇总表内容 -->
          <!-- 查询与重置 -->
          <el-form :inline="true" class="query-form" :model="searchForm" ref="searchFormRef2" label-width="100px">
            <el-form-item label="汇总月份:" prop="date">
              <el-date-picker v-model="searchForm.date" type="date" placeholder="选择日期" style="width: 200px;"></el-date-picker>
            </el-form-item>
            <el-form-item label="楼栋信息" prop="buildingId">
              <el-select v-model="searchForm.buildingId" placeholder="全部" style="width: 200px;">
                <el-option label="全部" value=""></el-option>                
                <el-option v-for="item in buildingList" :key="item.value" :label="item.buildingName" :value="item.id" /> 
              </el-select>
            </el-form-item>
            <el-form-item label="房间号" prop="roomNumber">
              <el-input v-model="searchForm.roomNumber" placeholder="请输入" style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item label="老人姓名" prop="elderName">
              <el-input v-model="searchForm.elderName" placeholder="请输入" style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item label="床位号" prop="bedNumber">
              <el-input v-model="searchForm.bedNumber" placeholder="请输入" style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
              <el-button @click="handleReset"  icon="Refresh">重置</el-button>
            </el-form-item>
          </el-form>
  
          <!-- 表格 -->
          <el-table :data="tableData2" border style="width: 100%">
            <el-table-column prop="sequence" label="序号" width="80" align="center"></el-table-column>
            <el-table-column prop="serviceDate" label="汇总月份" min-width="120" align="center"></el-table-column>
            <el-table-column prop="elderName" label="老人姓名" width="120" align="center"></el-table-column>
            <el-table-column prop="roomNumber" label="房间号" width="80" align="center"></el-table-column>
            <el-table-column prop="bedNumber" label="床位号" width="80" align="center"></el-table-column>
            <el-table-column prop="buildingInfo" label="楼栋信息" min-width="120" align="center"></el-table-column>
            <el-table-column prop="serviceItem" label="服务费用" min-width="120" align="center"></el-table-column>
            <el-table-column prop="operator" label="操作" min-width="120" align="center">
                <template #default="scope">
                    <el-button link type="primary" @click="handleDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
          </el-table>
          <div class="pagination-container">
          <el-pagination
            background
            v-model:current-page="searchForm.pageNum"
            v-model:page-size="searchForm.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
        </el-tab-pane>
      </el-tabs>
      <ReplaceDetail ref="replaceDetailRef"></ReplaceDetail>
      <!-- 耗材明细表详情 -->
      <consumablesDetailSheet ref="consumablesDetailSheetRef"></consumablesDetailSheet>
    </div>
  </template>
  
  <script setup>
  import { ref } from 'vue';
  import {ElMessageBox} from 'element-plus'
  import  ReplaceDetail from './replaceDetail.vue';
  import  consumablesDetailSheet from './consumablesDetailSheet.vue';
  const activeTab = ref('consumables'); // 默认选中“耗材明细表”
  const buildingList = ref([])
  const total = ref(0);
  const consumablesDetailSheetRef = ref(null);
  const searchForm = ref({
    pageNum: 1,
    pageSize: 10,
    date: '',
    name: '',
    projectName: ''
  });
  
  const tableData1 = ref([
    {
      sequence: 1,
      serviceDate: '2025-06-26',
      elderName: '王药师',
      roomNumber: '301',
      bedNumber: '301-01',
      buildingInfo: '和孚楼',
      serviceItem: '尿不湿',
      quantity: 1,
      price: '￥5',
      operator: '王护士',
      recordTime: '2025-06-26 12:00',
      remark: ''
    },
    // 更多默认数据项...
  ]);
  const tableData2 = ref([{
    sequence: 1,
      serviceDate: '2025-06-26',
      elderName: '王药师',
      roomNumber: '301',
      bedNumber: '301-01',
      buildingInfo: '和孚楼',
      serviceItem: '尿不湿',
      quantity: 1,
      price: '￥5',
      operator: '王护士',
      recordTime: '2025-06-26 12:00',
      remark: ''
  }])
  const replaceDetailRef = ref(null);
  const handleQuery = () => {
    console.log('查询:', searchForm.value);
  };
  
  const handleReset = () => {
    searchForm.value = {
    //   date: '',
    //   name: '',
    //   projectName: ''
    };
  };
  const handleDetail = (row) => {
    if (activeTab.value === 'consumablesSummary') {
        replaceDetailRef.value.openDialog(row)
    }else if(activeTab.value === 'consumables') {
      consumablesDetailSheetRef.value.openDialog(row)
    }
}
const handleDelete = (row) => {
  ElMessageBox.confirm('确定删除该耗材明细吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      deleteNurseCheckin(row.id).then(res => {
        if (res.code === 200) {
          ElMessage({
            type: 'success',
            message: '删除成功!'
          });
          handleQuery()
        } else {
          ElMessage({
            type: 'error',
            message: res.msg
          });
        }
      })
    })
}
  // tab 切换事件处理
  const handleTabClick = (tab) => {
    if (tab.name === 'consumables') {
      // 切换到“耗材明细表”时清空查询条件
      handleReset();
    } else if (tab.name === 'consumablesSummary') {
      // 切换到“耗材费用汇总表”时清空查询条件（如果需要）
      handleReset();
    }
  };
  // 分页大小改变事件
    const handleSizeChange = (val) => {
        searchForm.value.pageSize = val
    //    handleQuery()
    }

    // 当前页改变事件
    const handleCurrentChange = (val) => {
        searchForm.value.pageNum = val
        // handleQuery()
    }
  </script>
  
  <style scoped>
  .consumables-detail {
    padding: 20px;
  }
  
  .query-form {
    margin-bottom: 20px;
  }
  </style>