# Javascript Node CircleCI 2.0 configuration file
#
# Check https://circleci.com/docs/2.0/language-javascript/ for more details
#
version: 2

jobs:
  build:
    docker:
      - image: cypress/base:8
        environment:
          ## this enables colors in the output
          TERM: xterm
      
    working_directory: ~/repo

    steps:
      - checkout

      # Download and cache dependencies
      - restore_cache:
          keys:
          - v1-dependencies-{{ checksum "yarn.lock" }}-{{ checksum "docs-src/yarn.lock" }}
          # fallback to using the latest cache if no exact match is found
          - v1-dependencies-

      - run: yarn install && cd docs-src && yarn install

      - save_cache:
          paths:
            - node_modules
            - docs-src/node_modules
          key: v1-dependencies-{{ checksum "yarn.lock" }}-{{ checksum "docs-src/yarn.lock" }}
        
      # run tests!
      - run: yarn test

      - store_artifacts:
          path: docs-src/tests/e2e/videos
      - store_artifacts:
          path: docs-src/tests/e2e/screenshots


