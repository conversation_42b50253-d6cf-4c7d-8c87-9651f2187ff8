{"version": 3, "file": "vue-virtual-scroller.esm.js", "sources": ["../src/config.js", "../src/components/common.js", "../src/utils.js", "../src/components/RecycleScroller.vue", "../src/components/DynamicScroller.vue", "../src/components/DynamicScrollerItem.vue", "../src/mixins/IdState.js", "../src/index.js"], "sourcesContent": ["export default {\n  itemsLimit: 1000,\n}\n", "export const props = {\n  items: {\n    type: Array,\n    required: true,\n  },\n\n  keyField: {\n    type: String,\n    default: 'id',\n  },\n\n  direction: {\n    type: String,\n    default: 'vertical',\n    validator: (value) => ['vertical', 'horizontal'].includes(value),\n  },\n\n  listTag: {\n    type: String,\n    default: 'div',\n  },\n\n  itemTag: {\n    type: String,\n    default: 'div',\n  },\n}\n\nexport function simpleArray () {\n  return this.items.length && typeof this.items[0] !== 'object'\n}\n", "export let supportsPassive = false\n\nif (typeof window !== 'undefined') {\n  supportsPassive = false\n  try {\n    var opts = Object.defineProperty({}, 'passive', {\n      get () {\n        supportsPassive = true\n      },\n    })\n    window.addEventListener('test', null, opts)\n  } catch (e) {}\n}\n", "<template>\n  <div\n    v-observe-visibility=\"handleVisibilityChange\"\n    class=\"vue-recycle-scroller\"\n    :class=\"{\n      ready,\n      'page-mode': pageMode,\n      [`direction-${direction}`]: true,\n    }\"\n    @scroll.passive=\"handleScroll\"\n  >\n    <div\n      v-if=\"$slots.before\"\n      ref=\"before\"\n      class=\"vue-recycle-scroller__slot\"\n    >\n      <slot\n        name=\"before\"\n      />\n    </div>\n\n    <component\n      :is=\"listTag\"\n      ref=\"wrapper\"\n      :style=\"{ [direction === 'vertical' ? 'minHeight' : 'minWidth']: totalSize + 'px' }\"\n      class=\"vue-recycle-scroller__item-wrapper\"\n      :class=\"listClass\"\n    >\n      <component\n        :is=\"itemTag\"\n        v-for=\"view of pool\"\n        :key=\"view.nr.id\"\n        :style=\"ready ? {\n          transform: `translate${direction === 'vertical' ? 'Y' : 'X'}(${view.position}px) translate${direction === 'vertical' ? 'X' : 'Y'}(${view.offset}px)`,\n          width: gridItems ? `${direction === 'vertical' ? itemSecondarySize || itemSize : itemSize}px` : undefined,\n          height: gridItems ? `${direction === 'horizontal' ? itemSecondarySize || itemSize : itemSize}px` : undefined,\n        } : null\"\n        class=\"vue-recycle-scroller__item-view\"\n        :class=\"[\n          itemClass,\n          {\n            hover: !skipHover && hoverKey === view.nr.key\n          },\n        ]\"\n        v-on=\"skipHover ? {} : {\n          mouseenter: () => { hoverKey = view.nr.key },\n          mouseleave: () => { hoverKey = null },\n        }\"\n      >\n        <slot\n          :item=\"view.item\"\n          :index=\"view.nr.index\"\n          :active=\"view.nr.used\"\n        />\n      </component>\n\n      <slot\n        name=\"empty\"\n      />\n    </component>\n\n    <div\n      v-if=\"$slots.after\"\n      ref=\"after\"\n      class=\"vue-recycle-scroller__slot\"\n    >\n      <slot\n        name=\"after\"\n      />\n    </div>\n\n    <ResizeObserver @notify=\"handleResize\" />\n  </div>\n</template>\n\n<script>\nimport { ResizeObserver } from 'vue-resize'\nimport { ObserveVisibility } from 'vue-observe-visibility'\nimport ScrollParent from 'scrollparent'\nimport config from '../config'\nimport { props, simpleArray } from './common'\nimport { supportsPassive } from '../utils'\n\nlet uid = 0\n\nexport default {\n  name: 'RecycleScroller',\n\n  components: {\n    ResizeObserver,\n  },\n\n  directives: {\n    ObserveVisibility,\n  },\n\n  props: {\n    ...props,\n\n    itemSize: {\n      type: Number,\n      default: null,\n    },\n\n    gridItems: {\n      type: Number,\n      default: undefined,\n    },\n\n    itemSecondarySize: {\n      type: Number,\n      default: undefined,\n    },\n\n    minItemSize: {\n      type: [Number, String],\n      default: null,\n    },\n\n    sizeField: {\n      type: String,\n      default: 'size',\n    },\n\n    typeField: {\n      type: String,\n      default: 'type',\n    },\n\n    buffer: {\n      type: Number,\n      default: 200,\n    },\n\n    pageMode: {\n      type: Boolean,\n      default: false,\n    },\n\n    prerender: {\n      type: Number,\n      default: 0,\n    },\n\n    emitUpdate: {\n      type: Boolean,\n      default: false,\n    },\n\n    skipHover: {\n      type: Boolean,\n      default: false,\n    },\n\n    listTag: {\n      type: String,\n      default: 'div',\n    },\n\n    itemTag: {\n      type: String,\n      default: 'div',\n    },\n\n    listClass: {\n      type: [String, Object, Array],\n      default: '',\n    },\n\n    itemClass: {\n      type: [String, Object, Array],\n      default: '',\n    },\n  },\n\n  data () {\n    return {\n      pool: [],\n      totalSize: 0,\n      ready: false,\n      hoverKey: null,\n    }\n  },\n\n  computed: {\n    sizes () {\n      if (this.itemSize === null) {\n        const sizes = {\n          '-1': { accumulator: 0 },\n        }\n        const items = this.items\n        const field = this.sizeField\n        const minItemSize = this.minItemSize\n        let computedMinSize = 10000\n        let accumulator = 0\n        let current\n        for (let i = 0, l = items.length; i < l; i++) {\n          current = items[i][field] || minItemSize\n          if (current < computedMinSize) {\n            computedMinSize = current\n          }\n          accumulator += current\n          sizes[i] = { accumulator, size: current }\n        }\n        // eslint-disable-next-line\n        this.$_computedMinItemSize = computedMinSize\n        return sizes\n      }\n      return []\n    },\n\n    simpleArray,\n  },\n\n  watch: {\n    items () {\n      this.updateVisibleItems(true)\n    },\n\n    pageMode () {\n      this.applyPageMode()\n      this.updateVisibleItems(false)\n    },\n\n    sizes: {\n      handler () {\n        this.updateVisibleItems(false)\n      },\n      deep: true,\n    },\n\n    gridItems () {\n      this.updateVisibleItems(true)\n    },\n\n    itemSecondarySize () {\n      this.updateVisibleItems(true)\n    },\n  },\n\n  created () {\n    this.$_startIndex = 0\n    this.$_endIndex = 0\n    this.$_views = new Map()\n    this.$_unusedViews = new Map()\n    this.$_scrollDirty = false\n    this.$_lastUpdateScrollPosition = 0\n\n    // In SSR mode, we also prerender the same number of item for the first render\n    // to avoir mismatch between server and client templates\n    if (this.prerender) {\n      this.$_prerender = true\n      this.updateVisibleItems(false)\n    }\n\n    if (this.gridItems && !this.itemSize) {\n      console.error('[vue-recycle-scroller] You must provide an itemSize when using gridItems')\n    }\n  },\n\n  mounted () {\n    this.applyPageMode()\n    this.$nextTick(() => {\n      // In SSR mode, render the real number of visible items\n      this.$_prerender = false\n      this.updateVisibleItems(true)\n      this.ready = true\n    })\n  },\n\n  activated () {\n    const lastPosition = this.$_lastUpdateScrollPosition\n    if (typeof lastPosition === 'number') {\n      this.$nextTick(() => {\n        this.scrollToPosition(lastPosition)\n      })\n    }\n  },\n\n  beforeDestroy () {\n    this.removeListeners()\n  },\n\n  methods: {\n    addView (pool, index, item, key, type) {\n      const view = {\n        item,\n        position: 0,\n      }\n      const nonReactive = {\n        id: uid++,\n        index,\n        used: true,\n        key,\n        type,\n      }\n      Object.defineProperty(view, 'nr', {\n        configurable: false,\n        value: nonReactive,\n      })\n      pool.push(view)\n      return view\n    },\n\n    unuseView (view, fake = false) {\n      const unusedViews = this.$_unusedViews\n      const type = view.nr.type\n      let unusedPool = unusedViews.get(type)\n      if (!unusedPool) {\n        unusedPool = []\n        unusedViews.set(type, unusedPool)\n      }\n      unusedPool.push(view)\n      if (!fake) {\n        view.nr.used = false\n        view.position = -9999\n        this.$_views.delete(view.nr.key)\n      }\n    },\n\n    handleResize () {\n      this.$emit('resize')\n      if (this.ready) this.updateVisibleItems(false)\n    },\n\n    handleScroll (event) {\n      if (!this.$_scrollDirty) {\n        this.$_scrollDirty = true\n        requestAnimationFrame(() => {\n          this.$_scrollDirty = false\n          const { continuous } = this.updateVisibleItems(false, true)\n\n          // It seems sometimes chrome doesn't fire scroll event :/\n          // When non continous scrolling is ending, we force a refresh\n          if (!continuous) {\n            clearTimeout(this.$_refreshTimout)\n            this.$_refreshTimout = setTimeout(this.handleScroll, 100)\n          }\n        })\n      }\n    },\n\n    handleVisibilityChange (isVisible, entry) {\n      if (this.ready) {\n        if (isVisible || entry.boundingClientRect.width !== 0 || entry.boundingClientRect.height !== 0) {\n          this.$emit('visible')\n          requestAnimationFrame(() => {\n            this.updateVisibleItems(false)\n          })\n        } else {\n          this.$emit('hidden')\n        }\n      }\n    },\n\n    updateVisibleItems (checkItem, checkPositionDiff = false) {\n      const itemSize = this.itemSize\n      const gridItems = this.gridItems || 1\n      const itemSecondarySize = this.itemSecondarySize || itemSize\n      const minItemSize = this.$_computedMinItemSize\n      const typeField = this.typeField\n      const keyField = this.simpleArray ? null : this.keyField\n      const items = this.items\n      const count = items.length\n      const sizes = this.sizes\n      const views = this.$_views\n      const unusedViews = this.$_unusedViews\n      const pool = this.pool\n      let startIndex, endIndex\n      let totalSize\n      let visibleStartIndex, visibleEndIndex\n\n      if (!count) {\n        startIndex = endIndex = visibleStartIndex = visibleEndIndex = totalSize = 0\n      } else if (this.$_prerender) {\n        startIndex = visibleStartIndex = 0\n        endIndex = visibleEndIndex = Math.min(this.prerender, items.length)\n        totalSize = null\n      } else {\n        const scroll = this.getScroll()\n\n        // Skip update if use hasn't scrolled enough\n        if (checkPositionDiff) {\n          let positionDiff = scroll.start - this.$_lastUpdateScrollPosition\n          if (positionDiff < 0) positionDiff = -positionDiff\n          if ((itemSize === null && positionDiff < minItemSize) || positionDiff < itemSize) {\n            return {\n              continuous: true,\n            }\n          }\n        }\n        this.$_lastUpdateScrollPosition = scroll.start\n\n        const buffer = this.buffer\n        scroll.start -= buffer\n        scroll.end += buffer\n\n        // account for leading slot\n        let beforeSize = 0\n        if (this.$refs.before) {\n          beforeSize = this.$refs.before.scrollHeight\n          scroll.start -= beforeSize\n        }\n\n        // account for trailing slot\n        if (this.$refs.after) {\n          const afterSize = this.$refs.after.scrollHeight\n          scroll.end += afterSize\n        }\n\n        // Variable size mode\n        if (itemSize === null) {\n          let h\n          let a = 0\n          let b = count - 1\n          let i = ~~(count / 2)\n          let oldI\n\n          // Searching for startIndex\n          do {\n            oldI = i\n            h = sizes[i].accumulator\n            if (h < scroll.start) {\n              a = i\n            } else if (i < count - 1 && sizes[i + 1].accumulator > scroll.start) {\n              b = i\n            }\n            i = ~~((a + b) / 2)\n          } while (i !== oldI)\n          i < 0 && (i = 0)\n          startIndex = i\n\n          // For container style\n          totalSize = sizes[count - 1].accumulator\n\n          // Searching for endIndex\n          for (endIndex = i; endIndex < count && sizes[endIndex].accumulator < scroll.end; endIndex++);\n          if (endIndex === -1) {\n            endIndex = items.length - 1\n          } else {\n            endIndex++\n            // Bounds\n            endIndex > count && (endIndex = count)\n          }\n\n          // search visible startIndex\n          for (visibleStartIndex = startIndex; visibleStartIndex < count && (beforeSize + sizes[visibleStartIndex].accumulator) < scroll.start; visibleStartIndex++);\n\n          // search visible endIndex\n          for (visibleEndIndex = visibleStartIndex; visibleEndIndex < count && (beforeSize + sizes[visibleEndIndex].accumulator) < scroll.end; visibleEndIndex++);\n        } else {\n          // Fixed size mode\n          startIndex = ~~(scroll.start / itemSize * gridItems)\n          const remainer = startIndex % gridItems\n          startIndex -= remainer\n          endIndex = Math.ceil(scroll.end / itemSize * gridItems)\n          visibleStartIndex = Math.max(0, Math.floor((scroll.start - beforeSize) / itemSize * gridItems))\n          visibleEndIndex = Math.floor((scroll.end - beforeSize) / itemSize * gridItems)\n\n          // Bounds\n          startIndex < 0 && (startIndex = 0)\n          endIndex > count && (endIndex = count)\n          visibleStartIndex < 0 && (visibleStartIndex = 0)\n          visibleEndIndex > count && (visibleEndIndex = count)\n\n          totalSize = Math.ceil(count / gridItems) * itemSize\n        }\n      }\n\n      if (endIndex - startIndex > config.itemsLimit) {\n        this.itemsLimitError()\n      }\n\n      this.totalSize = totalSize\n\n      let view\n\n      const continuous = startIndex <= this.$_endIndex && endIndex >= this.$_startIndex\n\n      if (this.$_continuous !== continuous) {\n        if (continuous) {\n          views.clear()\n          unusedViews.clear()\n          for (let i = 0, l = pool.length; i < l; i++) {\n            view = pool[i]\n            this.unuseView(view)\n          }\n        }\n        this.$_continuous = continuous\n      } else if (continuous) {\n        for (let i = 0, l = pool.length; i < l; i++) {\n          view = pool[i]\n          if (view.nr.used) {\n            // Update view item index\n            if (checkItem) {\n              view.nr.index = items.indexOf(view.item)\n            }\n\n            // Check if index is still in visible range\n            if (\n              view.nr.index === -1 ||\n              view.nr.index < startIndex ||\n              view.nr.index >= endIndex\n            ) {\n              this.unuseView(view)\n            }\n          }\n        }\n      }\n\n      const unusedIndex = continuous ? null : new Map()\n\n      let item, type, unusedPool\n      let v\n      for (let i = startIndex; i < endIndex; i++) {\n        item = items[i]\n        const key = keyField ? item[keyField] : item\n        if (key == null) {\n          throw new Error(`Key is ${key} on item (keyField is '${keyField}')`)\n        }\n        view = views.get(key)\n\n        if (!itemSize && !sizes[i].size) {\n          if (view) this.unuseView(view)\n          continue\n        }\n\n        // No view assigned to item\n        if (!view) {\n          if (i === items.length - 1) this.$emit('scroll-end')\n          if (i === 0) this.$emit('scroll-start')\n\n          type = item[typeField]\n          unusedPool = unusedViews.get(type)\n\n          if (continuous) {\n            // Reuse existing view\n            if (unusedPool && unusedPool.length) {\n              view = unusedPool.pop()\n              view.item = item\n              view.nr.used = true\n              view.nr.index = i\n              view.nr.key = key\n              view.nr.type = type\n            } else {\n              view = this.addView(pool, i, item, key, type)\n            }\n          } else {\n            // Use existing view\n            // We don't care if they are already used\n            // because we are not in continous scrolling\n            v = unusedIndex.get(type) || 0\n\n            if (!unusedPool || v >= unusedPool.length) {\n              view = this.addView(pool, i, item, key, type)\n              this.unuseView(view, true)\n              unusedPool = unusedViews.get(type)\n            }\n\n            view = unusedPool[v]\n            view.item = item\n            view.nr.used = true\n            view.nr.index = i\n            view.nr.key = key\n            view.nr.type = type\n            unusedIndex.set(type, v + 1)\n            v++\n          }\n          views.set(key, view)\n        } else {\n          view.nr.used = true\n          view.item = item\n        }\n\n        // Update position\n        if (itemSize === null) {\n          view.position = sizes[i - 1].accumulator\n          view.offset = 0\n        } else {\n          view.position = Math.floor(i / gridItems) * itemSize\n          view.offset = (i % gridItems) * itemSecondarySize\n        }\n      }\n\n      this.$_startIndex = startIndex\n      this.$_endIndex = endIndex\n\n      if (this.emitUpdate) this.$emit('update', startIndex, endIndex, visibleStartIndex, visibleEndIndex)\n\n      // After the user has finished scrolling\n      // Sort views so text selection is correct\n      clearTimeout(this.$_sortTimer)\n      this.$_sortTimer = setTimeout(this.sortViews, 300)\n\n      return {\n        continuous,\n      }\n    },\n\n    getListenerTarget () {\n      let target = ScrollParent(this.$el)\n      // Fix global scroll target for Chrome and Safari\n      if (window.document && (target === window.document.documentElement || target === window.document.body)) {\n        target = window\n      }\n      return target\n    },\n\n    getScroll () {\n      const { $el: el, direction } = this\n      const isVertical = direction === 'vertical'\n      let scrollState\n\n      if (this.pageMode) {\n        const bounds = el.getBoundingClientRect()\n        const boundsSize = isVertical ? bounds.height : bounds.width\n        let start = -(isVertical ? bounds.top : bounds.left)\n        let size = isVertical ? window.innerHeight : window.innerWidth\n        if (start < 0) {\n          size += start\n          start = 0\n        }\n        if (start + size > boundsSize) {\n          size = boundsSize - start\n        }\n        scrollState = {\n          start,\n          end: start + size,\n        }\n      } else if (isVertical) {\n        scrollState = {\n          start: el.scrollTop,\n          end: el.scrollTop + el.clientHeight,\n        }\n      } else {\n        scrollState = {\n          start: el.scrollLeft,\n          end: el.scrollLeft + el.clientWidth,\n        }\n      }\n\n      return scrollState\n    },\n\n    applyPageMode () {\n      if (this.pageMode) {\n        this.addListeners()\n      } else {\n        this.removeListeners()\n      }\n    },\n\n    addListeners () {\n      this.listenerTarget = this.getListenerTarget()\n      this.listenerTarget.addEventListener('scroll', this.handleScroll, supportsPassive\n        ? {\n            passive: true,\n          }\n        : false)\n      this.listenerTarget.addEventListener('resize', this.handleResize)\n    },\n\n    removeListeners () {\n      if (!this.listenerTarget) {\n        return\n      }\n\n      this.listenerTarget.removeEventListener('scroll', this.handleScroll)\n      this.listenerTarget.removeEventListener('resize', this.handleResize)\n\n      this.listenerTarget = null\n    },\n\n    scrollToItem (index) {\n      let scroll\n      if (this.itemSize === null) {\n        scroll = index > 0 ? this.sizes[index - 1].accumulator : 0\n      } else {\n        scroll = Math.floor(index / this.gridItems) * this.itemSize\n      }\n      this.scrollToPosition(scroll)\n    },\n\n    scrollToPosition (position) {\n      const direction = this.direction === 'vertical'\n        ? { scroll: 'scrollTop', start: 'top' }\n        : { scroll: 'scrollLeft', start: 'left' }\n\n      let viewport\n      let scrollDirection\n      let scrollDistance\n\n      if (this.pageMode) {\n        const viewportEl = ScrollParent(this.$el)\n        // HTML doesn't overflow like other elements\n        const scrollTop = viewportEl.tagName === 'HTML' ? 0 : viewportEl[direction.scroll]\n        const bounds = viewportEl.getBoundingClientRect()\n\n        const scroller = this.$el.getBoundingClientRect()\n        const scrollerPosition = scroller[direction.start] - bounds[direction.start]\n\n        viewport = viewportEl\n        scrollDirection = direction.scroll\n        scrollDistance = position + scrollTop + scrollerPosition\n      } else {\n        viewport = this.$el\n        scrollDirection = direction.scroll\n        scrollDistance = position\n      }\n\n      viewport[scrollDirection] = scrollDistance\n    },\n\n    itemsLimitError () {\n      setTimeout(() => {\n        console.log('It seems the scroller element isn\\'t scrolling, so it tries to render all the items at once.', 'Scroller:', this.$el)\n        console.log('Make sure the scroller has a fixed height (or width) and \\'overflow-y\\' (or \\'overflow-x\\') set to \\'auto\\' so it can scroll correctly and only render the items visible in the scroll viewport.')\n      })\n      throw new Error('Rendered items limit reached')\n    },\n\n    sortViews () {\n      this.pool.sort((viewA, viewB) => viewA.nr.index - viewB.nr.index)\n    },\n  },\n}\n</script>\n\n<style>\n.vue-recycle-scroller {\n  position: relative;\n}\n\n.vue-recycle-scroller.direction-vertical:not(.page-mode) {\n  overflow-y: auto;\n}\n\n.vue-recycle-scroller.direction-horizontal:not(.page-mode) {\n  overflow-x: auto;\n}\n\n.vue-recycle-scroller.direction-horizontal {\n  display: flex;\n}\n\n.vue-recycle-scroller__slot {\n  flex: auto 0 0;\n}\n\n.vue-recycle-scroller__item-wrapper {\n  flex: 1;\n  box-sizing: border-box;\n  overflow: hidden;\n  position: relative;\n}\n\n.vue-recycle-scroller.ready .vue-recycle-scroller__item-view {\n  position: absolute;\n  top: 0;\n  left: 0;\n  will-change: transform;\n}\n\n.vue-recycle-scroller.direction-vertical .vue-recycle-scroller__item-wrapper {\n  width: 100%;\n}\n\n.vue-recycle-scroller.direction-horizontal .vue-recycle-scroller__item-wrapper {\n  height: 100%;\n}\n\n.vue-recycle-scroller.ready.direction-vertical .vue-recycle-scroller__item-view {\n  width: 100%;\n}\n\n.vue-recycle-scroller.ready.direction-horizontal .vue-recycle-scroller__item-view {\n  height: 100%;\n}\n</style>\n", "<template>\n  <RecycleScroller\n    ref=\"scroller\"\n    :items=\"itemsWithSize\"\n    :min-item-size=\"minItemSize\"\n    :direction=\"direction\"\n    key-field=\"id\"\n    :list-tag=\"listTag\"\n    :item-tag=\"itemTag\"\n    v-bind=\"$attrs\"\n    @resize=\"onScrollerResize\"\n    @visible=\"onScrollerVisible\"\n    v-on=\"listeners\"\n  >\n    <template slot-scope=\"{ item: itemWithSize, index, active }\">\n      <slot\n        v-bind=\"{\n          item: itemWithSize.item,\n          index,\n          active,\n          itemWithSize\n        }\"\n      />\n    </template>\n    <template slot=\"before\">\n      <slot name=\"before\" />\n    </template>\n    <template slot=\"after\">\n      <slot name=\"after\" />\n    </template>\n    <template slot=\"empty\">\n      <slot name=\"empty\" />\n    </template>\n  </RecycleScroller>\n</template>\n\n<script>\nimport RecycleScroller from './RecycleScroller.vue'\nimport { props, simpleArray } from './common'\n\nexport default {\n  name: 'DynamicScroller',\n\n  components: {\n    RecycleScroller,\n  },\n\n  provide () {\n    if (typeof ResizeObserver !== 'undefined') {\n      this.$_resizeObserver = new ResizeObserver(entries => {\n        requestAnimationFrame(() => {\n          if (!Array.isArray(entries)) {\n            return\n          }\n          for (const entry of entries) {\n            if (entry.target) {\n              const event = new CustomEvent(\n                'resize',\n                {\n                  detail: {\n                    contentRect: entry.contentRect,\n                  },\n                },\n              )\n              entry.target.dispatchEvent(event)\n            }\n          }\n        })\n      })\n    }\n\n    return {\n      vscrollData: this.vscrollData,\n      vscrollParent: this,\n      vscrollResizeObserver: this.$_resizeObserver,\n    }\n  },\n\n  inheritAttrs: false,\n\n  props: {\n    ...props,\n\n    minItemSize: {\n      type: [Number, String],\n      required: true,\n    },\n  },\n\n  data () {\n    return {\n      vscrollData: {\n        active: true,\n        sizes: {},\n        validSizes: {},\n        keyField: this.keyField,\n        simpleArray: false,\n      },\n    }\n  },\n\n  computed: {\n    simpleArray,\n\n    itemsWithSize () {\n      const result = []\n      const { items, keyField, simpleArray } = this\n      const sizes = this.vscrollData.sizes\n      const l = items.length\n      for (let i = 0; i < l; i++) {\n        const item = items[i]\n        const id = simpleArray ? i : item[keyField]\n        let size = sizes[id]\n        if (typeof size === 'undefined' && !this.$_undefinedMap[id]) {\n          size = 0\n        }\n        result.push({\n          item,\n          id,\n          size,\n        })\n      }\n      return result\n    },\n\n    listeners () {\n      const listeners = {}\n      for (const key in this.$listeners) {\n        if (key !== 'resize' && key !== 'visible') {\n          listeners[key] = this.$listeners[key]\n        }\n      }\n      return listeners\n    },\n  },\n\n  watch: {\n    items () {\n      this.forceUpdate(false)\n    },\n\n    simpleArray: {\n      handler (value) {\n        this.vscrollData.simpleArray = value\n      },\n      immediate: true,\n    },\n\n    direction (value) {\n      this.forceUpdate(true)\n    },\n\n    itemsWithSize (next, prev) {\n      const scrollTop = this.$el.scrollTop\n\n      // Calculate total diff between prev and next sizes\n      // over current scroll top. Then add it to scrollTop to\n      // avoid jumping the contents that the user is seeing.\n      let prevActiveTop = 0; let activeTop = 0\n      const length = Math.min(next.length, prev.length)\n      for (let i = 0; i < length; i++) {\n        if (prevActiveTop >= scrollTop) {\n          break\n        }\n        prevActiveTop += prev[i].size || this.minItemSize\n        activeTop += next[i].size || this.minItemSize\n      }\n      const offset = activeTop - prevActiveTop\n\n      if (offset === 0) {\n        return\n      }\n\n      this.$el.scrollTop += offset\n    },\n  },\n\n  beforeCreate () {\n    this.$_updates = []\n    this.$_undefinedSizes = 0\n    this.$_undefinedMap = {}\n  },\n\n  activated () {\n    this.vscrollData.active = true\n  },\n\n  deactivated () {\n    this.vscrollData.active = false\n  },\n\n  methods: {\n    onScrollerResize () {\n      const scroller = this.$refs.scroller\n      if (scroller) {\n        this.forceUpdate()\n      }\n      this.$emit('resize')\n    },\n\n    onScrollerVisible () {\n      this.$emit('vscroll:update', { force: false })\n      this.$emit('visible')\n    },\n\n    forceUpdate (clear = true) {\n      if (clear || this.simpleArray) {\n        this.vscrollData.validSizes = {}\n      }\n      this.$emit('vscroll:update', { force: true })\n    },\n\n    scrollToItem (index) {\n      const scroller = this.$refs.scroller\n      if (scroller) scroller.scrollToItem(index)\n    },\n\n    getItemSize (item, index = undefined) {\n      const id = this.simpleArray ? (index != null ? index : this.items.indexOf(item)) : item[this.keyField]\n      return this.vscrollData.sizes[id] || 0\n    },\n\n    scrollToBottom () {\n      if (this.$_scrollingToBottom) return\n      this.$_scrollingToBottom = true\n      const el = this.$el\n      // Item is inserted to the DOM\n      this.$nextTick(() => {\n        el.scrollTop = el.scrollHeight + 5000\n        // Item sizes are computed\n        const cb = () => {\n          el.scrollTop = el.scrollHeight + 5000\n          requestAnimationFrame(() => {\n            el.scrollTop = el.scrollHeight + 5000\n            if (this.$_undefinedSizes === 0) {\n              this.$_scrollingToBottom = false\n            } else {\n              requestAnimationFrame(cb)\n            }\n          })\n        }\n        requestAnimationFrame(cb)\n      })\n    },\n  },\n}\n</script>\n", "<script>\nexport default {\n  name: 'DynamicScrollerItem',\n\n  inject: [\n    'vscrollData',\n    'vscrollParent',\n    'vscrollResizeObserver',\n  ],\n\n  props: {\n    // eslint-disable-next-line vue/require-prop-types\n    item: {\n      required: true,\n    },\n\n    watchData: {\n      type: Boolean,\n      default: false,\n    },\n\n    /**\n     * Indicates if the view is actively used to display an item.\n     */\n    active: {\n      type: Boolean,\n      required: true,\n    },\n\n    index: {\n      type: Number,\n      default: undefined,\n    },\n\n    sizeDependencies: {\n      type: [Array, Object],\n      default: null,\n    },\n\n    emitResize: {\n      type: Boolean,\n      default: false,\n    },\n\n    tag: {\n      type: String,\n      default: 'div',\n    },\n  },\n\n  computed: {\n    id () {\n      if (this.vscrollData.simpleArray) return this.index\n      // eslint-disable-next-line no-prototype-builtins\n      if (this.item.hasOwnProperty(this.vscrollData.keyField)) return this.item[this.vscrollData.keyField]\n      throw new Error(`keyField '${this.vscrollData.keyField}' not found in your item. You should set a valid keyField prop on your Scroller`)\n    },\n\n    size () {\n      return (this.vscrollData.validSizes[this.id] && this.vscrollData.sizes[this.id]) || 0\n    },\n\n    finalActive () {\n      return this.active && this.vscrollData.active\n    },\n  },\n\n  watch: {\n    watchData: 'updateWatchData',\n\n    id () {\n      if (!this.size) {\n        this.onDataUpdate()\n      }\n    },\n\n    finalActive (value) {\n      if (!this.size) {\n        if (value) {\n          if (!this.vscrollParent.$_undefinedMap[this.id]) {\n            this.vscrollParent.$_undefinedSizes++\n            this.vscrollParent.$_undefinedMap[this.id] = true\n          }\n        } else {\n          if (this.vscrollParent.$_undefinedMap[this.id]) {\n            this.vscrollParent.$_undefinedSizes--\n            this.vscrollParent.$_undefinedMap[this.id] = false\n          }\n        }\n      }\n\n      if (this.vscrollResizeObserver) {\n        if (value) {\n          this.observeSize()\n        } else {\n          this.unobserveSize()\n        }\n      } else if (value && this.$_pendingVScrollUpdate === this.id) {\n        this.updateSize()\n      }\n    },\n  },\n\n  created () {\n    if (this.$isServer) return\n\n    this.$_forceNextVScrollUpdate = null\n    this.updateWatchData()\n\n    if (!this.vscrollResizeObserver) {\n      for (const k in this.sizeDependencies) {\n        this.$watch(() => this.sizeDependencies[k], this.onDataUpdate)\n      }\n\n      this.vscrollParent.$on('vscroll:update', this.onVscrollUpdate)\n      this.vscrollParent.$on('vscroll:update-size', this.onVscrollUpdateSize)\n    }\n  },\n\n  mounted () {\n    if (this.vscrollData.active) {\n      this.updateSize()\n      this.observeSize()\n    }\n  },\n\n  beforeDestroy () {\n    this.vscrollParent.$off('vscroll:update', this.onVscrollUpdate)\n    this.vscrollParent.$off('vscroll:update-size', this.onVscrollUpdateSize)\n    this.unobserveSize()\n  },\n\n  methods: {\n    updateSize () {\n      if (this.finalActive) {\n        if (this.$_pendingSizeUpdate !== this.id) {\n          this.$_pendingSizeUpdate = this.id\n          this.$_forceNextVScrollUpdate = null\n          this.$_pendingVScrollUpdate = null\n          this.computeSize(this.id)\n        }\n      } else {\n        this.$_forceNextVScrollUpdate = this.id\n      }\n    },\n\n    updateWatchData () {\n      if (this.watchData && !this.vscrollResizeObserver) {\n        this.$_watchData = this.$watch('item', () => {\n          this.onDataUpdate()\n        }, {\n          deep: true,\n        })\n      } else if (this.$_watchData) {\n        this.$_watchData()\n        this.$_watchData = null\n      }\n    },\n\n    onVscrollUpdate ({ force }) {\n      // If not active, sechedule a size update when it becomes active\n      if (!this.finalActive && force) {\n        this.$_pendingVScrollUpdate = this.id\n      }\n\n      if (this.$_forceNextVScrollUpdate === this.id || force || !this.size) {\n        this.updateSize()\n      }\n    },\n\n    onDataUpdate () {\n      this.updateSize()\n    },\n\n    computeSize (id) {\n      this.$nextTick(() => {\n        if (this.id === id) {\n          const width = this.$el.offsetWidth\n          const height = this.$el.offsetHeight\n          this.applySize(width, height)\n        }\n        this.$_pendingSizeUpdate = null\n      })\n    },\n\n    applySize (width, height) {\n      const size = ~~(this.vscrollParent.direction === 'vertical' ? height : width)\n      if (size && this.size !== size) {\n        if (this.vscrollParent.$_undefinedMap[this.id]) {\n          this.vscrollParent.$_undefinedSizes--\n          this.vscrollParent.$_undefinedMap[this.id] = undefined\n        }\n        this.$set(this.vscrollData.sizes, this.id, size)\n        this.$set(this.vscrollData.validSizes, this.id, true)\n        if (this.emitResize) this.$emit('resize', this.id)\n      }\n    },\n\n    observeSize () {\n      if (!this.vscrollResizeObserver || !this.$el.parentNode) return\n      this.vscrollResizeObserver.observe(this.$el.parentNode)\n      this.$el.parentNode.addEventListener('resize', this.onResize)\n    },\n\n    unobserveSize () {\n      if (!this.vscrollResizeObserver) return\n      this.vscrollResizeObserver.unobserve(this.$el.parentNode)\n      this.$el.parentNode.removeEventListener('resize', this.onResize)\n    },\n\n    onResize (event) {\n      const { width, height } = event.detail.contentRect\n      this.applySize(width, height)\n    },\n  },\n\n  render (h) {\n    return h(this.tag, this.$slots.default)\n  },\n}\n</script>\n", "import Vue from 'vue'\n\nexport default function ({\n  idProp = vm => vm.item.id,\n} = {}) {\n  const store = {}\n  const vm = new Vue({\n    data () {\n      return {\n        store,\n      }\n    },\n  })\n\n  // @vue/component\n  return {\n    data () {\n      return {\n        idState: null,\n      }\n    },\n\n    created () {\n      this.$_id = null\n      if (typeof idProp === 'function') {\n        this.$_getId = () => idProp.call(this, this)\n      } else {\n        this.$_getId = () => this[idProp]\n      }\n      this.$watch(this.$_getId, {\n        handler (value) {\n          this.$nextTick(() => {\n            this.$_id = value\n          })\n        },\n        immediate: true,\n      })\n      this.$_updateIdState()\n    },\n\n    beforeUpdate () {\n      this.$_updateIdState()\n    },\n\n    methods: {\n      /**\n       * Initialize an idState\n       * @param {number|string} id Unique id for the data\n       */\n      $_idStateInit (id) {\n        const factory = this.$options.idState\n        if (typeof factory === 'function') {\n          const data = factory.call(this, this)\n          vm.$set(store, id, data)\n          this.$_id = id\n          return data\n        } else {\n          throw new Error('[mixin IdState] Missing `idState` function on component definition.')\n        }\n      },\n\n      /**\n       * Ensure idState is created and up-to-date\n       */\n      $_updateIdState () {\n        const id = this.$_getId()\n        if (id == null) {\n          console.warn(`No id found for IdState with idProp: '${idProp}'.`)\n        }\n        if (id !== this.$_id) {\n          if (!store[id]) {\n            this.$_idStateInit(id)\n          }\n          this.idState = store[id]\n        }\n      },\n    },\n  }\n}\n", "import config from './config'\n\nimport RecycleScroller from './components/RecycleScroller.vue'\nimport DynamicScroller from './components/DynamicScroller.vue'\nimport DynamicScrollerItem from './components/DynamicScrollerItem.vue'\n\nexport { default as IdState } from './mixins/IdState'\n\nexport {\n  RecycleScroller,\n  DynamicScroller,\n  DynamicScrollerItem,\n}\n\nfunction registerComponents (Vue, prefix) {\n  Vue.component(`${prefix}recycle-scroller`, RecycleScroller)\n  Vue.component(`${prefix}RecycleScroller`, RecycleScroller)\n  Vue.component(`${prefix}dynamic-scroller`, DynamicScroller)\n  Vue.component(`${prefix}DynamicScroller`, DynamicScroller)\n  Vue.component(`${prefix}dynamic-scroller-item`, DynamicScrollerItem)\n  Vue.component(`${prefix}DynamicScrollerItem`, DynamicScrollerItem)\n}\n\nconst plugin = {\n  // eslint-disable-next-line no-undef\n  version: VERSION,\n  install (Vue, options) {\n    const finalOptions = Object.assign({}, {\n      installComponents: true,\n      componentsPrefix: '',\n    }, options)\n\n    for (const key in finalOptions) {\n      if (typeof finalOptions[key] !== 'undefined') {\n        config[key] = finalOptions[key]\n      }\n    }\n\n    if (finalOptions.installComponents) {\n      registerComponents(Vue, finalOptions.componentsPrefix)\n    }\n  },\n}\n\nexport default plugin\n\n// Auto-install\nlet GlobalVue = null\nif (typeof window !== 'undefined') {\n  GlobalVue = window.Vue\n} else if (typeof global !== 'undefined') {\n  GlobalVue = global.Vue\n}\nif (GlobalVue) {\n  GlobalVue.use(plugin)\n}\n"], "names": ["itemsLimit", "props", "items", "type", "Array", "required", "keyField", "String", "default", "direction", "validator", "value", "includes", "listTag", "itemTag", "simpleArray", "length", "supportsPassive", "window", "opts", "Object", "defineProperty", "get", "addEventListener", "e", "uid", "name", "components", "ResizeObserver", "directives", "ObserveVisibility", "itemSize", "Number", "gridItems", "undefined", "itemSecondarySize", "minItemSize", "sizeField", "typeField", "buffer", "pageMode", "Boolean", "prerender", "emitUpdate", "skipHover", "listClass", "itemClass", "data", "pool", "totalSize", "ready", "hoverKey", "computed", "sizes", "accumulator", "field", "computedMinSize", "current", "i", "l", "size", "$_computedMinItemSize", "watch", "updateVisibleItems", "applyPageMode", "handler", "deep", "created", "$_startIndex", "$_endIndex", "$_views", "Map", "$_unusedViews", "$_scrollDirty", "$_lastUpdateScrollPosition", "$_prerender", "console", "error", "mounted", "$nextTick", "activated", "lastPosition", "scrollToPosition", "<PERSON><PERSON><PERSON><PERSON>", "removeListeners", "methods", "add<PERSON><PERSON><PERSON>", "index", "item", "key", "view", "position", "nonReactive", "id", "used", "configurable", "push", "unuseView", "fake", "unusedViews", "nr", "unusedPool", "set", "delete", "handleResize", "$emit", "handleScroll", "event", "requestAnimationFrame", "continuous", "clearTimeout", "$_refreshTimout", "setTimeout", "handleVisibilityChange", "isVisible", "entry", "boundingClientRect", "width", "height", "checkItem", "checkPositionDiff", "count", "views", "startIndex", "endIndex", "visibleStartIndex", "visibleEndIndex", "Math", "min", "scroll", "getScroll", "positionDiff", "start", "end", "beforeSize", "$refs", "before", "scrollHeight", "after", "afterSize", "h", "a", "b", "oldI", "remainer", "ceil", "max", "floor", "config", "itemsLimitError", "$_continuous", "clear", "indexOf", "unusedIndex", "v", "Error", "pop", "offset", "$_sortTimer", "sortViews", "getListenerTarget", "target", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$el", "document", "documentElement", "body", "el", "isVertical", "scrollState", "bounds", "getBoundingClientRect", "boundsSize", "top", "left", "innerHeight", "innerWidth", "scrollTop", "clientHeight", "scrollLeft", "clientWidth", "addListeners", "<PERSON><PERSON><PERSON><PERSON>", "passive", "removeEventListener", "scrollToItem", "viewport", "scrollDirection", "scrollDistance", "viewportEl", "tagName", "scroller", "scrollerPosition", "log", "sort", "viewA", "viewB", "__vue_script__", "script", "RecycleScroller", "provide", "$_resizeObserver", "entries", "isArray", "CustomEvent", "detail", "contentRect", "dispatchEvent", "vscrollData", "vscrollParent", "vscrollResizeObserver", "inheritAttrs", "active", "validSizes", "itemsWithSize", "result", "$_undefinedMap", "listeners", "$listeners", "forceUpdate", "immediate", "next", "prev", "prevActiveTop", "activeTop", "beforeCreate", "$_updates", "$_undefinedSizes", "deactivated", "onScrollerResize", "onScrollerVisible", "force", "getItemSize", "scrollToBottom", "$_scrollingToBottom", "cb", "inject", "watchData", "sizeDependencies", "emitResize", "tag", "hasOwnProperty", "finalActive", "onDataUpdate", "observeSize", "unobserveSize", "$_pendingVScrollUpdate", "updateSize", "$isServer", "$_forceNextVScrollUpdate", "updateWatchData", "k", "$watch", "$on", "onVscrollUpdate", "onVscrollUpdateSize", "$off", "$_pendingSizeUpdate", "computeSize", "$_watchData", "offsetWidth", "offsetHeight", "applySize", "$set", "parentNode", "observe", "onResize", "unobserve", "render", "$slots", "idProp", "vm", "store", "<PERSON><PERSON>", "idState", "$_id", "$_getId", "call", "$_updateIdState", "beforeUpdate", "$_idStateInit", "factory", "$options", "warn", "registerComponents", "prefix", "component", "DynamicScroller", "DynamicScrollerItem", "plugin", "version", "VERSION", "install", "options", "finalOptions", "assign", "installComponents", "componentsPrefix", "GlobalVue", "global", "use"], "mappings": ";;;;;AAAA,aAAe;AACbA,EAAAA,UAAU,EAAE,IAAA;AACd,CAAC;;ACFM,MAAMC,KAAK,GAAG;AACnBC,EAAAA,KAAK,EAAE;AACLC,IAAAA,IAAI,EAAEC,KAAK;AACXC,IAAAA,QAAQ,EAAE,IAAA;GACX;AAEDC,EAAAA,QAAQ,EAAE;AACRH,IAAAA,IAAI,EAAEI,MAAM;AACZC,IAAAA,OAAO,EAAE,IAAA;GACV;AAEDC,EAAAA,SAAS,EAAE;AACTN,IAAAA,IAAI,EAAEI,MAAM;AACZC,IAAAA,OAAO,EAAE,UAAU;IACnBE,SAAS,EAAGC,KAAK,IAAK,CAAC,UAAU,EAAE,YAAY,CAAC,CAACC,QAAQ,CAACD,KAAK,CAAA;GAChE;AAEDE,EAAAA,OAAO,EAAE;AACPV,IAAAA,IAAI,EAAEI,MAAM;AACZC,IAAAA,OAAO,EAAE,KAAA;GACV;AAEDM,EAAAA,OAAO,EAAE;AACPX,IAAAA,IAAI,EAAEI,MAAM;AACZC,IAAAA,OAAO,EAAE,KAAA;AACX,GAAA;AACF,CAAC,CAAA;AAEM,SAASO,WAAW,GAAI;AAC7B,EAAA,OAAO,IAAI,CAACb,KAAK,CAACc,MAAM,IAAI,OAAO,IAAI,CAACd,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAA;AAC/D;;AC9BO,IAAIe,eAAe,GAAG,KAAK,CAAA;AAElC,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;AACjCD,EAAAA,eAAe,GAAG,KAAK,CAAA;EACvB,IAAI;IACF,IAAIE,IAAI,GAAGC,MAAM,CAACC,cAAc,CAAC,EAAE,EAAE,SAAS,EAAE;AAC9CC,MAAAA,GAAG,GAAI;AACLL,QAAAA,eAAe,GAAG,IAAI,CAAA;AACxB,OAAA;AACF,KAAC,CAAC,CAAA;IACFC,MAAM,CAACK,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAEJ,IAAI,CAAC,CAAA;AAC7C,GAAC,CAAC,OAAOK,CAAC,EAAE,EAAC;AACf;;;ACuEA,IAAAC,GAAA,GAAA,CAAA,CAAA;AAEA,eAAA;AACAC,EAAAA,IAAA,EAAA,iBAAA;AAEAC,EAAAA,UAAA,EAAA;AACAC,oBAAAA,gBAAAA;GACA;AAEAC,EAAAA,UAAA,EAAA;AACAC,IAAAA,iBAAAA;GACA;AAEA7B,EAAAA,KAAA,EAAA;AACA,IAAA,GAAAA,KAAA;AAEA8B,IAAAA,QAAA,EAAA;AACA5B,MAAAA,IAAA,EAAA6B,MAAA;AACAxB,MAAAA,OAAA,EAAA,IAAA;KACA;AAEAyB,IAAAA,SAAA,EAAA;AACA9B,MAAAA,IAAA,EAAA6B,MAAA;AACAxB,MAAAA,OAAA,EAAA0B,SAAAA;KACA;AAEAC,IAAAA,iBAAA,EAAA;AACAhC,MAAAA,IAAA,EAAA6B,MAAA;AACAxB,MAAAA,OAAA,EAAA0B,SAAAA;KACA;AAEAE,IAAAA,WAAA,EAAA;AACAjC,MAAAA,IAAA,EAAA,CAAA6B,MAAA,EAAAzB,MAAA,CAAA;AACAC,MAAAA,OAAA,EAAA,IAAA;KACA;AAEA6B,IAAAA,SAAA,EAAA;AACAlC,MAAAA,IAAA,EAAAI,MAAA;AACAC,MAAAA,OAAA,EAAA,MAAA;KACA;AAEA8B,IAAAA,SAAA,EAAA;AACAnC,MAAAA,IAAA,EAAAI,MAAA;AACAC,MAAAA,OAAA,EAAA,MAAA;KACA;AAEA+B,IAAAA,MAAA,EAAA;AACApC,MAAAA,IAAA,EAAA6B,MAAA;AACAxB,MAAAA,OAAA,EAAA,GAAA;KACA;AAEAgC,IAAAA,QAAA,EAAA;AACArC,MAAAA,IAAA,EAAAsC,OAAA;AACAjC,MAAAA,OAAA,EAAA,KAAA;KACA;AAEAkC,IAAAA,SAAA,EAAA;AACAvC,MAAAA,IAAA,EAAA6B,MAAA;AACAxB,MAAAA,OAAA,EAAA,CAAA;KACA;AAEAmC,IAAAA,UAAA,EAAA;AACAxC,MAAAA,IAAA,EAAAsC,OAAA;AACAjC,MAAAA,OAAA,EAAA,KAAA;KACA;AAEAoC,IAAAA,SAAA,EAAA;AACAzC,MAAAA,IAAA,EAAAsC,OAAA;AACAjC,MAAAA,OAAA,EAAA,KAAA;KACA;AAEAK,IAAAA,OAAA,EAAA;AACAV,MAAAA,IAAA,EAAAI,MAAA;AACAC,MAAAA,OAAA,EAAA,KAAA;KACA;AAEAM,IAAAA,OAAA,EAAA;AACAX,MAAAA,IAAA,EAAAI,MAAA;AACAC,MAAAA,OAAA,EAAA,KAAA;KACA;AAEAqC,IAAAA,SAAA,EAAA;AACA1C,MAAAA,IAAA,EAAA,CAAAI,MAAA,EAAAa,MAAA,EAAAhB,KAAA,CAAA;AACAI,MAAAA,OAAA,EAAA,EAAA;KACA;AAEAsC,IAAAA,SAAA,EAAA;AACA3C,MAAAA,IAAA,EAAA,CAAAI,MAAA,EAAAa,MAAA,EAAAhB,KAAA,CAAA;AACAI,MAAAA,OAAA,EAAA,EAAA;AACA,KAAA;GACA;AAEAuC,EAAAA,IAAA,GAAA;IACA,OAAA;AACAC,MAAAA,IAAA,EAAA,EAAA;AACAC,MAAAA,SAAA,EAAA,CAAA;AACAC,MAAAA,KAAA,EAAA,KAAA;AACAC,MAAAA,QAAA,EAAA,IAAA;KACA,CAAA;GACA;AAEAC,EAAAA,QAAA,EAAA;AACAC,IAAAA,KAAA,GAAA;AACA,MAAA,IAAA,IAAA,CAAAtB,QAAA,KAAA,IAAA,EAAA;AACA,QAAA,MAAAsB,KAAA,GAAA;AACA,UAAA,IAAA,EAAA;AAAAC,YAAAA,WAAA,EAAA,CAAA;AAAA,WAAA;SACA,CAAA;AACA,QAAA,MAAApD,KAAA,GAAA,IAAA,CAAAA,KAAA,CAAA;AACA,QAAA,MAAAqD,KAAA,GAAA,IAAA,CAAAlB,SAAA,CAAA;AACA,QAAA,MAAAD,WAAA,GAAA,IAAA,CAAAA,WAAA,CAAA;QACA,IAAAoB,eAAA,GAAA,KAAA,CAAA;QACA,IAAAF,WAAA,GAAA,CAAA,CAAA;AACA,QAAA,IAAAG,OAAA,CAAA;AACA,QAAA,KAAA,IAAAC,CAAA,GAAA,CAAA,EAAAC,CAAA,GAAAzD,KAAA,CAAAc,MAAA,EAAA0C,CAAA,GAAAC,CAAA,EAAAD,CAAA,EAAA,EAAA;UACAD,OAAA,GAAAvD,KAAA,CAAAwD,CAAA,CAAA,CAAAH,KAAA,CAAA,IAAAnB,WAAA,CAAA;UACA,IAAAqB,OAAA,GAAAD,eAAA,EAAA;AACAA,YAAAA,eAAA,GAAAC,OAAA,CAAA;AACA,WAAA;AACAH,UAAAA,WAAA,IAAAG,OAAA,CAAA;UACAJ,KAAA,CAAAK,CAAA,CAAA,GAAA;YAAAJ,WAAA;AAAAM,YAAAA,IAAA,EAAAH,OAAAA;WAAA,CAAA;AACA,SAAA;AACA;QACA,IAAA,CAAAI,qBAAA,GAAAL,eAAA,CAAA;AACA,QAAA,OAAAH,KAAA,CAAA;AACA,OAAA;AACA,MAAA,OAAA,EAAA,CAAA;KACA;AAEAtC,IAAAA,WAAAA;GACA;AAEA+C,EAAAA,KAAA,EAAA;AACA5D,IAAAA,KAAA,GAAA;AACA,MAAA,IAAA,CAAA6D,kBAAA,CAAA,IAAA,CAAA,CAAA;KACA;AAEAvB,IAAAA,QAAA,GAAA;MACA,IAAA,CAAAwB,aAAA,EAAA,CAAA;AACA,MAAA,IAAA,CAAAD,kBAAA,CAAA,KAAA,CAAA,CAAA;KACA;AAEAV,IAAAA,KAAA,EAAA;AACAY,MAAAA,OAAA,GAAA;AACA,QAAA,IAAA,CAAAF,kBAAA,CAAA,KAAA,CAAA,CAAA;OACA;AACAG,MAAAA,IAAA,EAAA,IAAA;KACA;AAEAjC,IAAAA,SAAA,GAAA;AACA,MAAA,IAAA,CAAA8B,kBAAA,CAAA,IAAA,CAAA,CAAA;KACA;AAEA5B,IAAAA,iBAAA,GAAA;AACA,MAAA,IAAA,CAAA4B,kBAAA,CAAA,IAAA,CAAA,CAAA;AACA,KAAA;GACA;AAEAI,EAAAA,OAAA,GAAA;IACA,IAAA,CAAAC,YAAA,GAAA,CAAA,CAAA;IACA,IAAA,CAAAC,UAAA,GAAA,CAAA,CAAA;AACA,IAAA,IAAA,CAAAC,OAAA,GAAA,IAAAC,GAAA,EAAA,CAAA;AACA,IAAA,IAAA,CAAAC,aAAA,GAAA,IAAAD,GAAA,EAAA,CAAA;IACA,IAAA,CAAAE,aAAA,GAAA,KAAA,CAAA;IACA,IAAA,CAAAC,0BAAA,GAAA,CAAA,CAAA;;AAEA;AACA;IACA,IAAA,IAAA,CAAAhC,SAAA,EAAA;MACA,IAAA,CAAAiC,WAAA,GAAA,IAAA,CAAA;AACA,MAAA,IAAA,CAAAZ,kBAAA,CAAA,KAAA,CAAA,CAAA;AACA,KAAA;IAEA,IAAA,IAAA,CAAA9B,SAAA,IAAA,CAAA,IAAA,CAAAF,QAAA,EAAA;AACA6C,MAAAA,OAAA,CAAAC,KAAA,CAAA,0EAAA,CAAA,CAAA;AACA,KAAA;GACA;AAEAC,EAAAA,OAAA,GAAA;IACA,IAAA,CAAAd,aAAA,EAAA,CAAA;IACA,IAAA,CAAAe,SAAA,CAAA,MAAA;AACA;MACA,IAAA,CAAAJ,WAAA,GAAA,KAAA,CAAA;AACA,MAAA,IAAA,CAAAZ,kBAAA,CAAA,IAAA,CAAA,CAAA;MACA,IAAA,CAAAb,KAAA,GAAA,IAAA,CAAA;AACA,KAAA,CAAA,CAAA;GACA;AAEA8B,EAAAA,SAAA,GAAA;AACA,IAAA,MAAAC,YAAA,GAAA,IAAA,CAAAP,0BAAA,CAAA;AACA,IAAA,IAAA,OAAAO,YAAA,KAAA,QAAA,EAAA;MACA,IAAA,CAAAF,SAAA,CAAA,MAAA;AACA,QAAA,IAAA,CAAAG,gBAAA,CAAAD,YAAA,CAAA,CAAA;AACA,OAAA,CAAA,CAAA;AACA,KAAA;GACA;AAEAE,EAAAA,aAAA,GAAA;IACA,IAAA,CAAAC,eAAA,EAAA,CAAA;GACA;AAEAC,EAAAA,OAAA,EAAA;IACAC,OAAA,CAAAtC,IAAA,EAAAuC,KAAA,EAAAC,IAAA,EAAAC,GAAA,EAAAtF,IAAA,EAAA;AACA,MAAA,MAAAuF,IAAA,GAAA;QACAF,IAAA;AACAG,QAAAA,QAAA,EAAA,CAAA;OACA,CAAA;AACA,MAAA,MAAAC,WAAA,GAAA;QACAC,EAAA,EAAApE,GAAA,EAAA;QACA8D,KAAA;AACAO,QAAAA,IAAA,EAAA,IAAA;QACAL,GAAA;AACAtF,QAAAA,IAAAA;OACA,CAAA;AACAiB,MAAAA,MAAA,CAAAC,cAAA,CAAAqE,IAAA,EAAA,IAAA,EAAA;AACAK,QAAAA,YAAA,EAAA,KAAA;AACApF,QAAAA,KAAA,EAAAiF,WAAAA;AACA,OAAA,CAAA,CAAA;AACA5C,MAAAA,IAAA,CAAAgD,IAAA,CAAAN,IAAA,CAAA,CAAA;AACA,MAAA,OAAAA,IAAA,CAAA;KACA;AAEAO,IAAAA,SAAA,CAAAP,IAAA,EAAAQ,IAAA,GAAA,KAAA,EAAA;AACA,MAAA,MAAAC,WAAA,GAAA,IAAA,CAAA3B,aAAA,CAAA;AACA,MAAA,MAAArE,IAAA,GAAAuF,IAAA,CAAAU,EAAA,CAAAjG,IAAA,CAAA;AACA,MAAA,IAAAkG,UAAA,GAAAF,WAAA,CAAA7E,GAAA,CAAAnB,IAAA,CAAA,CAAA;MACA,IAAA,CAAAkG,UAAA,EAAA;AACAA,QAAAA,UAAA,GAAA,EAAA,CAAA;AACAF,QAAAA,WAAA,CAAAG,GAAA,CAAAnG,IAAA,EAAAkG,UAAA,CAAA,CAAA;AACA,OAAA;AACAA,MAAAA,UAAA,CAAAL,IAAA,CAAAN,IAAA,CAAA,CAAA;MACA,IAAA,CAAAQ,IAAA,EAAA;AACAR,QAAAA,IAAA,CAAAU,EAAA,CAAAN,IAAA,GAAA,KAAA,CAAA;AACAJ,QAAAA,IAAA,CAAAC,QAAA,GAAA,CAAA,IAAA,CAAA;QACA,IAAA,CAAArB,OAAA,CAAAiC,MAAA,CAAAb,IAAA,CAAAU,EAAA,CAAAX,GAAA,CAAA,CAAA;AACA,OAAA;KACA;AAEAe,IAAAA,YAAA,GAAA;AACA,MAAA,IAAA,CAAAC,KAAA,CAAA,QAAA,CAAA,CAAA;MACA,IAAA,IAAA,CAAAvD,KAAA,EAAA,IAAA,CAAAa,kBAAA,CAAA,KAAA,CAAA,CAAA;KACA;IAEA2C,YAAA,CAAAC,KAAA,EAAA;AACA,MAAA,IAAA,CAAA,IAAA,CAAAlC,aAAA,EAAA;QACA,IAAA,CAAAA,aAAA,GAAA,IAAA,CAAA;AACAmC,QAAAA,qBAAA,CAAA,MAAA;UACA,IAAA,CAAAnC,aAAA,GAAA,KAAA,CAAA;UACA,MAAA;AAAAoC,YAAAA,UAAAA;WAAA,GAAA,IAAA,CAAA9C,kBAAA,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;;AAEA;AACA;UACA,IAAA,CAAA8C,UAAA,EAAA;AACAC,YAAAA,YAAA,CAAA,IAAA,CAAAC,eAAA,CAAA,CAAA;YACA,IAAA,CAAAA,eAAA,GAAAC,UAAA,CAAA,IAAA,CAAAN,YAAA,EAAA,GAAA,CAAA,CAAA;AACA,WAAA;AACA,SAAA,CAAA,CAAA;AACA,OAAA;KACA;AAEAO,IAAAA,sBAAA,CAAAC,SAAA,EAAAC,KAAA,EAAA;MACA,IAAA,IAAA,CAAAjE,KAAA,EAAA;AACA,QAAA,IAAAgE,SAAA,IAAAC,KAAA,CAAAC,kBAAA,CAAAC,KAAA,KAAA,CAAA,IAAAF,KAAA,CAAAC,kBAAA,CAAAE,MAAA,KAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAAb,KAAA,CAAA,SAAA,CAAA,CAAA;AACAG,UAAAA,qBAAA,CAAA,MAAA;AACA,YAAA,IAAA,CAAA7C,kBAAA,CAAA,KAAA,CAAA,CAAA;AACA,WAAA,CAAA,CAAA;AACA,SAAA,MAAA;AACA,UAAA,IAAA,CAAA0C,KAAA,CAAA,QAAA,CAAA,CAAA;AACA,SAAA;AACA,OAAA;KACA;AAEA1C,IAAAA,kBAAA,CAAAwD,SAAA,EAAAC,iBAAA,GAAA,KAAA,EAAA;AACA,MAAA,MAAAzF,QAAA,GAAA,IAAA,CAAAA,QAAA,CAAA;AACA,MAAA,MAAAE,SAAA,GAAA,IAAA,CAAAA,SAAA,IAAA,CAAA,CAAA;AACA,MAAA,MAAAE,iBAAA,GAAA,IAAA,CAAAA,iBAAA,IAAAJ,QAAA,CAAA;AACA,MAAA,MAAAK,WAAA,GAAA,IAAA,CAAAyB,qBAAA,CAAA;AACA,MAAA,MAAAvB,SAAA,GAAA,IAAA,CAAAA,SAAA,CAAA;MACA,MAAAhC,QAAA,GAAA,IAAA,CAAAS,WAAA,GAAA,IAAA,GAAA,IAAA,CAAAT,QAAA,CAAA;AACA,MAAA,MAAAJ,KAAA,GAAA,IAAA,CAAAA,KAAA,CAAA;AACA,MAAA,MAAAuH,KAAA,GAAAvH,KAAA,CAAAc,MAAA,CAAA;AACA,MAAA,MAAAqC,KAAA,GAAA,IAAA,CAAAA,KAAA,CAAA;AACA,MAAA,MAAAqE,KAAA,GAAA,IAAA,CAAApD,OAAA,CAAA;AACA,MAAA,MAAA6B,WAAA,GAAA,IAAA,CAAA3B,aAAA,CAAA;AACA,MAAA,MAAAxB,IAAA,GAAA,IAAA,CAAAA,IAAA,CAAA;MACA,IAAA2E,UAAA,EAAAC,QAAA,CAAA;AACA,MAAA,IAAA3E,SAAA,CAAA;MACA,IAAA4E,iBAAA,EAAAC,eAAA,CAAA;MAEA,IAAA,CAAAL,KAAA,EAAA;QACAE,UAAA,GAAAC,QAAA,GAAAC,iBAAA,GAAAC,eAAA,GAAA7E,SAAA,GAAA,CAAA,CAAA;AACA,OAAA,MAAA,IAAA,IAAA,CAAA0B,WAAA,EAAA;QACAgD,UAAA,GAAAE,iBAAA,GAAA,CAAA,CAAA;AACAD,QAAAA,QAAA,GAAAE,eAAA,GAAAC,IAAA,CAAAC,GAAA,CAAA,IAAA,CAAAtF,SAAA,EAAAxC,KAAA,CAAAc,MAAA,CAAA,CAAA;AACAiC,QAAAA,SAAA,GAAA,IAAA,CAAA;AACA,OAAA,MAAA;AACA,QAAA,MAAAgF,MAAA,GAAA,IAAA,CAAAC,SAAA,EAAA,CAAA;;AAEA;AACA,QAAA,IAAAV,iBAAA,EAAA;UACA,IAAAW,YAAA,GAAAF,MAAA,CAAAG,KAAA,GAAA,IAAA,CAAA1D,0BAAA,CAAA;AACA,UAAA,IAAAyD,YAAA,GAAA,CAAA,EAAAA,YAAA,GAAA,CAAAA,YAAA,CAAA;UACA,IAAApG,QAAA,KAAA,IAAA,IAAAoG,YAAA,GAAA/F,WAAA,IAAA+F,YAAA,GAAApG,QAAA,EAAA;YACA,OAAA;AACA8E,cAAAA,UAAA,EAAA,IAAA;aACA,CAAA;AACA,WAAA;AACA,SAAA;AACA,QAAA,IAAA,CAAAnC,0BAAA,GAAAuD,MAAA,CAAAG,KAAA,CAAA;AAEA,QAAA,MAAA7F,MAAA,GAAA,IAAA,CAAAA,MAAA,CAAA;QACA0F,MAAA,CAAAG,KAAA,IAAA7F,MAAA,CAAA;QACA0F,MAAA,CAAAI,GAAA,IAAA9F,MAAA,CAAA;;AAEA;QACA,IAAA+F,UAAA,GAAA,CAAA,CAAA;AACA,QAAA,IAAA,IAAA,CAAAC,KAAA,CAAAC,MAAA,EAAA;AACAF,UAAAA,UAAA,GAAA,IAAA,CAAAC,KAAA,CAAAC,MAAA,CAAAC,YAAA,CAAA;UACAR,MAAA,CAAAG,KAAA,IAAAE,UAAA,CAAA;AACA,SAAA;;AAEA;AACA,QAAA,IAAA,IAAA,CAAAC,KAAA,CAAAG,KAAA,EAAA;UACA,MAAAC,SAAA,GAAA,IAAA,CAAAJ,KAAA,CAAAG,KAAA,CAAAD,YAAA,CAAA;UACAR,MAAA,CAAAI,GAAA,IAAAM,SAAA,CAAA;AACA,SAAA;;AAEA;QACA,IAAA5G,QAAA,KAAA,IAAA,EAAA;AACA,UAAA,IAAA6G,CAAA,CAAA;UACA,IAAAC,CAAA,GAAA,CAAA,CAAA;AACA,UAAA,IAAAC,CAAA,GAAArB,KAAA,GAAA,CAAA,CAAA;AACA,UAAA,IAAA/D,CAAA,GAAA,CAAA,EAAA+D,KAAA,GAAA,CAAA,CAAA,CAAA;AACA,UAAA,IAAAsB,IAAA,CAAA;;AAEA;UACA,GAAA;AACAA,YAAAA,IAAA,GAAArF,CAAA,CAAA;AACAkF,YAAAA,CAAA,GAAAvF,KAAA,CAAAK,CAAA,CAAA,CAAAJ,WAAA,CAAA;AACA,YAAA,IAAAsF,CAAA,GAAAX,MAAA,CAAAG,KAAA,EAAA;AACAS,cAAAA,CAAA,GAAAnF,CAAA,CAAA;aACA,MAAA,IAAAA,CAAA,GAAA+D,KAAA,GAAA,CAAA,IAAApE,KAAA,CAAAK,CAAA,GAAA,CAAA,CAAA,CAAAJ,WAAA,GAAA2E,MAAA,CAAAG,KAAA,EAAA;AACAU,cAAAA,CAAA,GAAApF,CAAA,CAAA;AACA,aAAA;YACAA,CAAA,GAAA,CAAA,EAAA,CAAAmF,CAAA,GAAAC,CAAA,IAAA,CAAA,CAAA,CAAA;WACA,QAAApF,CAAA,KAAAqF,IAAA,EAAA;AACArF,UAAAA,CAAA,GAAA,CAAA,KAAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACAiE,UAAAA,UAAA,GAAAjE,CAAA,CAAA;;AAEA;UACAT,SAAA,GAAAI,KAAA,CAAAoE,KAAA,GAAA,CAAA,CAAA,CAAAnE,WAAA,CAAA;;AAEA;UACA,KAAAsE,QAAA,GAAAlE,CAAA,EAAAkE,QAAA,GAAAH,KAAA,IAAApE,KAAA,CAAAuE,QAAA,CAAA,CAAAtE,WAAA,GAAA2E,MAAA,CAAAI,GAAA,EAAAT,QAAA,EAAA,CAAA,CAAA;AACA,UAAA,IAAAA,QAAA,KAAA,CAAA,CAAA,EAAA;AACAA,YAAAA,QAAA,GAAA1H,KAAA,CAAAc,MAAA,GAAA,CAAA,CAAA;AACA,WAAA,MAAA;AACA4G,YAAAA,QAAA,EAAA,CAAA;AACA;AACAA,YAAAA,QAAA,GAAAH,KAAA,KAAAG,QAAA,GAAAH,KAAA,CAAA,CAAA;AACA,WAAA;;AAEA;UACA,KAAAI,iBAAA,GAAAF,UAAA,EAAAE,iBAAA,GAAAJ,KAAA,IAAAa,UAAA,GAAAjF,KAAA,CAAAwE,iBAAA,CAAA,CAAAvE,WAAA,GAAA2E,MAAA,CAAAG,KAAA,EAAAP,iBAAA,EAAA,CAAA,CAAA;;AAEA;UACA,KAAAC,eAAA,GAAAD,iBAAA,EAAAC,eAAA,GAAAL,KAAA,IAAAa,UAAA,GAAAjF,KAAA,CAAAyE,eAAA,CAAA,CAAAxE,WAAA,GAAA2E,MAAA,CAAAI,GAAA,EAAAP,eAAA,EAAA,CAAA,CAAA;AACA,SAAA,MAAA;AACA;UACAH,UAAA,GAAA,CAAA,EAAAM,MAAA,CAAAG,KAAA,GAAArG,QAAA,GAAAE,SAAA,CAAA,CAAA;AACA,UAAA,MAAA+G,QAAA,GAAArB,UAAA,GAAA1F,SAAA,CAAA;AACA0F,UAAAA,UAAA,IAAAqB,QAAA,CAAA;AACApB,UAAAA,QAAA,GAAAG,IAAA,CAAAkB,IAAA,CAAAhB,MAAA,CAAAI,GAAA,GAAAtG,QAAA,GAAAE,SAAA,CAAA,CAAA;UACA4F,iBAAA,GAAAE,IAAA,CAAAmB,GAAA,CAAA,CAAA,EAAAnB,IAAA,CAAAoB,KAAA,CAAA,CAAAlB,MAAA,CAAAG,KAAA,GAAAE,UAAA,IAAAvG,QAAA,GAAAE,SAAA,CAAA,CAAA,CAAA;AACA6F,UAAAA,eAAA,GAAAC,IAAA,CAAAoB,KAAA,CAAA,CAAAlB,MAAA,CAAAI,GAAA,GAAAC,UAAA,IAAAvG,QAAA,GAAAE,SAAA,CAAA,CAAA;;AAEA;AACA0F,UAAAA,UAAA,GAAA,CAAA,KAAAA,UAAA,GAAA,CAAA,CAAA,CAAA;AACAC,UAAAA,QAAA,GAAAH,KAAA,KAAAG,QAAA,GAAAH,KAAA,CAAA,CAAA;AACAI,UAAAA,iBAAA,GAAA,CAAA,KAAAA,iBAAA,GAAA,CAAA,CAAA,CAAA;AACAC,UAAAA,eAAA,GAAAL,KAAA,KAAAK,eAAA,GAAAL,KAAA,CAAA,CAAA;UAEAxE,SAAA,GAAA8E,IAAA,CAAAkB,IAAA,CAAAxB,KAAA,GAAAxF,SAAA,CAAA,GAAAF,QAAA,CAAA;AACA,SAAA;AACA,OAAA;AAEA,MAAA,IAAA6F,QAAA,GAAAD,UAAA,GAAAyB,MAAA,CAAApJ,UAAA,EAAA;QACA,IAAA,CAAAqJ,eAAA,EAAA,CAAA;AACA,OAAA;MAEA,IAAA,CAAApG,SAAA,GAAAA,SAAA,CAAA;AAEA,MAAA,IAAAyC,IAAA,CAAA;AAEA,MAAA,MAAAmB,UAAA,GAAAc,UAAA,IAAA,IAAA,CAAAtD,UAAA,IAAAuD,QAAA,IAAA,IAAA,CAAAxD,YAAA,CAAA;AAEA,MAAA,IAAA,IAAA,CAAAkF,YAAA,KAAAzC,UAAA,EAAA;AACA,QAAA,IAAAA,UAAA,EAAA;UACAa,KAAA,CAAA6B,KAAA,EAAA,CAAA;UACApD,WAAA,CAAAoD,KAAA,EAAA,CAAA;AACA,UAAA,KAAA,IAAA7F,CAAA,GAAA,CAAA,EAAAC,CAAA,GAAAX,IAAA,CAAAhC,MAAA,EAAA0C,CAAA,GAAAC,CAAA,EAAAD,CAAA,EAAA,EAAA;AACAgC,YAAAA,IAAA,GAAA1C,IAAA,CAAAU,CAAA,CAAA,CAAA;AACA,YAAA,IAAA,CAAAuC,SAAA,CAAAP,IAAA,CAAA,CAAA;AACA,WAAA;AACA,SAAA;QACA,IAAA,CAAA4D,YAAA,GAAAzC,UAAA,CAAA;OACA,MAAA,IAAAA,UAAA,EAAA;AACA,QAAA,KAAA,IAAAnD,CAAA,GAAA,CAAA,EAAAC,CAAA,GAAAX,IAAA,CAAAhC,MAAA,EAAA0C,CAAA,GAAAC,CAAA,EAAAD,CAAA,EAAA,EAAA;AACAgC,UAAAA,IAAA,GAAA1C,IAAA,CAAAU,CAAA,CAAA,CAAA;AACA,UAAA,IAAAgC,IAAA,CAAAU,EAAA,CAAAN,IAAA,EAAA;AACA;AACA,YAAA,IAAAyB,SAAA,EAAA;AACA7B,cAAAA,IAAA,CAAAU,EAAA,CAAAb,KAAA,GAAArF,KAAA,CAAAsJ,OAAA,CAAA9D,IAAA,CAAAF,IAAA,CAAA,CAAA;AACA,aAAA;;AAEA;YACA,IACAE,IAAA,CAAAU,EAAA,CAAAb,KAAA,KAAA,CAAA,CAAA,IACAG,IAAA,CAAAU,EAAA,CAAAb,KAAA,GAAAoC,UAAA,IACAjC,IAAA,CAAAU,EAAA,CAAAb,KAAA,IAAAqC,QAAA,EACA;AACA,cAAA,IAAA,CAAA3B,SAAA,CAAAP,IAAA,CAAA,CAAA;AACA,aAAA;AACA,WAAA;AACA,SAAA;AACA,OAAA;MAEA,MAAA+D,WAAA,GAAA5C,UAAA,GAAA,IAAA,GAAA,IAAAtC,GAAA,EAAA,CAAA;AAEA,MAAA,IAAAiB,IAAA,EAAArF,IAAA,EAAAkG,UAAA,CAAA;AACA,MAAA,IAAAqD,CAAA,CAAA;MACA,KAAA,IAAAhG,CAAA,GAAAiE,UAAA,EAAAjE,CAAA,GAAAkE,QAAA,EAAAlE,CAAA,EAAA,EAAA;AACA8B,QAAAA,IAAA,GAAAtF,KAAA,CAAAwD,CAAA,CAAA,CAAA;QACA,MAAA+B,GAAA,GAAAnF,QAAA,GAAAkF,IAAA,CAAAlF,QAAA,CAAA,GAAAkF,IAAA,CAAA;QACA,IAAAC,GAAA,IAAA,IAAA,EAAA;UACA,MAAA,IAAAkE,KAAA,CAAA,CAAA,OAAA,EAAAlE,GAAA,CAAAnF,uBAAAA,EAAAA,QAAA,IAAA,CAAA,CAAA;AACA,SAAA;AACAoF,QAAAA,IAAA,GAAAgC,KAAA,CAAApG,GAAA,CAAAmE,GAAA,CAAA,CAAA;QAEA,IAAA,CAAA1D,QAAA,IAAA,CAAAsB,KAAA,CAAAK,CAAA,CAAA,CAAAE,IAAA,EAAA;AACA,UAAA,IAAA8B,IAAA,EAAA,IAAA,CAAAO,SAAA,CAAAP,IAAA,CAAA,CAAA;AACA,UAAA,SAAA;AACA,SAAA;;AAEA;QACA,IAAA,CAAAA,IAAA,EAAA;AACA,UAAA,IAAAhC,CAAA,KAAAxD,KAAA,CAAAc,MAAA,GAAA,CAAA,EAAA,IAAA,CAAAyF,KAAA,CAAA,YAAA,CAAA,CAAA;UACA,IAAA/C,CAAA,KAAA,CAAA,EAAA,IAAA,CAAA+C,KAAA,CAAA,cAAA,CAAA,CAAA;AAEAtG,UAAAA,IAAA,GAAAqF,IAAA,CAAAlD,SAAA,CAAA,CAAA;AACA+D,UAAAA,UAAA,GAAAF,WAAA,CAAA7E,GAAA,CAAAnB,IAAA,CAAA,CAAA;AAEA,UAAA,IAAA0G,UAAA,EAAA;AACA;AACA,YAAA,IAAAR,UAAA,IAAAA,UAAA,CAAArF,MAAA,EAAA;AACA0E,cAAAA,IAAA,GAAAW,UAAA,CAAAuD,GAAA,EAAA,CAAA;cACAlE,IAAA,CAAAF,IAAA,GAAAA,IAAA,CAAA;AACAE,cAAAA,IAAA,CAAAU,EAAA,CAAAN,IAAA,GAAA,IAAA,CAAA;AACAJ,cAAAA,IAAA,CAAAU,EAAA,CAAAb,KAAA,GAAA7B,CAAA,CAAA;AACAgC,cAAAA,IAAA,CAAAU,EAAA,CAAAX,GAAA,GAAAA,GAAA,CAAA;AACAC,cAAAA,IAAA,CAAAU,EAAA,CAAAjG,IAAA,GAAAA,IAAA,CAAA;AACA,aAAA,MAAA;AACAuF,cAAAA,IAAA,GAAA,IAAA,CAAAJ,OAAA,CAAAtC,IAAA,EAAAU,CAAA,EAAA8B,IAAA,EAAAC,GAAA,EAAAtF,IAAA,CAAA,CAAA;AACA,aAAA;AACA,WAAA,MAAA;AACA;AACA;AACA;YACAuJ,CAAA,GAAAD,WAAA,CAAAnI,GAAA,CAAAnB,IAAA,CAAA,IAAA,CAAA,CAAA;YAEA,IAAA,CAAAkG,UAAA,IAAAqD,CAAA,IAAArD,UAAA,CAAArF,MAAA,EAAA;AACA0E,cAAAA,IAAA,GAAA,IAAA,CAAAJ,OAAA,CAAAtC,IAAA,EAAAU,CAAA,EAAA8B,IAAA,EAAAC,GAAA,EAAAtF,IAAA,CAAA,CAAA;AACA,cAAA,IAAA,CAAA8F,SAAA,CAAAP,IAAA,EAAA,IAAA,CAAA,CAAA;AACAW,cAAAA,UAAA,GAAAF,WAAA,CAAA7E,GAAA,CAAAnB,IAAA,CAAA,CAAA;AACA,aAAA;AAEAuF,YAAAA,IAAA,GAAAW,UAAA,CAAAqD,CAAA,CAAA,CAAA;YACAhE,IAAA,CAAAF,IAAA,GAAAA,IAAA,CAAA;AACAE,YAAAA,IAAA,CAAAU,EAAA,CAAAN,IAAA,GAAA,IAAA,CAAA;AACAJ,YAAAA,IAAA,CAAAU,EAAA,CAAAb,KAAA,GAAA7B,CAAA,CAAA;AACAgC,YAAAA,IAAA,CAAAU,EAAA,CAAAX,GAAA,GAAAA,GAAA,CAAA;AACAC,YAAAA,IAAA,CAAAU,EAAA,CAAAjG,IAAA,GAAAA,IAAA,CAAA;YACAsJ,WAAA,CAAAnD,GAAA,CAAAnG,IAAA,EAAAuJ,CAAA,GAAA,CAAA,CAAA,CAAA;AACAA,YAAAA,CAAA,EAAA,CAAA;AACA,WAAA;AACAhC,UAAAA,KAAA,CAAApB,GAAA,CAAAb,GAAA,EAAAC,IAAA,CAAA,CAAA;AACA,SAAA,MAAA;AACAA,UAAAA,IAAA,CAAAU,EAAA,CAAAN,IAAA,GAAA,IAAA,CAAA;UACAJ,IAAA,CAAAF,IAAA,GAAAA,IAAA,CAAA;AACA,SAAA;;AAEA;QACA,IAAAzD,QAAA,KAAA,IAAA,EAAA;UACA2D,IAAA,CAAAC,QAAA,GAAAtC,KAAA,CAAAK,CAAA,GAAA,CAAA,CAAA,CAAAJ,WAAA,CAAA;UACAoC,IAAA,CAAAmE,MAAA,GAAA,CAAA,CAAA;AACA,SAAA,MAAA;AACAnE,UAAAA,IAAA,CAAAC,QAAA,GAAAoC,IAAA,CAAAoB,KAAA,CAAAzF,CAAA,GAAAzB,SAAA,CAAA,GAAAF,QAAA,CAAA;AACA2D,UAAAA,IAAA,CAAAmE,MAAA,GAAAnG,CAAA,GAAAzB,SAAA,GAAAE,iBAAA,CAAA;AACA,SAAA;AACA,OAAA;MAEA,IAAA,CAAAiC,YAAA,GAAAuD,UAAA,CAAA;MACA,IAAA,CAAAtD,UAAA,GAAAuD,QAAA,CAAA;AAEA,MAAA,IAAA,IAAA,CAAAjF,UAAA,EAAA,IAAA,CAAA8D,KAAA,CAAA,QAAA,EAAAkB,UAAA,EAAAC,QAAA,EAAAC,iBAAA,EAAAC,eAAA,CAAA,CAAA;;AAEA;AACA;AACAhB,MAAAA,YAAA,CAAA,IAAA,CAAAgD,WAAA,CAAA,CAAA;MACA,IAAA,CAAAA,WAAA,GAAA9C,UAAA,CAAA,IAAA,CAAA+C,SAAA,EAAA,GAAA,CAAA,CAAA;MAEA,OAAA;AACAlD,QAAAA,UAAAA;OACA,CAAA;KACA;AAEAmD,IAAAA,iBAAA,GAAA;AACA,MAAA,IAAAC,MAAA,GAAAC,YAAA,CAAA,IAAA,CAAAC,GAAA,CAAA,CAAA;AACA;MACA,IAAAjJ,MAAA,CAAAkJ,QAAA,KAAAH,MAAA,KAAA/I,MAAA,CAAAkJ,QAAA,CAAAC,eAAA,IAAAJ,MAAA,KAAA/I,MAAA,CAAAkJ,QAAA,CAAAE,IAAA,CAAA,EAAA;AACAL,QAAAA,MAAA,GAAA/I,MAAA,CAAA;AACA,OAAA;AACA,MAAA,OAAA+I,MAAA,CAAA;KACA;AAEA/B,IAAAA,SAAA,GAAA;MACA,MAAA;AAAAiC,QAAAA,GAAA,EAAAI,EAAA;AAAA9J,QAAAA,SAAAA;AAAA,OAAA,GAAA,IAAA,CAAA;AACA,MAAA,MAAA+J,UAAA,GAAA/J,SAAA,KAAA,UAAA,CAAA;AACA,MAAA,IAAAgK,WAAA,CAAA;MAEA,IAAA,IAAA,CAAAjI,QAAA,EAAA;AACA,QAAA,MAAAkI,MAAA,GAAAH,EAAA,CAAAI,qBAAA,EAAA,CAAA;QACA,MAAAC,UAAA,GAAAJ,UAAA,GAAAE,MAAA,CAAApD,MAAA,GAAAoD,MAAA,CAAArD,KAAA,CAAA;AACA,QAAA,IAAAe,KAAA,GAAA,EAAAoC,UAAA,GAAAE,MAAA,CAAAG,GAAA,GAAAH,MAAA,CAAAI,IAAA,CAAA,CAAA;QACA,IAAAlH,IAAA,GAAA4G,UAAA,GAAAtJ,MAAA,CAAA6J,WAAA,GAAA7J,MAAA,CAAA8J,UAAA,CAAA;QACA,IAAA5C,KAAA,GAAA,CAAA,EAAA;AACAxE,UAAAA,IAAA,IAAAwE,KAAA,CAAA;AACAA,UAAAA,KAAA,GAAA,CAAA,CAAA;AACA,SAAA;AACA,QAAA,IAAAA,KAAA,GAAAxE,IAAA,GAAAgH,UAAA,EAAA;UACAhH,IAAA,GAAAgH,UAAA,GAAAxC,KAAA,CAAA;AACA,SAAA;AACAqC,QAAAA,WAAA,GAAA;UACArC,KAAA;UACAC,GAAA,EAAAD,KAAA,GAAAxE,IAAAA;SACA,CAAA;OACA,MAAA,IAAA4G,UAAA,EAAA;AACAC,QAAAA,WAAA,GAAA;UACArC,KAAA,EAAAmC,EAAA,CAAAU,SAAA;AACA5C,UAAAA,GAAA,EAAAkC,EAAA,CAAAU,SAAA,GAAAV,EAAA,CAAAW,YAAAA;SACA,CAAA;AACA,OAAA,MAAA;AACAT,QAAAA,WAAA,GAAA;UACArC,KAAA,EAAAmC,EAAA,CAAAY,UAAA;AACA9C,UAAAA,GAAA,EAAAkC,EAAA,CAAAY,UAAA,GAAAZ,EAAA,CAAAa,WAAAA;SACA,CAAA;AACA,OAAA;AAEA,MAAA,OAAAX,WAAA,CAAA;KACA;AAEAzG,IAAAA,aAAA,GAAA;MACA,IAAA,IAAA,CAAAxB,QAAA,EAAA;QACA,IAAA,CAAA6I,YAAA,EAAA,CAAA;AACA,OAAA,MAAA;QACA,IAAA,CAAAjG,eAAA,EAAA,CAAA;AACA,OAAA;KACA;AAEAiG,IAAAA,YAAA,GAAA;AACA,MAAA,IAAA,CAAAC,cAAA,GAAA,IAAA,CAAAtB,iBAAA,EAAA,CAAA;AACA,MAAA,IAAA,CAAAsB,cAAA,CAAA/J,gBAAA,CAAA,QAAA,EAAA,IAAA,CAAAmF,YAAA,EAAAzF,eAAA,GACA;AACAsK,QAAAA,OAAA,EAAA,IAAA;OACA,GACA,KAAA,CAAA,CAAA;MACA,IAAA,CAAAD,cAAA,CAAA/J,gBAAA,CAAA,QAAA,EAAA,IAAA,CAAAiF,YAAA,CAAA,CAAA;KACA;AAEApB,IAAAA,eAAA,GAAA;AACA,MAAA,IAAA,CAAA,IAAA,CAAAkG,cAAA,EAAA;AACA,QAAA,OAAA;AACA,OAAA;MAEA,IAAA,CAAAA,cAAA,CAAAE,mBAAA,CAAA,QAAA,EAAA,IAAA,CAAA9E,YAAA,CAAA,CAAA;MACA,IAAA,CAAA4E,cAAA,CAAAE,mBAAA,CAAA,QAAA,EAAA,IAAA,CAAAhF,YAAA,CAAA,CAAA;MAEA,IAAA,CAAA8E,cAAA,GAAA,IAAA,CAAA;KACA;IAEAG,YAAA,CAAAlG,KAAA,EAAA;AACA,MAAA,IAAA0C,MAAA,CAAA;AACA,MAAA,IAAA,IAAA,CAAAlG,QAAA,KAAA,IAAA,EAAA;AACAkG,QAAAA,MAAA,GAAA1C,KAAA,GAAA,CAAA,GAAA,IAAA,CAAAlC,KAAA,CAAAkC,KAAA,GAAA,CAAA,CAAA,CAAAjC,WAAA,GAAA,CAAA,CAAA;AACA,OAAA,MAAA;AACA2E,QAAAA,MAAA,GAAAF,IAAA,CAAAoB,KAAA,CAAA5D,KAAA,GAAA,IAAA,CAAAtD,SAAA,CAAA,GAAA,IAAA,CAAAF,QAAA,CAAA;AACA,OAAA;AACA,MAAA,IAAA,CAAAmD,gBAAA,CAAA+C,MAAA,CAAA,CAAA;KACA;IAEA/C,gBAAA,CAAAS,QAAA,EAAA;AACA,MAAA,MAAAlF,SAAA,GAAA,IAAA,CAAAA,SAAA,KAAA,UAAA,GACA;AAAAwH,QAAAA,MAAA,EAAA,WAAA;AAAAG,QAAAA,KAAA,EAAA,KAAA;AAAA,OAAA,GACA;AAAAH,QAAAA,MAAA,EAAA,YAAA;AAAAG,QAAAA,KAAA,EAAA,MAAA;OAAA,CAAA;AAEA,MAAA,IAAAsD,QAAA,CAAA;AACA,MAAA,IAAAC,eAAA,CAAA;AACA,MAAA,IAAAC,cAAA,CAAA;MAEA,IAAA,IAAA,CAAApJ,QAAA,EAAA;AACA,QAAA,MAAAqJ,UAAA,GAAA3B,YAAA,CAAA,IAAA,CAAAC,GAAA,CAAA,CAAA;AACA;AACA,QAAA,MAAAc,SAAA,GAAAY,UAAA,CAAAC,OAAA,KAAA,MAAA,GAAA,CAAA,GAAAD,UAAA,CAAApL,SAAA,CAAAwH,MAAA,CAAA,CAAA;AACA,QAAA,MAAAyC,MAAA,GAAAmB,UAAA,CAAAlB,qBAAA,EAAA,CAAA;AAEA,QAAA,MAAAoB,QAAA,GAAA,IAAA,CAAA5B,GAAA,CAAAQ,qBAAA,EAAA,CAAA;AACA,QAAA,MAAAqB,gBAAA,GAAAD,QAAA,CAAAtL,SAAA,CAAA2H,KAAA,CAAA,GAAAsC,MAAA,CAAAjK,SAAA,CAAA2H,KAAA,CAAA,CAAA;AAEAsD,QAAAA,QAAA,GAAAG,UAAA,CAAA;QACAF,eAAA,GAAAlL,SAAA,CAAAwH,MAAA,CAAA;AACA2D,QAAAA,cAAA,GAAAjG,QAAA,GAAAsF,SAAA,GAAAe,gBAAA,CAAA;AACA,OAAA,MAAA;QACAN,QAAA,GAAA,IAAA,CAAAvB,GAAA,CAAA;QACAwB,eAAA,GAAAlL,SAAA,CAAAwH,MAAA,CAAA;AACA2D,QAAAA,cAAA,GAAAjG,QAAA,CAAA;AACA,OAAA;AAEA+F,MAAAA,QAAA,CAAAC,eAAA,CAAA,GAAAC,cAAA,CAAA;KACA;AAEAvC,IAAAA,eAAA,GAAA;AACArC,MAAAA,UAAA,CAAA,MAAA;QACApC,OAAA,CAAAqH,GAAA,CAAA,8FAAA,EAAA,WAAA,EAAA,IAAA,CAAA9B,GAAA,CAAA,CAAA;AACAvF,QAAAA,OAAA,CAAAqH,GAAA,CAAA,kMAAA,CAAA,CAAA;AACA,OAAA,CAAA,CAAA;AACA,MAAA,MAAA,IAAAtC,KAAA,CAAA,8BAAA,CAAA,CAAA;KACA;AAEAI,IAAAA,SAAA,GAAA;MACA,IAAA,CAAA/G,IAAA,CAAAkJ,IAAA,CAAA,CAAAC,KAAA,EAAAC,KAAA,KAAAD,KAAA,CAAA/F,EAAA,CAAAb,KAAA,GAAA6G,KAAA,CAAAhG,EAAA,CAAAb,KAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAntBA,MAAc8G,gBAAA,GAAAC,SAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsCd,eAAA;AACA5K,EAAAA,IAAA,EAAA,iBAAA;AAEAC,EAAAA,UAAA,EAAA;AACA4K,qBAAAA,mBAAAA;GACA;AAEAC,EAAAA,OAAA,GAAA;AACA,IAAA,IAAA,OAAA5K,cAAA,KAAA,WAAA,EAAA;AACA,MAAA,IAAA,CAAA6K,gBAAA,GAAA,IAAA7K,cAAA,CAAA8K,OAAA,IAAA;AACA9F,QAAAA,qBAAA,CAAA,MAAA;AACA,UAAA,IAAA,CAAAxG,KAAA,CAAAuM,OAAA,CAAAD,OAAA,CAAA,EAAA;AACA,YAAA,OAAA;AACA,WAAA;AACA,UAAA,KAAA,MAAAvF,KAAA,IAAAuF,OAAA,EAAA;YACA,IAAAvF,KAAA,CAAA8C,MAAA,EAAA;AACA,cAAA,MAAAtD,KAAA,GAAA,IAAAiG,WAAA,CACA,QAAA,EACA;AACAC,gBAAAA,MAAA,EAAA;kBACAC,WAAA,EAAA3F,KAAA,CAAA2F,WAAAA;AACA,iBAAA;AACA,eAAA,CACA,CAAA;AACA3F,cAAAA,KAAA,CAAA8C,MAAA,CAAA8C,aAAA,CAAApG,KAAA,CAAA,CAAA;AACA,aAAA;AACA,WAAA;AACA,SAAA,CAAA,CAAA;AACA,OAAA,CAAA,CAAA;AACA,KAAA;IAEA,OAAA;MACAqG,WAAA,EAAA,IAAA,CAAAA,WAAA;AACAC,MAAAA,aAAA,EAAA,IAAA;MACAC,qBAAA,EAAA,IAAA,CAAAT,gBAAAA;KACA,CAAA;GACA;AAEAU,EAAAA,YAAA,EAAA,KAAA;AAEAlN,EAAAA,KAAA,EAAA;AACA,IAAA,GAAAA,KAAA;AAEAmC,IAAAA,WAAA,EAAA;AACAjC,MAAAA,IAAA,EAAA,CAAA6B,MAAA,EAAAzB,MAAA,CAAA;AACAF,MAAAA,QAAA,EAAA,IAAA;AACA,KAAA;GACA;AAEA0C,EAAAA,IAAA,GAAA;IACA,OAAA;AACAiK,MAAAA,WAAA,EAAA;AACAI,QAAAA,MAAA,EAAA,IAAA;QACA/J,KAAA,EAAA,EAAA;QACAgK,UAAA,EAAA,EAAA;QACA/M,QAAA,EAAA,IAAA,CAAAA,QAAA;AACAS,QAAAA,WAAA,EAAA,KAAA;AACA,OAAA;KACA,CAAA;GACA;AAEAqC,EAAAA,QAAA,EAAA;IACArC,WAAA;AAEAuM,IAAAA,aAAA,GAAA;MACA,MAAAC,MAAA,GAAA,EAAA,CAAA;MACA,MAAA;QAAArN,KAAA;QAAAI,QAAA;AAAAS,QAAAA,WAAAA;AAAA,OAAA,GAAA,IAAA,CAAA;AACA,MAAA,MAAAsC,KAAA,GAAA,IAAA,CAAA2J,WAAA,CAAA3J,KAAA,CAAA;AACA,MAAA,MAAAM,CAAA,GAAAzD,KAAA,CAAAc,MAAA,CAAA;MACA,KAAA,IAAA0C,CAAA,GAAA,CAAA,EAAAA,CAAA,GAAAC,CAAA,EAAAD,CAAA,EAAA,EAAA;AACA,QAAA,MAAA8B,IAAA,GAAAtF,KAAA,CAAAwD,CAAA,CAAA,CAAA;QACA,MAAAmC,EAAA,GAAA9E,WAAA,GAAA2C,CAAA,GAAA8B,IAAA,CAAAlF,QAAA,CAAA,CAAA;AACA,QAAA,IAAAsD,IAAA,GAAAP,KAAA,CAAAwC,EAAA,CAAA,CAAA;AACA,QAAA,IAAA,OAAAjC,IAAA,KAAA,WAAA,IAAA,CAAA,IAAA,CAAA4J,cAAA,CAAA3H,EAAA,CAAA,EAAA;AACAjC,UAAAA,IAAA,GAAA,CAAA,CAAA;AACA,SAAA;QACA2J,MAAA,CAAAvH,IAAA,CAAA;UACAR,IAAA;UACAK,EAAA;AACAjC,UAAAA,IAAAA;AACA,SAAA,CAAA,CAAA;AACA,OAAA;AACA,MAAA,OAAA2J,MAAA,CAAA;KACA;AAEAE,IAAAA,SAAA,GAAA;MACA,MAAAA,SAAA,GAAA,EAAA,CAAA;AACA,MAAA,KAAA,MAAAhI,GAAA,IAAA,IAAA,CAAAiI,UAAA,EAAA;AACA,QAAA,IAAAjI,GAAA,KAAA,QAAA,IAAAA,GAAA,KAAA,SAAA,EAAA;UACAgI,SAAA,CAAAhI,GAAA,CAAA,GAAA,IAAA,CAAAiI,UAAA,CAAAjI,GAAA,CAAA,CAAA;AACA,SAAA;AACA,OAAA;AACA,MAAA,OAAAgI,SAAA,CAAA;AACA,KAAA;GACA;AAEA3J,EAAAA,KAAA,EAAA;AACA5D,IAAAA,KAAA,GAAA;AACA,MAAA,IAAA,CAAAyN,WAAA,CAAA,KAAA,CAAA,CAAA;KACA;AAEA5M,IAAAA,WAAA,EAAA;MACAkD,OAAA,CAAAtD,KAAA,EAAA;AACA,QAAA,IAAA,CAAAqM,WAAA,CAAAjM,WAAA,GAAAJ,KAAA,CAAA;OACA;AACAiN,MAAAA,SAAA,EAAA,IAAA;KACA;IAEAnN,SAAA,CAAAE,KAAA,EAAA;AACA,MAAA,IAAA,CAAAgN,WAAA,CAAA,IAAA,CAAA,CAAA;KACA;AAEAL,IAAAA,aAAA,CAAAO,IAAA,EAAAC,IAAA,EAAA;AACA,MAAA,MAAA7C,SAAA,GAAA,IAAA,CAAAd,GAAA,CAAAc,SAAA,CAAA;;AAEA;AACA;AACA;MACA,IAAA8C,aAAA,GAAA,CAAA,CAAA;MAAA,IAAAC,SAAA,GAAA,CAAA,CAAA;AACA,MAAA,MAAAhN,MAAA,GAAA+G,IAAA,CAAAC,GAAA,CAAA6F,IAAA,CAAA7M,MAAA,EAAA8M,IAAA,CAAA9M,MAAA,CAAA,CAAA;MACA,KAAA,IAAA0C,CAAA,GAAA,CAAA,EAAAA,CAAA,GAAA1C,MAAA,EAAA0C,CAAA,EAAA,EAAA;QACA,IAAAqK,aAAA,IAAA9C,SAAA,EAAA;AACA,UAAA,MAAA;AACA,SAAA;QACA8C,aAAA,IAAAD,IAAA,CAAApK,CAAA,CAAA,CAAAE,IAAA,IAAA,IAAA,CAAAxB,WAAA,CAAA;QACA4L,SAAA,IAAAH,IAAA,CAAAnK,CAAA,CAAA,CAAAE,IAAA,IAAA,IAAA,CAAAxB,WAAA,CAAA;AACA,OAAA;AACA,MAAA,MAAAyH,MAAA,GAAAmE,SAAA,GAAAD,aAAA,CAAA;MAEA,IAAAlE,MAAA,KAAA,CAAA,EAAA;AACA,QAAA,OAAA;AACA,OAAA;AAEA,MAAA,IAAA,CAAAM,GAAA,CAAAc,SAAA,IAAApB,MAAA,CAAA;AACA,KAAA;GACA;AAEAoE,EAAAA,YAAA,GAAA;IACA,IAAA,CAAAC,SAAA,GAAA,EAAA,CAAA;IACA,IAAA,CAAAC,gBAAA,GAAA,CAAA,CAAA;AACA,IAAA,IAAA,CAAAX,cAAA,GAAA,EAAA,CAAA;GACA;AAEAxI,EAAAA,SAAA,GAAA;AACA,IAAA,IAAA,CAAAgI,WAAA,CAAAI,MAAA,GAAA,IAAA,CAAA;GACA;AAEAgB,EAAAA,WAAA,GAAA;AACA,IAAA,IAAA,CAAApB,WAAA,CAAAI,MAAA,GAAA,KAAA,CAAA;GACA;AAEA/H,EAAAA,OAAA,EAAA;AACAgJ,IAAAA,gBAAA,GAAA;AACA,MAAA,MAAAtC,QAAA,GAAA,IAAA,CAAAxD,KAAA,CAAAwD,QAAA,CAAA;AACA,MAAA,IAAAA,QAAA,EAAA;QACA,IAAA,CAAA4B,WAAA,EAAA,CAAA;AACA,OAAA;AACA,MAAA,IAAA,CAAAlH,KAAA,CAAA,QAAA,CAAA,CAAA;KACA;AAEA6H,IAAAA,iBAAA,GAAA;AACA,MAAA,IAAA,CAAA7H,KAAA,CAAA,gBAAA,EAAA;AAAA8H,QAAAA,KAAA,EAAA,KAAA;AAAA,OAAA,CAAA,CAAA;AACA,MAAA,IAAA,CAAA9H,KAAA,CAAA,SAAA,CAAA,CAAA;KACA;AAEAkH,IAAAA,WAAA,CAAApE,KAAA,GAAA,IAAA,EAAA;AACA,MAAA,IAAAA,KAAA,IAAA,IAAA,CAAAxI,WAAA,EAAA;AACA,QAAA,IAAA,CAAAiM,WAAA,CAAAK,UAAA,GAAA,EAAA,CAAA;AACA,OAAA;AACA,MAAA,IAAA,CAAA5G,KAAA,CAAA,gBAAA,EAAA;AAAA8H,QAAAA,KAAA,EAAA,IAAA;AAAA,OAAA,CAAA,CAAA;KACA;IAEA9C,YAAA,CAAAlG,KAAA,EAAA;AACA,MAAA,MAAAwG,QAAA,GAAA,IAAA,CAAAxD,KAAA,CAAAwD,QAAA,CAAA;AACA,MAAA,IAAAA,QAAA,EAAAA,QAAA,CAAAN,YAAA,CAAAlG,KAAA,CAAA,CAAA;KACA;AAEAiJ,IAAAA,WAAA,CAAAhJ,IAAA,EAAAD,KAAA,GAAArD,SAAA,EAAA;MACA,MAAA2D,EAAA,GAAA,IAAA,CAAA9E,WAAA,GAAAwE,KAAA,IAAA,IAAA,GAAAA,KAAA,GAAA,IAAA,CAAArF,KAAA,CAAAsJ,OAAA,CAAAhE,IAAA,CAAA,GAAAA,IAAA,CAAA,IAAA,CAAAlF,QAAA,CAAA,CAAA;MACA,OAAA,IAAA,CAAA0M,WAAA,CAAA3J,KAAA,CAAAwC,EAAA,CAAA,IAAA,CAAA,CAAA;KACA;AAEA4I,IAAAA,cAAA,GAAA;MACA,IAAA,IAAA,CAAAC,mBAAA,EAAA,OAAA;MACA,IAAA,CAAAA,mBAAA,GAAA,IAAA,CAAA;AACA,MAAA,MAAAnE,EAAA,GAAA,IAAA,CAAAJ,GAAA,CAAA;AACA;MACA,IAAA,CAAApF,SAAA,CAAA,MAAA;AACAwF,QAAAA,EAAA,CAAAU,SAAA,GAAAV,EAAA,CAAA9B,YAAA,GAAA,IAAA,CAAA;AACA;QACA,MAAAkG,EAAA,GAAA,MAAA;AACApE,UAAAA,EAAA,CAAAU,SAAA,GAAAV,EAAA,CAAA9B,YAAA,GAAA,IAAA,CAAA;AACA7B,UAAAA,qBAAA,CAAA,MAAA;AACA2D,YAAAA,EAAA,CAAAU,SAAA,GAAAV,EAAA,CAAA9B,YAAA,GAAA,IAAA,CAAA;AACA,YAAA,IAAA,IAAA,CAAA0F,gBAAA,KAAA,CAAA,EAAA;cACA,IAAA,CAAAO,mBAAA,GAAA,KAAA,CAAA;AACA,aAAA,MAAA;cACA9H,qBAAA,CAAA+H,EAAA,CAAA,CAAA;AACA,aAAA;AACA,WAAA,CAAA,CAAA;SACA,CAAA;QACA/H,qBAAA,CAAA+H,EAAA,CAAA,CAAA;AACA,OAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA,CAAA;;;AAnPA,MAActC,gBAAA,GAAAC,SAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACDd,aAAA;AACA5K,EAAAA,IAAA,EAAA,qBAAA;AAEAkN,EAAAA,MAAA,EAAA,CACA,aAAA,EACA,eAAA,EACA,uBAAA,CACA;AAEA3O,EAAAA,KAAA,EAAA;AACA;AACAuF,IAAAA,IAAA,EAAA;AACAnF,MAAAA,QAAA,EAAA,IAAA;KACA;AAEAwO,IAAAA,SAAA,EAAA;AACA1O,MAAAA,IAAA,EAAAsC,OAAA;AACAjC,MAAAA,OAAA,EAAA,KAAA;KACA;AAEA;AACA;AACA;AACA4M,IAAAA,MAAA,EAAA;AACAjN,MAAAA,IAAA,EAAAsC,OAAA;AACApC,MAAAA,QAAA,EAAA,IAAA;KACA;AAEAkF,IAAAA,KAAA,EAAA;AACApF,MAAAA,IAAA,EAAA6B,MAAA;AACAxB,MAAAA,OAAA,EAAA0B,SAAAA;KACA;AAEA4M,IAAAA,gBAAA,EAAA;AACA3O,MAAAA,IAAA,EAAA,CAAAC,KAAA,EAAAgB,MAAA,CAAA;AACAZ,MAAAA,OAAA,EAAA,IAAA;KACA;AAEAuO,IAAAA,UAAA,EAAA;AACA5O,MAAAA,IAAA,EAAAsC,OAAA;AACAjC,MAAAA,OAAA,EAAA,KAAA;KACA;AAEAwO,IAAAA,GAAA,EAAA;AACA7O,MAAAA,IAAA,EAAAI,MAAA;AACAC,MAAAA,OAAA,EAAA,KAAA;AACA,KAAA;GACA;AAEA4C,EAAAA,QAAA,EAAA;AACAyC,IAAAA,EAAA,GAAA;MACA,IAAA,IAAA,CAAAmH,WAAA,CAAAjM,WAAA,EAAA,OAAA,IAAA,CAAAwE,KAAA,CAAA;AACA;MACA,IAAA,IAAA,CAAAC,IAAA,CAAAyJ,cAAA,CAAA,IAAA,CAAAjC,WAAA,CAAA1M,QAAA,CAAA,EAAA,OAAA,IAAA,CAAAkF,IAAA,CAAA,IAAA,CAAAwH,WAAA,CAAA1M,QAAA,CAAA,CAAA;MACA,MAAA,IAAAqJ,KAAA,CAAA,CAAA,UAAA,EAAA,IAAA,CAAAqD,WAAA,CAAA1M,QAAA,CAAA,+EAAA,CAAA,CAAA,CAAA;KACA;AAEAsD,IAAAA,IAAA,GAAA;MACA,OAAA,IAAA,CAAAoJ,WAAA,CAAAK,UAAA,CAAA,IAAA,CAAAxH,EAAA,CAAA,IAAA,IAAA,CAAAmH,WAAA,CAAA3J,KAAA,CAAA,IAAA,CAAAwC,EAAA,CAAA,IAAA,CAAA,CAAA;KACA;AAEAqJ,IAAAA,WAAA,GAAA;MACA,OAAA,IAAA,CAAA9B,MAAA,IAAA,IAAA,CAAAJ,WAAA,CAAAI,MAAA,CAAA;AACA,KAAA;GACA;AAEAtJ,EAAAA,KAAA,EAAA;AACA+K,IAAAA,SAAA,EAAA,iBAAA;AAEAhJ,IAAAA,EAAA,GAAA;AACA,MAAA,IAAA,CAAA,IAAA,CAAAjC,IAAA,EAAA;QACA,IAAA,CAAAuL,YAAA,EAAA,CAAA;AACA,OAAA;KACA;IAEAD,WAAA,CAAAvO,KAAA,EAAA;AACA,MAAA,IAAA,CAAA,IAAA,CAAAiD,IAAA,EAAA;AACA,QAAA,IAAAjD,KAAA,EAAA;UACA,IAAA,CAAA,IAAA,CAAAsM,aAAA,CAAAO,cAAA,CAAA,IAAA,CAAA3H,EAAA,CAAA,EAAA;AACA,YAAA,IAAA,CAAAoH,aAAA,CAAAkB,gBAAA,EAAA,CAAA;YACA,IAAA,CAAAlB,aAAA,CAAAO,cAAA,CAAA,IAAA,CAAA3H,EAAA,CAAA,GAAA,IAAA,CAAA;AACA,WAAA;AACA,SAAA,MAAA;UACA,IAAA,IAAA,CAAAoH,aAAA,CAAAO,cAAA,CAAA,IAAA,CAAA3H,EAAA,CAAA,EAAA;AACA,YAAA,IAAA,CAAAoH,aAAA,CAAAkB,gBAAA,EAAA,CAAA;YACA,IAAA,CAAAlB,aAAA,CAAAO,cAAA,CAAA,IAAA,CAAA3H,EAAA,CAAA,GAAA,KAAA,CAAA;AACA,WAAA;AACA,SAAA;AACA,OAAA;MAEA,IAAA,IAAA,CAAAqH,qBAAA,EAAA;AACA,QAAA,IAAAvM,KAAA,EAAA;UACA,IAAA,CAAAyO,WAAA,EAAA,CAAA;AACA,SAAA,MAAA;UACA,IAAA,CAAAC,aAAA,EAAA,CAAA;AACA,SAAA;OACA,MAAA,IAAA1O,KAAA,IAAA,IAAA,CAAA2O,sBAAA,KAAA,IAAA,CAAAzJ,EAAA,EAAA;QACA,IAAA,CAAA0J,UAAA,EAAA,CAAA;AACA,OAAA;AACA,KAAA;GACA;AAEApL,EAAAA,OAAA,GAAA;IACA,IAAA,IAAA,CAAAqL,SAAA,EAAA,OAAA;IAEA,IAAA,CAAAC,wBAAA,GAAA,IAAA,CAAA;IACA,IAAA,CAAAC,eAAA,EAAA,CAAA;AAEA,IAAA,IAAA,CAAA,IAAA,CAAAxC,qBAAA,EAAA;AACA,MAAA,KAAA,MAAAyC,CAAA,IAAA,IAAA,CAAAb,gBAAA,EAAA;AACA,QAAA,IAAA,CAAAc,MAAA,CAAA,MAAA,IAAA,CAAAd,gBAAA,CAAAa,CAAA,CAAA,EAAA,IAAA,CAAAR,YAAA,CAAA,CAAA;AACA,OAAA;MAEA,IAAA,CAAAlC,aAAA,CAAA4C,GAAA,CAAA,gBAAA,EAAA,IAAA,CAAAC,eAAA,CAAA,CAAA;MACA,IAAA,CAAA7C,aAAA,CAAA4C,GAAA,CAAA,qBAAA,EAAA,IAAA,CAAAE,mBAAA,CAAA,CAAA;AACA,KAAA;GACA;AAEAjL,EAAAA,OAAA,GAAA;AACA,IAAA,IAAA,IAAA,CAAAkI,WAAA,CAAAI,MAAA,EAAA;MACA,IAAA,CAAAmC,UAAA,EAAA,CAAA;MACA,IAAA,CAAAH,WAAA,EAAA,CAAA;AACA,KAAA;GACA;AAEAjK,EAAAA,aAAA,GAAA;IACA,IAAA,CAAA8H,aAAA,CAAA+C,IAAA,CAAA,gBAAA,EAAA,IAAA,CAAAF,eAAA,CAAA,CAAA;IACA,IAAA,CAAA7C,aAAA,CAAA+C,IAAA,CAAA,qBAAA,EAAA,IAAA,CAAAD,mBAAA,CAAA,CAAA;IACA,IAAA,CAAAV,aAAA,EAAA,CAAA;GACA;AAEAhK,EAAAA,OAAA,EAAA;AACAkK,IAAAA,UAAA,GAAA;MACA,IAAA,IAAA,CAAAL,WAAA,EAAA;AACA,QAAA,IAAA,IAAA,CAAAe,mBAAA,KAAA,IAAA,CAAApK,EAAA,EAAA;AACA,UAAA,IAAA,CAAAoK,mBAAA,GAAA,IAAA,CAAApK,EAAA,CAAA;UACA,IAAA,CAAA4J,wBAAA,GAAA,IAAA,CAAA;UACA,IAAA,CAAAH,sBAAA,GAAA,IAAA,CAAA;AACA,UAAA,IAAA,CAAAY,WAAA,CAAA,IAAA,CAAArK,EAAA,CAAA,CAAA;AACA,SAAA;AACA,OAAA,MAAA;AACA,QAAA,IAAA,CAAA4J,wBAAA,GAAA,IAAA,CAAA5J,EAAA,CAAA;AACA,OAAA;KACA;AAEA6J,IAAAA,eAAA,GAAA;MACA,IAAA,IAAA,CAAAb,SAAA,IAAA,CAAA,IAAA,CAAA3B,qBAAA,EAAA;QACA,IAAA,CAAAiD,WAAA,GAAA,IAAA,CAAAP,MAAA,CAAA,MAAA,EAAA,MAAA;UACA,IAAA,CAAAT,YAAA,EAAA,CAAA;AACA,SAAA,EAAA;AACAjL,UAAAA,IAAA,EAAA,IAAA;AACA,SAAA,CAAA,CAAA;AACA,OAAA,MAAA,IAAA,IAAA,CAAAiM,WAAA,EAAA;QACA,IAAA,CAAAA,WAAA,EAAA,CAAA;QACA,IAAA,CAAAA,WAAA,GAAA,IAAA,CAAA;AACA,OAAA;KACA;AAEAL,IAAAA,eAAA,CAAA;AAAAvB,MAAAA,KAAAA;AAAA,KAAA,EAAA;AACA;AACA,MAAA,IAAA,CAAA,IAAA,CAAAW,WAAA,IAAAX,KAAA,EAAA;AACA,QAAA,IAAA,CAAAe,sBAAA,GAAA,IAAA,CAAAzJ,EAAA,CAAA;AACA,OAAA;AAEA,MAAA,IAAA,IAAA,CAAA4J,wBAAA,KAAA,IAAA,CAAA5J,EAAA,IAAA0I,KAAA,IAAA,CAAA,IAAA,CAAA3K,IAAA,EAAA;QACA,IAAA,CAAA2L,UAAA,EAAA,CAAA;AACA,OAAA;KACA;AAEAJ,IAAAA,YAAA,GAAA;MACA,IAAA,CAAAI,UAAA,EAAA,CAAA;KACA;IAEAW,WAAA,CAAArK,EAAA,EAAA;MACA,IAAA,CAAAd,SAAA,CAAA,MAAA;AACA,QAAA,IAAA,IAAA,CAAAc,EAAA,KAAAA,EAAA,EAAA;AACA,UAAA,MAAAwB,KAAA,GAAA,IAAA,CAAA8C,GAAA,CAAAiG,WAAA,CAAA;AACA,UAAA,MAAA9I,MAAA,GAAA,IAAA,CAAA6C,GAAA,CAAAkG,YAAA,CAAA;AACA,UAAA,IAAA,CAAAC,SAAA,CAAAjJ,KAAA,EAAAC,MAAA,CAAA,CAAA;AACA,SAAA;QACA,IAAA,CAAA2I,mBAAA,GAAA,IAAA,CAAA;AACA,OAAA,CAAA,CAAA;KACA;AAEAK,IAAAA,SAAA,CAAAjJ,KAAA,EAAAC,MAAA,EAAA;AACA,MAAA,MAAA1D,IAAA,GAAA,CAAA,EAAA,IAAA,CAAAqJ,aAAA,CAAAxM,SAAA,KAAA,UAAA,GAAA6G,MAAA,GAAAD,KAAA,CAAA,CAAA;AACA,MAAA,IAAAzD,IAAA,IAAA,IAAA,CAAAA,IAAA,KAAAA,IAAA,EAAA;QACA,IAAA,IAAA,CAAAqJ,aAAA,CAAAO,cAAA,CAAA,IAAA,CAAA3H,EAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAAoH,aAAA,CAAAkB,gBAAA,EAAA,CAAA;UACA,IAAA,CAAAlB,aAAA,CAAAO,cAAA,CAAA,IAAA,CAAA3H,EAAA,CAAA,GAAA3D,SAAA,CAAA;AACA,SAAA;AACA,QAAA,IAAA,CAAAqO,IAAA,CAAA,IAAA,CAAAvD,WAAA,CAAA3J,KAAA,EAAA,IAAA,CAAAwC,EAAA,EAAAjC,IAAA,CAAA,CAAA;AACA,QAAA,IAAA,CAAA2M,IAAA,CAAA,IAAA,CAAAvD,WAAA,CAAAK,UAAA,EAAA,IAAA,CAAAxH,EAAA,EAAA,IAAA,CAAA,CAAA;AACA,QAAA,IAAA,IAAA,CAAAkJ,UAAA,EAAA,IAAA,CAAAtI,KAAA,CAAA,QAAA,EAAA,IAAA,CAAAZ,EAAA,CAAA,CAAA;AACA,OAAA;KACA;AAEAuJ,IAAAA,WAAA,GAAA;MACA,IAAA,CAAA,IAAA,CAAAlC,qBAAA,IAAA,CAAA,IAAA,CAAA/C,GAAA,CAAAqG,UAAA,EAAA,OAAA;MACA,IAAA,CAAAtD,qBAAA,CAAAuD,OAAA,CAAA,IAAA,CAAAtG,GAAA,CAAAqG,UAAA,CAAA,CAAA;AACA,MAAA,IAAA,CAAArG,GAAA,CAAAqG,UAAA,CAAAjP,gBAAA,CAAA,QAAA,EAAA,IAAA,CAAAmP,QAAA,CAAA,CAAA;KACA;AAEArB,IAAAA,aAAA,GAAA;AACA,MAAA,IAAA,CAAA,IAAA,CAAAnC,qBAAA,EAAA,OAAA;MACA,IAAA,CAAAA,qBAAA,CAAAyD,SAAA,CAAA,IAAA,CAAAxG,GAAA,CAAAqG,UAAA,CAAA,CAAA;AACA,MAAA,IAAA,CAAArG,GAAA,CAAAqG,UAAA,CAAAhF,mBAAA,CAAA,QAAA,EAAA,IAAA,CAAAkF,QAAA,CAAA,CAAA;KACA;IAEAA,QAAA,CAAA/J,KAAA,EAAA;MACA,MAAA;QAAAU,KAAA;AAAAC,QAAAA,MAAAA;AAAA,OAAA,GAAAX,KAAA,CAAAkG,MAAA,CAAAC,WAAA,CAAA;AACA,MAAA,IAAA,CAAAwD,SAAA,CAAAjJ,KAAA,EAAAC,MAAA,CAAA,CAAA;AACA,KAAA;GACA;EAEAsJ,MAAA,CAAAhI,CAAA,EAAA;IACA,OAAAA,CAAA,CAAA,IAAA,CAAAoG,GAAA,EAAA,IAAA,CAAA6B,MAAA,CAAArQ,OAAA,CAAA,CAAA;AACA,GAAA;AACA,CAAA;;;AAzNA,MAAc,cAAA,GAAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAC,gBAAU,EAAA;AACvBsQ,EAAAA,MAAM,GAAGC,EAAE,IAAIA,EAAE,CAACvL,IAAI,CAACK,EAAAA;AACzB,CAAC,GAAG,EAAE,EAAE;EACN,MAAMmL,KAAK,GAAG,EAAE,CAAA;AAChB,EAAA,MAAMD,EAAE,GAAG,IAAIE,GAAG,CAAC;AACjBlO,IAAAA,IAAI,GAAI;MACN,OAAO;AACLiO,QAAAA,KAAAA;OACD,CAAA;AACH,KAAA;AACF,GAAC,CAAC,CAAA;;AAEF;EACA,OAAO;AACLjO,IAAAA,IAAI,GAAI;MACN,OAAO;AACLmO,QAAAA,OAAO,EAAE,IAAA;OACV,CAAA;KACF;AAED/M,IAAAA,OAAO,GAAI;MACT,IAAI,CAACgN,IAAI,GAAG,IAAI,CAAA;AAChB,MAAA,IAAI,OAAOL,MAAM,KAAK,UAAU,EAAE;QAChC,IAAI,CAACM,OAAO,GAAG,MAAMN,MAAM,CAACO,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;AAC9C,OAAC,MAAM;AACL,QAAA,IAAI,CAACD,OAAO,GAAG,MAAM,IAAI,CAACN,MAAM,CAAC,CAAA;AACnC,OAAA;AACA,MAAA,IAAI,CAAClB,MAAM,CAAC,IAAI,CAACwB,OAAO,EAAE;QACxBnN,OAAO,CAAEtD,KAAK,EAAE;UACd,IAAI,CAACoE,SAAS,CAAC,MAAM;YACnB,IAAI,CAACoM,IAAI,GAAGxQ,KAAK,CAAA;AACnB,WAAC,CAAC,CAAA;SACH;AACDiN,QAAAA,SAAS,EAAE,IAAA;AACb,OAAC,CAAC,CAAA;MACF,IAAI,CAAC0D,eAAe,EAAE,CAAA;KACvB;AAEDC,IAAAA,YAAY,GAAI;MACd,IAAI,CAACD,eAAe,EAAE,CAAA;KACvB;AAEDjM,IAAAA,OAAO,EAAE;AACP;AACN;AACA;AACA;MACMmM,aAAa,CAAE3L,EAAE,EAAE;AACjB,QAAA,MAAM4L,OAAO,GAAG,IAAI,CAACC,QAAQ,CAACR,OAAO,CAAA;AACrC,QAAA,IAAI,OAAOO,OAAO,KAAK,UAAU,EAAE;UACjC,MAAM1O,IAAI,GAAG0O,OAAO,CAACJ,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;UACrCN,EAAE,CAACR,IAAI,CAACS,KAAK,EAAEnL,EAAE,EAAE9C,IAAI,CAAC,CAAA;UACxB,IAAI,CAACoO,IAAI,GAAGtL,EAAE,CAAA;AACd,UAAA,OAAO9C,IAAI,CAAA;AACb,SAAC,MAAM;AACL,UAAA,MAAM,IAAI4G,KAAK,CAAC,qEAAqE,CAAC,CAAA;AACxF,SAAA;OACD;AAED;AACN;AACA;AACM2H,MAAAA,eAAe,GAAI;AACjB,QAAA,MAAMzL,EAAE,GAAG,IAAI,CAACuL,OAAO,EAAE,CAAA;QACzB,IAAIvL,EAAE,IAAI,IAAI,EAAE;AACdjB,UAAAA,OAAO,CAAC+M,IAAI,CAAE,CAAwCb,sCAAAA,EAAAA,MAAO,IAAG,CAAC,CAAA;AACnE,SAAA;AACA,QAAA,IAAIjL,EAAE,KAAK,IAAI,CAACsL,IAAI,EAAE;AACpB,UAAA,IAAI,CAACH,KAAK,CAACnL,EAAE,CAAC,EAAE;AACd,YAAA,IAAI,CAAC2L,aAAa,CAAC3L,EAAE,CAAC,CAAA;AACxB,WAAA;AACA,UAAA,IAAI,CAACqL,OAAO,GAAGF,KAAK,CAACnL,EAAE,CAAC,CAAA;AAC1B,SAAA;AACF,OAAA;AACF,KAAA;GACD,CAAA;AACH;;AChEA,SAAS+L,kBAAkB,CAAEX,GAAG,EAAEY,MAAM,EAAE;EACxCZ,GAAG,CAACa,SAAS,CAAE,CAAA,EAAED,MAAO,CAAiB,gBAAA,CAAA,EAAEtF,mBAAe,CAAC,CAAA;EAC3D0E,GAAG,CAACa,SAAS,CAAE,CAAA,EAAED,MAAO,CAAgB,eAAA,CAAA,EAAEtF,mBAAe,CAAC,CAAA;EAC1D0E,GAAG,CAACa,SAAS,CAAE,CAAA,EAAED,MAAO,CAAiB,gBAAA,CAAA,EAAEE,mBAAe,CAAC,CAAA;EAC3Dd,GAAG,CAACa,SAAS,CAAE,CAAA,EAAED,MAAO,CAAgB,eAAA,CAAA,EAAEE,mBAAe,CAAC,CAAA;EAC1Dd,GAAG,CAACa,SAAS,CAAE,CAAA,EAAED,MAAO,CAAsB,qBAAA,CAAA,EAAEG,iBAAmB,CAAC,CAAA;EACpEf,GAAG,CAACa,SAAS,CAAE,CAAA,EAAED,MAAO,CAAoB,mBAAA,CAAA,EAAEG,iBAAmB,CAAC,CAAA;AACpE,CAAA;AAEA,MAAMC,MAAM,GAAG;AACb;AACAC,EAAAA,OAAO,EAAEC,OAAO;AAChBC,EAAAA,OAAO,CAAEnB,GAAG,EAAEoB,OAAO,EAAE;IACrB,MAAMC,YAAY,GAAGlR,MAAM,CAACmR,MAAM,CAAC,EAAE,EAAE;AACrCC,MAAAA,iBAAiB,EAAE,IAAI;AACvBC,MAAAA,gBAAgB,EAAE,EAAA;KACnB,EAAEJ,OAAO,CAAC,CAAA;AAEX,IAAA,KAAK,MAAM5M,GAAG,IAAI6M,YAAY,EAAE;AAC9B,MAAA,IAAI,OAAOA,YAAY,CAAC7M,GAAG,CAAC,KAAK,WAAW,EAAE;AAC5C2D,QAAAA,MAAM,CAAC3D,GAAG,CAAC,GAAG6M,YAAY,CAAC7M,GAAG,CAAC,CAAA;AACjC,OAAA;AACF,KAAA;IAEA,IAAI6M,YAAY,CAACE,iBAAiB,EAAE;AAClCZ,MAAAA,kBAAkB,CAACX,GAAG,EAAEqB,YAAY,CAACG,gBAAgB,CAAC,CAAA;AACxD,KAAA;AACF,GAAA;AACF,EAAC;;AAID;AACA,IAAIC,SAAS,GAAG,IAAI,CAAA;AACpB,IAAI,OAAOxR,MAAM,KAAK,WAAW,EAAE;EACjCwR,SAAS,GAAGxR,MAAM,CAAC+P,GAAG,CAAA;AACxB,CAAC,MAAM,IAAI,OAAO0B,MAAM,KAAK,WAAW,EAAE;EACxCD,SAAS,GAAGC,MAAM,CAAC1B,GAAG,CAAA;AACxB,CAAA;AACA,IAAIyB,SAAS,EAAE;AACbA,EAAAA,SAAS,CAACE,GAAG,CAACX,MAAM,CAAC,CAAA;AACvB;;;;"}