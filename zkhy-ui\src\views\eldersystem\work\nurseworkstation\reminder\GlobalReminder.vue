<template>
  <div class="reminder-container">
    <el-dialog
      v-model="dialogVisible"
      title="护士提醒"
      width="500px"
      :show-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="reminder-content">
        <div class="reminder-icon">
          <img src="@/assets/reminder-icon.svg" alt="提醒图标" />
        </div>
        <div class="reminder-message">
          <h3>{{ reminderMessage }}</h3>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="onDelay">延迟1小时</el-button>
          <el-button type="primary" @click="onConfirm">知道了</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useEventBus } from '@/utils/eventBus'
import { updateMyRemind } from '@/api/nurseworkstation/index'
import { ElMessage } from 'element-plus'
import moment from 'moment'

const dialogVisible = ref(false)
const reminderMessage = ref('')
const reminderId = ref(null)
const userInfo = ref({})

// 使用事件总线监听提醒事件
onMounted(() => {
  // 获取当前用户信息
  userInfo.value = JSON.parse(localStorage.getItem('userInfo') || '{}')
  console.log('userInfo:', userInfo.value.userId)
  useEventBus.on('nurse-reminder', (data) => {
    try {
      // 如果数据是字符串，尝试解析为JSON
      const reminderData = typeof data === 'string' ? JSON.parse(data) : data
      
      // 确保弹窗只弹出一次
      if (!dialogVisible.value) {
        // 确保提醒属于当前用户
        if(userInfo.value.userId !== reminderData.id) return
        reminderMessage.value = reminderData.content || '您有一条新的提醒'
        reminderId.value = reminderData.id
        dialogVisible.value = true
      }
    } catch (error) {
      console.error('处理提醒数据失败:', error)
      // 即使解析失败也显示提醒
      if (!dialogVisible.value) {
        reminderMessage.value = '您有一条新的提醒'
        dialogVisible.value = true
      }
    }
  })
})

// 延迟1小时按钮点击事件
const onDelay = async () => {
  try {
    // 获取当前时间并加1小时
    const now = new Date()
    const delayedTime = new Date(now.getTime() + 60 * 60 * 1000) // 当前时间加1小时
    
    // 格式化时间为接口需要的格式 (YYYY-MM-DD HH:mm:ss)
    const formattedTime = moment(delayedTime).format('YYYY-MM-DD HH:mm:ss')
    console.log('formattedTime:', formattedTime)
    
    // 调用接口更新提醒时间
    const res = await updateMyRemind({
      id: reminderId.value,
      nurseId: userInfo.value.userId,
      nurseName: userInfo.value.userName,
      status: '0', // 保持未完成状态
      reminderTime: formattedTime // 设置新的提醒时间
    })
    
    if (res.code === 200) {
      ElMessage.success('提醒已延迟1小时')
      dialogVisible.value = false
      // 触发事件通知其他组件刷新提醒列表
      useEventBus.emit('refresh-reminders')
    } else {
      ElMessage.error('操作失败，请重试')
    }
  } catch (error) {
    console.error('延迟提醒出错:', error)
    ElMessage.error('操作失败，请重试')
  }
}

// 知道了按钮点击事件
const onConfirm = async () => {
  try {
    // 调用接口更新提醒状态为已完成
    const res = await updateMyRemind({
      id: reminderId.value,
      nurseId: userInfo.value.userId,
      nurseName: userInfo.value.userName,
      status: '1' // 设置为已完成状态
    })
    
    if (res.code === 200) {
      dialogVisible.value = false
      ElMessage.success('提醒已完成')
      // 触发事件通知其他组件刷新提醒列表
      useEventBus.emit('refresh-reminders')
    } else {
      ElMessage.error('操作失败，请重试')
    }
  } catch (error) {
    console.error('确认提醒出错:', error)
    ElMessage.error('操作失败，请重试')
    dialogVisible.value = false
  }
}
</script>

<style scoped>
.reminder-container {
  position: relative;
  z-index: 2000;
}

.reminder-content {
  display: flex;
  align-items: center;
  padding: 20px 0;
}

.reminder-icon {
  margin-right: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  background-color: #4e72f1;
  border-radius: 50%;
}

.reminder-icon img {
  width: 40px;
  height: 40px;
}

.reminder-message {
  flex: 1;
}

.reminder-message h3 {
  font-size: 18px;
  margin: 0;
  line-height: 1.5;
}

.dialog-footer {
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 20px;
}
</style>