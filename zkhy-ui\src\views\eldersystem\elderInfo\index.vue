<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryRef"
      :inline="true"
      :model="queryParams"
      label-width="80px"
    >
      <el-row>
        <el-col :span="6">
          <el-form-item label="老人姓名" prop="elderName">
            <el-input
              v-model="queryParams.elderName"
              clearable
              placeholder="请输入老人姓名"
              style="width: 230px"
              @keyup.enter="handleQuery"
            /> </el-form-item
        ></el-col>
        <el-col :span="6">
          <el-form-item label="自理能力" prop="selfCareAbility">
            <el-select
              v-model="queryParams.selfCareAbility"
              clearable
              placeholder="请选择自理能力"
              style="width: 230px"
              @keyup.enter="handleQuery"
            >
              <el-option
                v-for="dict in self_careability"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="6">
          <el-form-item label="照护等级" prop="careLevel">
            <el-select
              v-model="queryParams.careLevel"
              clearable
              placeholder="请选择照护等级"
              style="width: 230px"
              @keyup.enter="handleQuery"
            >
              <el-option
                v-for="dict in care_level"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="6">
          <el-form-item label="护理等级" prop="nursingLevel">
            <el-select
              v-model="queryParams.nursingLevel"
              clearable
              placeholder="请选择护理等级"
              style="width: 230px"
              @keyup.enter="handleQuery"
            >
              <el-option
                v-for="dict in nursing_grade"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="6">
          <el-form-item label="入住房号" prop="roomId">
            <el-input
              v-model="queryParams.roomBed"
              clearable
              placeholder="请输入入住房号"
              style="width: 230px"
              @keyup.enter="handleQuery"
            />
            <el-select
              v-model="queryParams.roomBed"
              clearable
              v-if="false"
              placeholder="请选择入住房号"
              style="width: 230px"
              @keyup.enter="handleQuery"
            >
              <el-option
                v-for="dict in roomBed"
                :key="dict.id"
                :label="dict.value"
                :value="dict.value"
              ></el-option>
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="6">
          <el-form-item
            label="状&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;态"
            prop="status"
          >
            <el-select
              v-model="queryParams.status"
              clearable
              placeholder="请选择状态"
              style="width: 230px"
              @keyup.enter="handleQuery"
            >
              <!--                    <el-option v-for='dict in in_hospital_status' :key='dict.value' :label='dict.label' :value='dict.value'></el-option>-->
              <el-option
                v-for="dict in in_hospital_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="6">
          <el-form-item label="入院时间" prop="contractStartDate">
            <el-date-picker
              v-model="queryParams.dateRangeRY"
              end-placeholder="结束日期"
              range-separator="-"
              start-placeholder="开始日期"
              style="width: 230px"
              type="daterange"
              value-format="YYYY-MM-DD"
            ></el-date-picker> </el-form-item
        ></el-col>
        <el-col :span="6">
          <el-form-item label="合同日期" prop="contractStartDate">
            <el-date-picker
              v-model="queryParams.dateRangeHT"
              end-placeholder="结束日期"
              range-separator="-"
              start-placeholder="开始日期"
              style="width: 230px"
              type="daterange"
              value-format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row justify="end" style="height: 5px">
        <el-form-item>
          <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          <el-button icon="Plus" plain type="primary">
            <template #default>
              <router-link
                :to="{
                  name: 'elderInfoDetail',
                  params: { id: '0', type: 'add' },
                }"
              >
                <span>新增</span>
              </router-link>
            </template>
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <div class="changeTagCss">
      <div @click="changeIcon(0)">
        <el-icon :class="isSelect == 0 ? 'colorSelect' : 'colorNoSelect'" size="20">
          <Expand />
        </el-icon>
      </div>
      <div @click="changeIcon(1)">
        <el-icon :class="isSelect == 1 ? 'colorSelect' : 'colorNoSelect'" size="20">
          <Grid />
        </el-icon>
      </div>
    </div>
    <div v-if="isSelect == 0">
      <el-table
        v-loading="loading"
        :data="telderinfoList"
        border
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" type="index" />
        <el-table-column align="center" label="老人头像" prop="elderId" min-width="90">
          <template #default="scope">
            <el-image
              :src="scope.row.avatar"
              class="avatarcss"
              style="width: 60px; height: 60px"
            ></el-image>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="老人姓名"
          prop="elderName"
          min-width="120"
        />
        <el-table-column
          align="center"
          label="老人编号"
          prop="elderCode"
          min-width="120"
        />
        <el-table-column align="center" label="老人性别" prop="gender">
          <template #default="scope">
            <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="老人年龄" prop="age" />
        <el-table-column align="center" label="出生年月" prop="birthDate" width="120" />
        <el-table-column align="center" label="入住房号" prop="roomId">
          <template #default="scope">
            <span>{{ scope.row.roomBed }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="自理能力" prop="selfCareAbility">
          <template #default="scope">
            <dict-tag :options="self_careability" :value="scope.row.selfCareAbility" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="能力等级" prop="abilityLevel">
          <template #default="scope">
            <dict-tag :options="capability_level" :value="scope.row.abilityLevel" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="照护等级" prop="careLevel">
          <template #default="scope">
            <dict-tag :options="care_level" :value="scope.row.careLevel" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="护理等级" prop="nursingLevel">
          <template #default="scope">
            <dict-tag :options="nursing_grade" :value="scope.row.nursingLevel" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="入院时间" prop="checkInDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.checkInDate, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="收费标准"
          prop="priceStandard"
          min-width="140"
        ></el-table-column>
        <el-table-column align="center" label="状态" prop="status">
          <template #default="scope">
            <dict-tag :options="in_hospital_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="合同期限" prop="bedId" width="200">
          <template #default="scope">
            <span
              >{{ scope.row.contractStartDate }}--{{ scope.row.contractEndDate }}</span
            >
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          fixed="right"
          class-name="small-padding fixed-width"
          label="操作"
          width="200px"
        >
          <template #default="scope">
            <el-button icon="Search" link type="primary">
              <template #default>
                <router-link
                  :to="{
                    name: 'elderInfoDetail',
                    params: { id: scope.row.id, type: 'show' },
                  }"
                >
                  <span>查看</span>
                </router-link>
              </template>
            </el-button>
            <el-button icon="Edit" link type="primary">
              <template #default>
                <router-link
                  :to="{
                    name: 'elderInfoDetail',
                    params: { id: scope.row.id, type: 'edit' },
                  }"
                >
                  <span>修改</span>
                </router-link>
              </template>
            </el-button>
            <el-button icon="Delete" link type="primary" @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        v-model:limit="queryParams.pageSize"
        v-model:page="queryParams.pageNum"
        :total="total"
        @pagination="getList"
      />
    </div>
    <div v-if="isSelect == 1">
      <el-row :gutter="15">
        <el-col v-for="(item, index) in telderinfoList" :key="index" :span="4">
          <el-card shadow="hover" style="margin-bottom: 10px; border-radius: 10px">
            <div>
              <div class="cardDetailTop flexAlginContent">
                <div>
                  <el-image
                    :src="item.avatar"
                    class="avatarcss"
                    style="width: 60px; height: 60px"
                  ></el-image>
                </div>
                <div style="margin-left: 15px; width: 100%">
                  <div class="cardDetailTop flexSpaceBetween">
                    <div class="elderFont">{{ item.elderName }}</div>
                    <div :class="item.status == 1 ? 'greenType' : 'redType'">
                      {{ item.status == 1 ? "在院" : "离院" }}
                    </div>
                  </div>
                  <div style="margin-top: 10px; color: #999">
                    ID:
                    <span class="valueCss">{{ item.elderCode }}</span>
                  </div>
                </div>
              </div>

              <div class="abilityCss">
                <div>
                  <dict-tag :options="self_careability" :value="item.selfCareAbility" />
                </div>
                <div>
                  <dict-tag :options="care_level" :value="item.careLevel" />
                </div>
                <div>
                  <dict-tag :options="nursing_grade" :value="item.nursingLevel" />
                </div>
              </div>

              <div class="fontLineheight" style="margin-top: 10px">
                年 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;龄：<span class="valueCss">{{
                  item.age
                }}</span>
              </div>
              <div class="fontLineheight">
                房 &nbsp;间 &nbsp;号：<span class="valueCss"
                  >{{ item.roomId }}--{{ item.bedId }}</span
                >
              </div>
              <div class="fontLineheight">
                入院时间：
                <span class="valueCss">{{ item.checkInDate }}</span>
              </div>
              <div class="cardDetailTop flexSpaceBetween" style="margin-top: 12px">
                <el-icon @click="handleDelete(item)" color="#c1bdbd"> <Delete /></el-icon>

                <el-button type="primary" size="small" plain>
                  <template #default>
                    <router-link :to="'/elderInfo/elderFiles/detail/' + item.id">
                      <span>用户详情</span>
                    </router-link>
                  </template>
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <!-- 添加或修改老人基本信息对话框 -->
    <el-dialog v-model="open" :title="title" append-to-body width="70%">
      <el-row :gutter="20">
        <el-col :span="2"></el-col>
        <el-col :span="20">
          <el-form ref="telderinfoRef" :model="form" :rules="rules" label-width="120px">
            <el-form-item label="老人姓名" prop="elderName">
              <el-input v-model="form.elderName" placeholder="请输入老人姓名" />
            </el-form-item>
            <el-form-item label="老人编号" prop="elderCode">
              <el-input v-model="form.elderCode" placeholder="请输入老人编号" />
            </el-form-item>
            <el-form-item label="老人头像路径" prop="avatar">
              <el-input v-model="form.avatar" placeholder="请输入老人头像路径" />
            </el-form-item>
            <el-form-item label="性别" prop="gender">
              <el-input v-model="form.gender" placeholder="请输入性别" />
            </el-form-item>
            <el-form-item label="老人年龄" prop="age">
              <el-input v-model="form.age" placeholder="请输入老人年龄" />
            </el-form-item>
            <el-form-item label="出生日期" prop="birthDate">
              <el-date-picker
                v-model="form.birthDate"
                clearable
                placeholder="请选择出生日期"
                type="date"
                value-format="YYYY-MM-DD"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="form.idCard" placeholder="请输入身份证号" />
            </el-form-item>
            <el-form-item label="身份证正面照片路径" prop="idCardFrontPhoto">
              <el-input
                v-model="form.idCardFrontPhoto"
                placeholder="请输入身份证正面照片路径"
              />
            </el-form-item>
            <el-form-item label="身份证反面照片路径" prop="idCardBackPhoto">
              <el-input
                v-model="form.idCardBackPhoto"
                placeholder="请输入身份证反面照片路径"
              />
            </el-form-item>
            <el-form-item label="老人电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入老人电话" />
            </el-form-item>
            <el-form-item label="民族" prop="nation">
              <el-input v-model="form.nation" placeholder="请输入民族" />
            </el-form-item>
            <el-form-item label="教育程度" prop="education">
              <el-input v-model="form.education" placeholder="请输入教育程度" />
            </el-form-item>
            <el-form-item label="籍贯" prop="hometown">
              <el-input v-model="form.hometown" placeholder="请输入籍贯" />
            </el-form-item>
            <el-form-item label="老人职业" prop="formerOccupation">
              <el-input v-model="form.formerOccupation" placeholder="请输入老人职业" />
            </el-form-item>
            <el-form-item label="工作单位" prop="workUnit">
              <el-input v-model="form.workUnit" placeholder="请输入工作单位" />
            </el-form-item>
            <el-form-item label="社保号码" prop="socialSecurityCode">
              <el-input v-model="form.socialSecurityCode" placeholder="请输入社保号码" />
            </el-form-item>
            <el-form-item label="经济来源" prop="economicSource">
              <el-input v-model="form.economicSource" placeholder="请输入经济来源" />
            </el-form-item>
            <el-form-item label="家庭住址" prop="homeAddress">
              <el-input v-model="form.homeAddress" placeholder="请输入家庭住址" />
            </el-form-item>
            <el-form-item label="老人标签" prop="elderTags">
              <el-input
                v-model="form.elderTags"
                placeholder="请输入内容"
                type="textarea"
              />
            </el-form-item>
            <el-form-item label="老人备注" prop="remark">
              <el-input v-model="form.remark" placeholder="请输入内容" type="textarea" />
            </el-form-item>
            <el-form-item label="建档人" prop="archivist">
              <el-input v-model="form.archivist" placeholder="请输入建档人" />
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script name="Telderinfo" setup>
import {
  addBasicInfo,
  delBasicInfo,
  getBasicInfo,
  listBasicInfo,
  updateBasicInfo,
} from "@/api/ReceptionManagement/telderinfo";
import { dealParams } from "@/utils/paramUtil.js";

import { ref } from "vue";
import { useRoute } from "vue-router";

const { proxy } = getCurrentInstance();
const {
  sys_yes_no,
  sys_user_sex,
  self_careability,
  care_level,
  nursing_grade,
  capability_level,
  residential_type,
  in_hospital_status,
} = proxy.useDict(
  "sys_yes_no",
  "sys_user_sex",
  "self_careability",
  "care_level",
  "nursing_grade",
  "capability_level",
  "residential_type",
  "in_hospital_status"
);
const route = useRoute();
const in_hospital_status_local = [
  {
    label: "在院",
    value: Number(1),
  },
  {
    label: "离院",
    value: Number(0),
  },
];
const telderinfoList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const isSelect = ref(0);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    elderName: null,
    elderCode: null,
    gender: null,
    age: null,
    birthDate: null,
    idCard: null,
    idCardFrontPhoto: null,
    idCardBackPhoto: null,
    politicalStatus: null,
    socialSecurityCode: null,
    status: null,
    dateRangeHT: [],
    dateRangeRY: [],
  },
  rules: {
    elderCode: [
      {
        required: true,
        message: "老人编号不能为空",
        trigger: "blur",
      },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询老人基本信息列表 */
function getList() {
  loading.value = true;

  // 处理签约时间范围
  const params = { ...queryParams.value };
  // 处理时间范围条件
  dealParams(params, queryParams, ["dateRangeHT", "dateRangeRY"]);
  delete params.dateRangeHT;
  delete params.dateRangeRY;

  listBasicInfo(params).then((response) => {
    console.log(response, "initeldinfo");
    telderinfoList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

const roomBed = [
  {
    id: 1,
    value: "201-01",
  },
  {
    id: 2,
    value: "201-02",
  },
  {
    id: 3,
    value: "201-03",
  },
  {
    id: 4,
    value: "201-04",
  },
];

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    elderName: null,
    elderCode: null,
    avatar: null,
    gender: null,
    age: null,
    birthDate: null,
    idCard: null,
    idCardFrontPhoto: null,
    idCardBackPhoto: null,
    phone: null,
    nation: null,
    education: null,
    politicalStatus: null,
    maritalStatus: null,
    bloodType: null,
    hometown: null,
    formerOccupation: null,
    workUnit: null,
    socialSecurityCode: null,
    economicSource: null,
    homeAddress: null,
    elderTags: null,
    remark: null,
    archivist: null,
    status: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
  };

  proxy.resetForm("telderinfoRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.dateRangeRY = [];
  queryParams.value.dateRangeHT = [];
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加老人基本信息";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getBasicInfo(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改老人基本信息";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["telderinfoRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateBasicInfo(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addBasicInfo(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除老人基本信息编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delBasicInfo(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "elderinfo/basicInfo/export",
    {
      ...queryParams.value,
    },
    `telderinfo_${new Date().getTime()}.xlsx`
  );
}

function changeIcon(type) {
  console.log(type, "changes");
  if (type == 0) {
    isSelect.value = 0;
  } else if (type == 1) {
    isSelect.value = 1;
  }
}

function handleShowDetail(id) {
  //router.to;
}

getList();
</script>
<style scoped>
.colorSelect {
  color: rgb(64, 158, 225);
  width: 50px;
  height: 50px;
}

.colorNoSelect {
  color: rgb(155, 153, 153);
  width: 50px;
  height: 50px;
}

.changeTagCss {
  display: flex;
  flex-direction: row;
  justify-content: left;
  align-content: center;
  height: 45px;
}

.cardDetailTop {
  width: 100%;
  display: flex;
  flex-direction: row;
}

.flexSpaceBetween {
  justify-content: space-between;
}

.flexAlginContent {
  align-content: center;
  align-items: center;
}

.elderFont {
  font-size: 18px;
  color: #999;
  font-weight: 600;
}

.fontLineheight {
  line-height: 26px;
  color: #999;
}

.bodderBotton {
  border-bottom: 1px solid #ebe7e7;
}

.greenType {
  background-color: rgb(10, 184, 82);
  font-size: 11px;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 20px;
  text-align: center;
  justify-content: center;
  padding-top: 7px;
}

.redType {
  background-color: rgb(199, 30, 30);
  font-size: 11px;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 20px;
  text-align: center;
  justify-content: center;
  padding-top: 7px;
}

.avatarcss {
  border-radius: 30px;
}

.abilityCss {
  display: flex;
  flex-direction: row;
  justify-content: left;
  width: 100%;
  height: 22px;
}

.valueCss {
  color: rgb(63, 62, 62);
}
</style>
