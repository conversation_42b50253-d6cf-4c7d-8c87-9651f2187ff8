import request from '@/utils/request'
//和孚护理查房记录-新增查房
export function addNurseCheck(data) {
  return request({
    url: '/elderinfo/basicInfo/listinfo',
    method: 'get',
    params: data
  })
}
//和孚护理查房记录-提交查房
export function submitNurseCheck(data) {
  return request({
    url: '/roomnurserec/hfrecord',
    method: 'post',
    data: data
  })
}
// 和孚护理查房记录-查询查房记录
export function getNurseCheckList(data) {
  return request({
    url: '/roomnurserec/hfrecord/list',
    method: 'get',
    params: data
  })
}
// 和孚护理查房记录-删除查房记录
export function deleteNurseCheck(data) {
  return request({
    url: `roomnurserec/hfrecord/${data}`,
    method: 'delete'
  })
}
// 新增护士待办
export function addNurseTodo(data) {
  return request({
    url: '/nursereminder/todo',
    method: 'post',
    data: data
  })
}
//查询护士待办列表
export function getNurseTodoList(data) {
  return request({
    url: '/nursereminder/todo/list',
    method: 'get',
    params: data
  })
}
// 修改护士待办接口
export function updateNurseTodo(data) {
  return request({
    url: '/nursereminder/todo',
    method: 'put',
    data: data
  })
}
// 删除护士待办接口
export function deleteNurseTodo(ids) {
  return request({
    url: `/nursereminder/todo/${ids}`,
    method: 'delete'
  })
}

//机构综合查房记录-新增查房
export function getOrgCheckList(data) {
  return request({
    url: '/roomnurserec/orgrecord',
    method: 'post',
    data: data
  })
}
//机构综合查房记录-查询查房记录
export function getOrgCheckListData(data) {
  return request({
    url: '/roomnurserec/orgrecord/list',
    method: 'get',
    params: data
  })
}
//机构综合查房记录-修改查房记录
export function updateOrgCheckList(data) {
  return request({
    url: '/roomnurserec/orgrecord',
    method: 'put',
    data: data
  })
}
//机构综合查房记录-删除查房记录
export function deleteOrgCheckList(id) {
  return request({
    url: `/roomnurserec/orgrecord/${id}`,
    method: 'delete'
  })
}
//机构查房历史记录-查询查房记录
export function getOrgCheckHistoryList(data) {
  return request({
    url: '/roomnurserec/orgrecord/listHis',
    method: 'get',
    params: data
  })
}
//机构查房记录-查询详情
export function getOrgCheckDetail(id) {
  return request({
    url: `/roomnurserec/orgrecord/${id}`,
    method: 'get'
  })
}
// 行政查房新增
export function addAdminCheck(data) {
  return request({
    url: '/roomnurserec/xzrecord',
    method: 'post',
    data: data
  })
}
//行政查房查询
export function getAdminCheckList(data) {
  return request({
    url: '/roomnurserec/xzrecord/list',
    method: 'get',
    params: data
  })
}
//行政查房查询详情
export function getAdminCheckDetail(id) {
  return request({
    url: `/roomnurserec/xzrecord/${id}`,
    method: 'get'
  })
}
//行政查房修改
export function updateAdminCheck(data) {
  return request({
    url: '/roomnurserec/xzrecord',
    method: 'put',
    data: data
  })
}
//行政查房删除
export function deleteAdminCheck(id) {
  return request({
    url: `/roomnurserec/xzrecord/${id}`,
    method: 'delete'
  })
}
//我的提醒
export function getMyRemindList(data) {
  return request({
    url: '/nursereminder/reminder/list',
    method: 'get',
    params: data
  })
}
//我的提醒-修改
export function updateMyRemind(data) {
  return request({
    url: '/nursereminder/reminder',
    method: 'put',
    data: data
  })
}
// 护理查房记录新增
export function addNurseCheckHuLi(data) {
  return request({
    url: '/roomnurserec/hlrecord',
    method: 'post',
    data: data
  })
  
}
// 护理查房记录查询
export function getNurseCheckHuLiList(data) {
  return request({
    url: '/roomnurserec/hlrecord/list',
    method: 'get',
    params: data
  })
}
// 护理查房记录修改
export function updateNurseCheckHuLi(data) {
  return request({
    url: '/roomnurserec/hlrecord',
    method: 'put',
    data: data
  })
}
// 护理查房记录详情
export function getNurseCheckHuLiListDetail(id) {
  return request({
    url: `/roomnurserec/hlrecord/${id}`,
    method: 'get'
  })
}
// 护理查房记录删除
export function deleteNurseCheckHuLi(id) {
  return request({
    url: `/roomnurserec/hlrecord/${id}`,
    method: 'delete'
  })
}
//历史记录-和孚护理查房记录
export function getNurseCheckHuLiListHistory(data) {
  return request({
    url: '/roomnurserec/hfrecord/listHis',
    method: 'get',
    params: data
  })
}
//历史记录-和孚护理查房记录详情
export function getNurseCheckHuLiListHistoryDetail(id) {
  return request({
    url: `/roomnurserec/hfrecord/${id}`,
    method: 'get'
  })
}

// 护士工作台=工作记录
export function getNurseWorkStation(data) {
  return request({
    url: '/nurseWorkstation/getWorkRec',
    method: 'get',
    params: data
  })
}
// 护理组长查房记录-新增
export function hlzcNurseRecordAdd(data) {
  return request({
    url: '/roomnurserec/hlzzrecord',
    method: 'post',
    data: data
  })
}
// 护理组长查房记录-查询
export function hlzcNurseRecordList(data) {
  return request({
    url: '/roomnurserec/hlzzrecord/list',
    method: 'get',
    params: data
  })
}
// 护理组长查房记录-修改
export function hlzcNurseRecordEdit(data) {
  return request({
    url: '/roomnurserec/hlzzrecord',
    method: 'put',
    data: data
  })
}
// 护理组长查房记录-删除
export function hlzcNurseRecordDel(id) {
  return request({
    url: `/roomnurserec/hlzzrecord/${id}`,
    method: 'delete'
  })
}
// 护理组长查房记录-详情
export function hlzcNurseRecordDetail(id) {
  return request({
    url: `/roomnurserec/hlzzrecord/${id}`,
    method: 'get'
  })
}