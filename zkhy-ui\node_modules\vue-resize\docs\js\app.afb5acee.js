(function(e){function t(t){for(var i,s,a=t[0],c=t[1],u=t[2],f=0,l=[];f<a.length;f++)s=a[f],r[s]&&l.push(r[s][0]),r[s]=0;for(i in c)Object.prototype.hasOwnProperty.call(c,i)&&(e[i]=c[i]);d&&d(t);while(l.length)l.shift()();return o.push.apply(o,u||[]),n()}function n(){for(var e,t=0;t<o.length;t++){for(var n=o[t],i=!0,a=1;a<n.length;a++){var c=n[a];0!==r[c]&&(i=!1)}i&&(o.splice(t--,1),e=s(s.s=n[0]))}return e}var i={},r={app:0},o=[];function s(t){if(i[t])return i[t].exports;var n=i[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,s),n.l=!0,n.exports}s.m=e,s.c=i,s.d=function(e,t,n){s.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},s.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},s.t=function(e,t){if(1&t&&(e=s(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(s.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)s.d(n,i,function(t){return e[t]}.bind(null,i));return n},s.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return s.d(t,"a",t),t},s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},s.p="/vue-resize/";var a=window["webpackJsonp"]=window["webpackJsonp"]||[],c=a.push.bind(a);a.push=t,a=a.slice();for(var u=0;u<a.length;u++)t(a[u]);var d=c;o.push([0,"chunk-vendors"]),n()})({0:function(e,t,n){e.exports=n("56d7")},"56d7":function(e,t,n){"use strict";n.r(t);n("cadf"),n("551c"),n("097d"),n("a670");var i=n("2b0e"),r=n("ddc1"),o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{attrs:{id:"app"}},[n("div",{staticClass:"counter"},[e._v("Resize: "+e._s(e.count))]),n("div",[n("button",{staticClass:"toggle",on:{click:function(t){e.show=!e.show}}},[e._v("Toggle")]),n("button",{staticClass:"change",on:{click:e.changeSize}},[e._v("Change CSS size")])]),e.show?n("div",{staticClass:"resized"},[n("textarea",{style:{width:e.width+"px"}}),n("resize-observer",{on:{notify:e.handleResize}})],1):e._e()])},s=[],a={name:"app",data:function(){return{count:0,show:!0,width:300}},methods:{handleResize:function(){this.count++,console.log("handle")},changeSize:function(){this.width=Math.round(400*Math.random())+100}}},c=a,u=(n("7faf"),n("2877")),d=Object(u["a"])(c,o,s,!1,null,null,null);d.options.__file="App.vue";var f=d.exports;i["a"].use(r["b"]),console.log(r["b"],r["c"],r["a"]),new i["a"]({el:"#app",render:function(e){return e(f)}})},"7faf":function(e,t,n){"use strict";var i=n("8fba"),r=n.n(i);r.a},"8fba":function(e,t,n){},a670:function(e,t,n){},ddc1:function(e,t,n){"use strict";(function(e){function i(){var e=window.navigator.userAgent,t=e.indexOf("MSIE ");if(t>0)return parseInt(e.substring(t+5,e.indexOf(".",t)),10);var n=e.indexOf("Trident/");if(n>0){var i=e.indexOf("rv:");return parseInt(e.substring(i+3,e.indexOf(".",i)),10)}var r=e.indexOf("Edge/");return r>0?parseInt(e.substring(r+5,e.indexOf(".",r)),10):-1}n.d(t,"c",function(){return a}),n.d(t,"a",function(){return s});var r=void 0;function o(){o.init||(o.init=!0,r=-1!==i())}var s={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"resize-observer",attrs:{tabindex:"-1"}})},staticRenderFns:[],_scopeId:"data-v-b329ee4c",name:"resize-observer",methods:{compareAndNotify:function(){this._w===this.$el.offsetWidth&&this._h===this.$el.offsetHeight||(this._w=this.$el.offsetWidth,this._h=this.$el.offsetHeight,this.$emit("notify"))},addResizeHandlers:function(){this._resizeObject.contentDocument.defaultView.addEventListener("resize",this.compareAndNotify),this.compareAndNotify()},removeResizeHandlers:function(){this._resizeObject&&this._resizeObject.onload&&(!r&&this._resizeObject.contentDocument&&this._resizeObject.contentDocument.defaultView.removeEventListener("resize",this.compareAndNotify),delete this._resizeObject.onload)}},mounted:function(){var e=this;o(),this.$nextTick(function(){e._w=e.$el.offsetWidth,e._h=e.$el.offsetHeight});var t=document.createElement("object");this._resizeObject=t,t.setAttribute("aria-hidden","true"),t.setAttribute("tabindex",-1),t.onload=this.addResizeHandlers,t.type="text/html",r&&this.$el.appendChild(t),t.data="about:blank",r||this.$el.appendChild(t)},beforeDestroy:function(){this.removeResizeHandlers()}};function a(e){e.component("resize-observer",s),e.component("ResizeObserver",s)}var c={version:"0.4.4",install:a},u=null;"undefined"!==typeof window?u=window.Vue:"undefined"!==typeof e&&(u=e.Vue),u&&u.use(c),t["b"]=c}).call(this,n("c8ba"))}});
//# sourceMappingURL=app.afb5acee.js.map