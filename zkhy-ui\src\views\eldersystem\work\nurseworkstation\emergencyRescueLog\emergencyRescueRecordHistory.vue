<template>
    <div class="log-review-container">
      <!-- 返回工作台按钮 -->
      <el-button type="primary" @click="goBack">
        返回工作台
      </el-button>
  
      <!-- 查询表单 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form" label-width="120px">
        <el-form-item label="意外发生日期" prop="logDate">
          <el-date-picker
            style="width: 150px;"
            v-model="queryParams.logDate"
            type="date"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
            clearable
          />
        </el-form-item>
  
        <el-form-item label="老人姓名" prop="elderName">
          <el-input
            style="width: 150px;"
            v-model="queryParams.elderName"
            placeholder="请输入"
            clearable
          />
        </el-form-item>
        <el-form-item label="记录人" prop="nurseName">
          <el-input
            style="width: 150px;"
            v-model="queryParams.nurseName"
            placeholder="请输入"
            clearable
          />
        </el-form-item>
  
        <el-form-item>
          <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
          <el-button @click="resetQuery" icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
  
      <!-- 表格 -->
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column prop="id" label="序号" width="80" align="center" />
        <el-table-column prop="logDate" label="意外发生日期" width="150" align="center" />
        <el-table-column prop="nurseName" label="老人姓名" min-width="120" align="center" />
        <el-table-column prop="nurseName" label="老人性别" min-width="120" align="center" />
        <el-table-column prop="nurseName" label="老人年龄" min-width="120" align="center" />
        <el-table-column prop="nurseName" label="房间号" min-width="120" align="center" />
        <el-table-column prop="nurseName" label="意外发生地址" min-width="120" align="center" />
        <el-table-column prop="department" label="当天护理员" min-width="120" align="center" />
        <el-table-column prop="reviewStatus" label="记录人" width="120" align="center"></el-table-column>
        <el-table-column prop="reviewStatus" label="记录时间" width="120" align="center"></el-table-column>
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="showDetail(row)">查看</el-button>
            <el-button type="primary" link @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
  
      <!-- 分页 -->
      <div class="paginationBox">
        <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParams.pageNum"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="queryParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />

      </div>
      <!-- 详情对话框 -->
      <el-dialog title="查看" v-model="detailVisible" width="50%">
        <div v-if="currentDetail" ref="printContent">
          <div class="nurse-log">
                <h2 class="titleLog">老人意外情况记录表</h2>
                <table class="table-style">
            <tbody>
                <tr>
                    <td style="text-align: left;">老人姓名:</td>
                    <td style="text-align: left;">老人性别:</td>
                    <td style="text-align: left;">老人年龄：
                    </td>
                </tr>
                <tr>
                    <td style="text-align: left;">房间信息:</td>
                    <td style="text-align: left;">入住时间：</td>
                    <td style="text-align: left;">能力等级：
                    </td>
                </tr>
                <tr>
                    <td style="text-align: left;">护理等级:</td>
                    <td style="text-align: left;">照护等级：</td>
                    <td style="text-align: left;">当天护理员：
                    </td>
                </tr>
                <tr>
                    <td style="text-align: left;" colspan="3">意外发生时间</td>
                </tr>
                <tr>
                    <td style="text-align: left;" colspan="3">意外发生地址</td>
                </tr>
                <tr>
                    <td style="text-align: left;" colspan="3">伤情描述</td>
                </tr>
                <tr>
                    <td style="text-align: left;" colspan="3">身体处置情况</td>
                </tr>
                <tr>
                    <td style="text-align: left;" colspan="3">生命体征情况</td>
                </tr>
                <tr>
                    <td style="text-align: left;" colspan="3">送往医院方式及医院名称</td>
                </tr>
                <tr>
                    <td style="text-align: left;" colspan="3">通知监护人情况</td>
                </tr>
                <tr>
                    <td style="text-align: left;" colspan="3">发生意外情况描述</td>
                </tr>
                <tr>
                    <td style="text-align: left;" colspan="3">意外处置参与人员</td>
                </tr>
                <tr>
                    <td style="text-align: left;" colspan="3">谈话记录</td>
                </tr>
            </tbody>
        </table>
            </div>
        </div>
        <template #footer>
          <el-button type="primary" @click="detailVisible = false" plain>返回</el-button>
          <el-button type="default" @click="handlePrint" plain>打印</el-button>
        </template>
      </el-dialog>
    </div>
  </template>
  
  <script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
   // 查询参数
   const queryParams = reactive({
        logDate: '',
        department: '',
        reviewStatus: '',
        nurseName: '',
        pageNum: 1,
        pageSize: 10
      })
      const router = useRouter()
      // 表格数据
      const tableData = ref([])
      const total = ref(0)
      const detailVisible = ref(false)
      const currentDetail = ref(null)
      const printContent = ref(null)
      // 模拟数据
      const mockData = [
        { id: 1, logDate: '2025-6-26', nurseName: '王护士', department: '护士部', reviewStatus: '未审阅' },
        { id: 2, logDate: '2025-6-26', nurseName: '王护士', department: '护士部', reviewStatus: '未审阅' },
        { id: 3, logDate: '2025-6-25', nurseName: '王护士', department: '护士部', reviewStatus: '未审阅' },
        { id: 4, logDate: '2025-6-24', nurseName: '王护士', department: '护士部', reviewStatus: '未审阅' },
        { id: 5, logDate: '2025-6-23', nurseName: '王护士', department: '护士部', reviewStatus: '已审阅' },
        { id: 6, logDate: '2025-6-22', nurseName: '王护士', department: '护士部', reviewStatus: '已审阅' },
        { id: 7, logDate: '2025-6-21', nurseName: '李护士', department: '护士部', reviewStatus: '已审阅' },
        { id: 8, logDate: '2025-6-20', nurseName: '李护士', department: '护士部', reviewStatus: '已审阅' },
        { id: 9, logDate: '2025-6-19', nurseName: '李护士', department: '护士部', reviewStatus: '已审阅' },
        { id: 10, logDate: '2025-6-18', nurseName: '李护士', department: '护士部', reviewStatus: '已审阅' },
      ]
  
      // 获取表格数据
      const getList = () => {
        // 模拟API请求
        return new Promise(resolve => {
          setTimeout(() => {
            // 过滤数据
            let filteredData = [...mockData]
            
            if (queryParams.logDate) {
              filteredData = filteredData.filter(item => item.logDate === queryParams.logDate)
            }
            
            if (queryParams.department) {
              filteredData = filteredData.filter(item => item.department === queryParams.department)
            }
            
            if (queryParams.reviewStatus) {
              filteredData = filteredData.filter(item => item.reviewStatus === queryParams.reviewStatus)
            }
            
            if (queryParams.nurseName) {
              filteredData = filteredData.filter(item => 
                item.nurseName.includes(queryParams.nurseName)
              )
            }
            
            // 分页
            const start = (queryParams.pageNum - 1) * queryParams.pageSize
            const end = start + queryParams.pageSize
            const pageData = filteredData.slice(start, end)
            
            tableData.value = pageData
            total.value = filteredData.length
            resolve()
          }, 300)
        })
      }
  
      // 查询
      const handleQuery = () => {
        queryParams.pageNum = 1
        getList()
      }
  
      // 重置
      const resetQuery = () => {
        queryParams.logDate = ''
        queryParams.department = ''
        queryParams.reviewStatus = ''
        queryParams.nurseName = ''
        queryParams.pageNum = 1
        getList()
      }
  
      // 分页
      const handleSizeChange = (val) => {
        queryParams.pageSize = val
        getList()
      }
  
      const handleCurrentChange = (val) => {
        queryParams.pageNum = val
        getList()
      }
  
      // 详情
      const showDetail = (row) => {
        currentDetail.value = row
        detailVisible.value = true
      }
  
      // 删除
      const handleDelete = (row) => {
        ElMessageBox.confirm('确定删除该老人意外情况记录表吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 这里应该是调用删除API
          ElMessage.success('删除成功')
          getList()
        }).catch(() => {
          ElMessage.info('已取消删除')
        })
      }
      const handlePrint = () => {
        // 克隆要打印的节点
    const content = printContent.value.cloneNode(true)
    
    // 移除所有输入元素的交互特性
    const inputs = content.querySelectorAll('.el-input, .el-textarea')
    inputs.forEach(input => {
      // 替换为纯文本显示
      const text = input.querySelector('input, textarea')?.value || ''
      const textNode = document.createElement('div')
      textNode.textContent = text
      textNode.style.padding = '8px'
      input.replaceWith(textNode)
    })
    
    // 创建打印窗口
    const printWindow = window.open('', '_blank')
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>老人意外情况记录表</title>
          <style>
            body { font-family: Arial; padding: 20px; }
            .title_record { 
              color: #D9001B; 
              text-align: center; 
              font-size: 20px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .table-style {
              width: 100%;
              border-collapse: collapse;
            }
            .table-style td {
              border: 1px solid #ebeef5;
              padding: 8px;
              color:#606266;
            }
            .titleLog {
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 20px;
                color: #D9001B;
                text-align: center;
            }
            .text-center { text-align: center; }
          </style>
        </head>
        <body>
          ${content.innerHTML}
          <script>
            setTimeout(() => {
              window.print()
              window.close()
            }, 200)
          <\/script>
        </body>
      </html>
    `)
    printWindow.document.close()
      }
      // 返回工作台
      const goBack = () => {
        router.push('/work/nurseworkstation')
      }
  
      // 初始化
      onMounted(() => {
        getList()
      })
 
  </script>
  
  <style scoped>
  .log-review-container {
    padding: 20px;
  }
  
  .back-btn {
    margin-bottom: 20px;
    padding-left: 0;
  }
  
  .search-form {
    margin-bottom: 20px;
  }
  
  .paginationBox {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  .nurse-log {
    .titleLog {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #D9001B;
        text-align: center;
    }
}

.table-style {
    border: 1px solid #ebeef5;
    border-collapse: collapse;
    width: 100%;

    td {
        border: 1px solid #ebeef5;
        padding: 8px;
        font-size: 14px;
    }
}
.tdColor{
    color:#D9001B
}
  </style>