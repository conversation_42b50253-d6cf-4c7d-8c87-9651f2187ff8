<template>
  <div class="app-container home">
    <div class="main-title">欢迎使用</div>
    <div class="subtitle">颐家通智慧养老系统平台</div>
    <div class="card-row">
      <!-- 卡片1 -->
      <div class="card" @click="openElderInfo">
        <div class="card-icon">
          <!-- 图书/档案 SVG -->
          <svg width="44" height="44" viewBox="0 0 48 48" fill="none">
            <rect
              x="8"
              y="8"
              width="32"
              height="32"
              rx="3"
              stroke="#4989f8"
              stroke-width="3"
            />
            <path
              d="M16 16H32V32"
              stroke="#4989f8"
              stroke-width="3"
              stroke-linecap="round"
            />
          </svg>
        </div>
        <div class="card-title">老人档案</div>
        <div class="card-desc">Health records</div>
        <div class="card-wave">
          <svg viewBox="0 0 180 38" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M0 24C30 36 60 36 90 24C120 12 150 12 180 24V38H0V24Z"
              fill="#eaf4ff"
            />
          </svg>
        </div>
      </div>
      <!-- 卡片2 -->
      <div class="card" @click="openHealthAssessment">
        <div class="card-icon">
          <!-- 评估/折线 SVG -->
          <svg width="44" height="44" viewBox="0 0 48 48" fill="none">
            <rect
              x="8"
              y="8"
              width="32"
              height="32"
              rx="3"
              stroke="#34bfa3"
              stroke-width="3"
            />
            <path
              d="M16 32L24 24L32 28"
              stroke="#34bfa3"
              stroke-width="3"
              stroke-linecap="round"
            />
            <circle cx="16" cy="32" r="2.5" fill="#34bfa3" />
            <circle cx="24" cy="24" r="2.5" fill="#34bfa3" />
            <circle cx="32" cy="28" r="2.5" fill="#34bfa3" />
          </svg>
        </div>
        <div class="card-title">健康评估</div>
        <div class="card-desc">Health assessment</div>
        <div class="card-wave">
          <svg viewBox="0 0 180 38" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M0 24C30 36 60 36 90 24C120 12 150 12 180 24V38H0V24Z"
              fill="#eaf4ff"
            />
          </svg>
        </div>
      </div>
      <!-- 卡片3 -->
      <div class="card" @click="openHealthProgramme">
        <div class="card-icon">
          <!-- 居住/表单 SVG -->
          <svg width="44" height="44" viewBox="0 0 48 48" fill="none">
            <rect
              x="8"
              y="8"
              width="32"
              height="32"
              rx="3"
              stroke="#4989f8"
              stroke-width="3"
            />
            <rect x="16" y="16" width="16" height="4" rx="2" fill="#4989f8" />
            <rect x="16" y="24" width="8" height="4" rx="2" fill="#4989f8" />
          </svg>
        </div>
        <div class="card-title">居住管理</div>
        <div class="card-desc">Health programme</div>
        <div class="card-wave">
          <svg viewBox="0 0 180 38" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M0 24C30 36 60 36 90 24C120 12 150 12 180 24V38H0V24Z"
              fill="#eaf4ff"
            />
          </svg>
        </div>
      </div>
      <!-- 卡片4 -->
      <div class="card">
        <div class="card-icon">
          <!-- 药品/分配 SVG -->
          <svg width="44" height="44" viewBox="0 0 48 48" fill="none">
            <rect
              x="8"
              y="8"
              width="32"
              height="32"
              rx="3"
              stroke="#34bfa3"
              stroke-width="3"
            />
            <path
              d="M18 18L30 30"
              stroke="#34bfa3"
              stroke-width="3"
              stroke-linecap="round"
            />
            <path
              d="M30 18L18 30"
              stroke="#34bfa3"
              stroke-width="3"
              stroke-linecap="round"
            />
          </svg>
        </div>
        <div class="card-title">药品管理</div>
        <div class="card-desc">Task allocation</div>
        <div class="card-wave">
          <svg viewBox="0 0 180 38" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M0 24C30 36 60 36 90 24C120 12 150 12 180 24V38H0V24Z"
              fill="#eaf4ff"
            />
          </svg>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="Index">
const version = ref("3.8.9");
const router = useRouter();
function goTarget(url) {
  window.open(url, "__blank");
}

function openElderInfo() {
  console.log("-----");
  router.push("/elderInfo/elderFiles");
}
function openHealthAssessment() {
  console.log("---22--");
  router.push("/assessment/assessmentRecord");
}

function openHealthProgramme() {
  console.log("--33---");
  router.push("/live/roommanage");
}
</script>

<style scoped lang="scss">
.home {
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }
  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }
  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }
}

body {
  background: #fafbfc;
  min-height: 100vh;
  margin: 0;
  font-family: "Segoe UI", "Microsoft YaHei", Arial, sans-serif;
}
.main-title {
  text-align: center;
  color: #4989f8;
  font-size: 2.5rem;
  font-weight: bold;
  margin-top: 90px;
  margin-bottom: 28px;
  letter-spacing: 2px;
}
.subtitle {
  text-align: center;
  color: #4a5ea7;
  font-size: 1.5rem;
  margin-bottom: 90px;
  letter-spacing: 3px;
}
.card-row {
  display: flex;
  justify-content: center;
  gap: 48px;
  margin-top: 0;
  flex-wrap: wrap;
}
.card {
  width: 220px;
  height: 260px;
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 8px 24px rgba(80, 120, 220, 0.1), 0 2px 8px rgba(80, 120, 220, 0.08);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 38px 0 0 0;
  margin-bottom: 38px;
  position: relative;
  transition: box-shadow 0.23s, transform 0.23s;
}
.card:hover {
  box-shadow: 0 16px 48px rgba(80, 120, 220, 0.18), 0 4px 16px rgba(80, 120, 220, 0.16);
  transform: translateY(-8px) scale(1.045);
  z-index: 2;
}
.card-icon {
  margin-bottom: 30px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.card-icon svg {
  width: 56px;
  height: 56px;
}
.card-title {
  color: #4989f8;
  font-size: 1.35rem;
  font-weight: bold;
  margin-bottom: 10px;
  letter-spacing: 1.5px;
}
.card-desc {
  color: #888;
  font-size: 1.05rem;
  margin-bottom: 38px;
  letter-spacing: 0.5px;
}
.card-wave {
  width: 100%;
  height: 52px;
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 1;
}
.card-wave svg {
  width: 100%;
  height: 100%;
}
@media (max-width: 900px) {
  .card-row {
    gap: 24px;
  }
  .card {
    width: 44vw;
    min-width: 150px;
    max-width: 220px;
  }
}
@media (max-width: 600px) {
  .main-title {
    font-size: 1.3rem;
    margin-top: 40px;
  }
  .subtitle {
    font-size: 1rem;
    margin-bottom: 30px;
  }
  .card-row {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }
  .card {
    width: 90vw;
    min-width: 0;
    max-width: 100vw;
  }
}
</style>
