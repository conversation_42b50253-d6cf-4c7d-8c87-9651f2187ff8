<template>
    <div class="log-review-container">
      <!-- 返回工作台按钮 -->
      <el-button type="primary" @click="goBack">
        返回工作台
      </el-button>
  
      <!-- 查询表单 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form" label-width="90px">
        <el-form-item label="日志日期：" prop="logDate">
          <el-date-picker
            style="width: 150px;"
            v-model="queryParams.logDate"
            type="date"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
            clearable
          />
        </el-form-item>
  
        <el-form-item label="所属部门：" prop="department">
          <el-select v-model="queryParams.department" placeholder="全部" clearable style="width: 150px;">
            <el-option label="全部" value="" />
            <el-option label="护士部" value="护士部" />
            <el-option label="医生部" value="医生部" />
            <el-option label="行政部" value="行政部" />
          </el-select>
        </el-form-item>
  
        <el-form-item label="审阅状态：" prop="reviewStatus">
          <el-select v-model="queryParams.reviewStatus" placeholder="全部" clearable style="width: 150px;">
            <el-option label="全部" value="" />
            <el-option label="已审阅" value="已审阅" />
            <el-option label="未审阅" value="未审阅" />
          </el-select>
        </el-form-item>
  
        <el-form-item label="护士姓名：" prop="nurseName">
          <el-input
            style="width: 150px;"
            v-model="queryParams.nurseName"
            placeholder="请输入"
            clearable
          />
        </el-form-item>
  
        <div style="text-align: right;">
          <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
          <el-button @click="resetQuery" icon="Refresh">重置</el-button>
        </div>
      </el-form>
  
      <!-- 表格 -->
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column prop="id" label="序号" width="80" align="center" />
        <el-table-column prop="logDate" label="日志日期" min-width="120" align="center" />
        <el-table-column prop="nurseName" label="护士姓名" min-width="120" align="center" />
        <el-table-column prop="department" label="所属部门" min-width="120" align="center" />
        <el-table-column prop="reviewStatus" label="院长审阅" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="row.reviewStatus === '已审阅' ? 'success' : 'warning'">
              {{ row.reviewStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="showDetail(row)">详情</el-button>
            <el-button type="primary" link @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
  
      <!-- 分页 -->
      <div class="paginationBox">
        <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParams.pageNum"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="queryParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />

      </div>
      <!-- 详情对话框 -->
      <el-dialog title="日志详情" v-model="detailVisible" width="50%">
        <div v-if="currentDetail">
          <div class="nurse-log">
                <h2 class="titleLog">护士日志</h2>
                <table class="table-style">
                    <tbody>
                        <tr>
                            <td style="text-align: center;width: 150px;">所属部门:护士部</td>
                            <td style="text-align: center;">护士姓名：王</td>
                            <td style="text-align: center;">日志日期：2222-02-02</td>
                        </tr>
                        <tr>
                            <td style="text-align: center;">工作内容</td>
                            <td colspan="2">
                                1.22222
                            </td>
                        </tr>
                        <tr>
                            <td style="text-align: center;">工作计划</td>
                            <td colspan="2">
                                1.22222
                            </td>
                        </tr>
                        <tr>
                            <td style="text-align: center;">工作建议</td>
                            <td colspan="2">
                                1.22222
                            </td>
                        </tr>
                        <tr>
                            <td style="text-align: center;">院长审阅</td>
                            <td colspan="2">
                                <el-button type="primary" link>已审阅</el-button>
                            </td>
                        </tr>
                        <tr>
                            <td></td>
                            <td colspan="2" class="tdColor">请王护士着重汇报每个老人的心理状态，以及近期子女反应情况，另外增加对护理人员专业知识辅导，加强楼道卫生检查等工作</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <template #footer>
          <el-button type="primary" @click="detailVisible = false" plain>返回</el-button>
        </template>
      </el-dialog>
    </div>
  </template>
  
  <script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
   // 查询参数
   const queryParams = reactive({
        logDate: '',
        department: '',
        reviewStatus: '',
        nurseName: '',
        pageNum: 1,
        pageSize: 10
      })
      const router = useRouter()
      // 表格数据
      const tableData = ref([])
      const total = ref(0)
      const detailVisible = ref(false)
      const currentDetail = ref(null)
  
      // 模拟数据
      const mockData = [
        { id: 1, logDate: '2025-6-26', nurseName: '王护士', department: '护士部', reviewStatus: '未审阅' },
        { id: 2, logDate: '2025-6-26', nurseName: '王护士', department: '护士部', reviewStatus: '未审阅' },
        { id: 3, logDate: '2025-6-25', nurseName: '王护士', department: '护士部', reviewStatus: '未审阅' },
        { id: 4, logDate: '2025-6-24', nurseName: '王护士', department: '护士部', reviewStatus: '未审阅' },
        { id: 5, logDate: '2025-6-23', nurseName: '王护士', department: '护士部', reviewStatus: '已审阅' },
        { id: 6, logDate: '2025-6-22', nurseName: '王护士', department: '护士部', reviewStatus: '已审阅' },
        { id: 7, logDate: '2025-6-21', nurseName: '李护士', department: '护士部', reviewStatus: '已审阅' },
        { id: 8, logDate: '2025-6-20', nurseName: '李护士', department: '护士部', reviewStatus: '已审阅' },
        { id: 9, logDate: '2025-6-19', nurseName: '李护士', department: '护士部', reviewStatus: '已审阅' },
        { id: 10, logDate: '2025-6-18', nurseName: '李护士', department: '护士部', reviewStatus: '已审阅' },
      ]
  
      // 获取表格数据
      const getList = () => {
        // 模拟API请求
        return new Promise(resolve => {
          setTimeout(() => {
            // 过滤数据
            let filteredData = [...mockData]
            
            if (queryParams.logDate) {
              filteredData = filteredData.filter(item => item.logDate === queryParams.logDate)
            }
            
            if (queryParams.department) {
              filteredData = filteredData.filter(item => item.department === queryParams.department)
            }
            
            if (queryParams.reviewStatus) {
              filteredData = filteredData.filter(item => item.reviewStatus === queryParams.reviewStatus)
            }
            
            if (queryParams.nurseName) {
              filteredData = filteredData.filter(item => 
                item.nurseName.includes(queryParams.nurseName)
              )
            }
            
            // 分页
            const start = (queryParams.pageNum - 1) * queryParams.pageSize
            const end = start + queryParams.pageSize
            const pageData = filteredData.slice(start, end)
            
            tableData.value = pageData
            total.value = filteredData.length
            resolve()
          }, 300)
        })
      }
  
      // 查询
      const handleQuery = () => {
        queryParams.pageNum = 1
        getList()
      }
  
      // 重置
      const resetQuery = () => {
        queryParams.logDate = ''
        queryParams.department = ''
        queryParams.reviewStatus = ''
        queryParams.nurseName = ''
        queryParams.pageNum = 1
        getList()
      }
  
      // 分页
      const handleSizeChange = (val) => {
        queryParams.pageSize = val
        getList()
      }
  
      const handleCurrentChange = (val) => {
        queryParams.pageNum = val
        getList()
      }
  
      // 详情
      const showDetail = (row) => {
        currentDetail.value = row
        detailVisible.value = true
      }
  
      // 删除
      const handleDelete = (row) => {
        ElMessageBox.confirm('确定删除该护士日志吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 这里应该是调用删除API
          ElMessage.success('删除成功')
          getList()
        }).catch(() => {
          ElMessage.info('已取消删除')
        })
      }
  
      // 返回工作台
      const goBack = () => {
        router.push('/work/nurseworkstation')
      }
  
      // 初始化
      onMounted(() => {
        getList()
      })
 
  </script>
  
  <style scoped>
  .log-review-container {
    padding: 20px;
  }
  
  .back-btn {
    margin-bottom: 20px;
    padding-left: 0;
  }
  
  .search-form {
    margin-bottom: 20px;
  }
  
  .paginationBox {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  .nurse-log {
    .titleLog {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #D9001B;
        text-align: center;
    }
}

.table-style {
    border: 1px solid #ebeef5;
    border-collapse: collapse;
    width: 100%;

    td {
        border: 1px solid #ebeef5;
        padding: 8px;
        font-size: 14px;
    }
}
.tdColor{
    color:#D9001B
}
  </style>