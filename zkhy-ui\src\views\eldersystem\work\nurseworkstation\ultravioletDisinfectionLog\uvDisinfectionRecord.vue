<template>
    <div class="replace-consumables">
      <el-button type="primary" @click="goBack">返回工作台</el-button>
      <div class="headerTitle">
          <h2>紫外线消毒记录表</h2>
      </div>
      <div style="text-align: right;">
        <el-button type="primary" @click="addNewElder">+ 新增房间</el-button>
      </div>
      <el-table :data="tableData" border style="width: 100%">
        <!-- 房屋信息列 -->
        <el-table-column label="房屋信息" width="200" align="center" prop="avatar">
          <template #default="scope">
            <div class="elder-info">
              <div class="info">                
                <p class="roomNumber">{{ scope.row.room }}</p>
                <p class="leaderName">{{ scope.row.name }} {{ scope.row.floorNumber }}</p>
                <span class="processIndex">{{ scope.$index + 1 }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="serviceDate" label="服务日期" min-width="230" align="center">
           <template #default="scope">
               <el-date-picker v-model="scope.row.serviceDate" type="date" placeholder="选择日期" style="width: 200px;"></el-date-picker>
           </template>
        </el-table-column>
        <el-table-column prop="ultravioletLampNumber" label="紫外灯编号" width="180" align="center">
          <template #default="scope">
            <el-input v-model="scope.row.ultravioletLampNumber" placeholder="请输入紫外灯编号" style="width: 150px;"></el-input>
          </template>
        </el-table-column>         
        <el-table-column prop="disinfectionTime" label="消毒时间" width="180" align="center">
          <template #default="scope">
            <el-time-picker
            v-model="scope.row.disinfectionTime"
            is-range
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width: 150px;"
            format="HH:mm"
            value-format="HH:mm"
            />          
          </template>
        </el-table-column>
        <el-table-column prop="disinfectionDuration" label="消毒时长" width="150" align="center">
          <template #default="scope">
            <el-input v-model="scope.row.disinfectionDuration" placeholder="≥30分钟" style="width: 120px;"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="irradiationIntensityResults" label="辐照强度结果" width="130" align="center">
          <template #default="scope">
            <el-input v-model="scope.row.irradiationIntensityResults" style="width: 100px;" placeholder="请输入"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="disinfectionPersonnel" label="消毒人员" width="130" align="center">
          <template #default="scope">
            <el-input v-model="scope.row.disinfectionPersonnel" style="width: 100px;" placeholder="请输入"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="supervisors" label="监督人员" min-width="140" align="center">
          <template #default="scope">
            <el-input v-model="scope.row.supervisors" placeholder="请输入"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="disinfectionArea" label="消毒区域" min-width="200" align="center">
          <template #default="scope">
            <el-input v-model="scope.row.disinfectionArea" placeholder="请输入" :rows="2" type="textarea"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="200" align="center">
          <template #default="scope">
            <el-input v-model="scope.row.remark" placeholder="请输入" :rows="2" type="textarea"></el-input>
          </template>
        </el-table-column>
        <!-- 操作列 -->
        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template #default="scope">
            <el-button type="danger" :icon="Delete" circle  @click.stop="deleteServiceRecord(scope.$index)"/>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top: 20px;text-align: center;">
        <el-button type="primary" @click="submit">提交</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
      <el-dialog v-model="visible" title="新增房间" width="70%" :close-on-click-modal="false" append-to-body>
                    <el-form ref="formRef" :model="formRoom" :rules="rules" label-width="120px" label-position="left">
                        <div class="room_info_top">
                            <div class="title_room">
                                <h3>房间信息</h3>
                            </div>
                            <div class="room_form">
                                <el-row :gutter="24">
                                    <el-col :span="8">
                                        <el-form-item label="查房日期" prop="handoverDate">
                                            <el-date-picker v-model="formRoom.handoverDate" type="date" placeholder="选择日期" style="width: 200px" value-format="YYYY-MM-DD"></el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="楼栋信息" prop="buildingId">
                                            <el-select v-model="formRoom.buildingId" style="width: 200px" @change="getFloorListData">
                                                <el-option v-for="item in buildingList" :key="item.value" :label="item.buildingName" :value="item.id" />
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="楼栋层数" prop="floorId">
                                            <el-select v-model="formRoom.floorId" :disabled="!formRoom.buildingId" style="width: 200px" @change="getRoomListData">
                                                <el-option v-for="item in floorList" :key="item.value" :label="item.floorName" :value="item.id" />
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="24">
                                    <div class="roomList">
                                        <el-check-tag 
                                            v-for="item in roomOptions" 
                                            :key="item.id"  
                                            :checked="selectedRooms.some(r => r.roomId === item.id)"
                                            @change="handleRoomChange(item)"
                                        >
                                            {{ item.roomName }}
                                        </el-check-tag>
                                    </div>
                                </el-row>
                            </div>
                        </div>
                </el-form>
                <!-- 底部按钮 -->
                <template #footer>
                    <el-button type="primary" @click="handleSubmit()">提交</el-button>
                    <el-button @click="visible = false">取消</el-button>
                </template>

                </el-dialog>
    </div>
  </template>
  
  <script setup>
  import { ref, computed } from 'vue';
  import { getBuildingList, getFloorList } from '@/api/live/roommanage'
  import { listRoom } from "@/api/roominfo/tLiveRoom";
  import {
    Delete,
    } from '@element-plus/icons-vue'    
  import moment from 'moment'
  const router = useRouter()
  const visible = ref(false)
  const formRoom = ref({})
  const buildingList = ref([])//楼栋下拉列表
  const floorList = ref([])//楼层下拉列表
  const roomOptions = ref([])//房间
  const selectedRooms = ref([])
  const rules = ref({
    handoverDate:[{ required: true, message: '请选择日期', trigger: 'change' }],
    buildingId:[{ required: true, message: '请选择楼栋', trigger: 'change' }],
    floorId:[{ required: true, message: '请选择楼层', trigger: 'change' }]
})
  const tableData = ref([
    {
      name: '和孚楼',
      floorNumber: '1层',
      room: '301',
      id:"1",
      serviceDate: moment().format('YYYY-MM-DD'),
      ultravioletLampNumber:"",
      disinfectionTime:"",
      disinfectionDuration:"",
      irradiationIntensityResults:"",
      disinfectionPersonnel:"",
      supervisors:"",
      disinfectionArea:"",
      remark:"",
    },
    // 更多默认数据项...
  ]);

  const goBack = () => {
    router.push('/work/nurseworkstation')
    }
  // 添加新老人
  const addNewElder = () => {
    visible.value = true;
  };
  const getBuildingListData = async () => {    
    const res = await getBuildingList()
    buildingList.value = res.rows || []
}
  const getFloorListData =async (val) =>{    
    floorList.value = []
    selectedRooms.value = []
    roomOptions.value = []
    formRoom.value.floorId = ""
    const res = await getFloorList(val)
    floorList.value = res.rows;
}
const getRoomListData =async (val) =>{
    roomOptions.value = []
    selectedRooms.value = []
    const roomsRes = await listRoom({ floorId: val })
    roomOptions.value = roomsRes.rows;
}
const handleRoomChange = (room) => {
    // 判断是否已经存在
  const index = selectedRooms.value.findIndex(r => r.roomId === room.id)
  
  if (index > -1) {
    // 如果已存在则移除
    selectedRooms.value.splice(index, 1)
  } else {
    // 如果不存在则添加
    selectedRooms.value.push({
      roomId: room.id,
      roomName: room.roomName
    })
  }
}
// 删除服务记录
const deleteServiceRecord = (index) => {
    tableData.value.splice(index, 1);
};
  
const submit = () => {
console.log('提交数据:', tableData.value);
};

const cancel = () => {
console.log('取消操作');
};
const handleSubmit = () => {
    
}
  const initRequest = () => {
    // 初始化请求数据
    tableData.value = tableData.value;
    getBuildingListData()
  }
  onMounted(() => {
    initRequest()
  })
  </script>
  
  <style scoped>
  .headerTitle{
    text-align: center;
    color:#D9001B;
  }
  .replace-consumables {
    padding: 20px;
  }
  
  .elder-info {
    display: flex;
    flex-direction: column;
    align-items: center;
     position: relative;    
    .processIndex{
        position: absolute;
        left: 2px;
        top:2px;
        color:var(--el-color-primary);
        font-weight: bold;
    }
  }
  
  .avatar {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    margin-right: 10px;
  }
  
  .info p {
    margin: 0;
  }
  .leaderName{
    color:var(--el-color-primary);
    margin:10px 0;
  }
  .service-item {
    display: flex;
  }
  .roomNumber{
    background: var(--el-color-primary);
    color:#fff;
    padding: 5px 10px;
    border-radius: 10px;
  }
  .roomList{
    display: flex;
    span{
        margin-left: 15px;
        width: 85px;
        height: 33px;
        line-height:inherit;
        text-align: center;
    }
}
  </style>