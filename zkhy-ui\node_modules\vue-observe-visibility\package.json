{"name": "vue-observe-visibility", "description": "Detect when an element is becoming visible or hidden on the page. ", "version": "0.4.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["vue", "v<PERSON><PERSON><PERSON>", "plugin"], "license": "MIT", "main": "dist/vue-observe-visibility.umd.js", "module": "dist/vue-observe-visibility.esm.js", "unpkg": "dist/vue-observe-visibility.min.js", "scripts": {"dev": "nodemon --exec 'npm run build' --watch src", "build": "npm run build:browser && npm run build:es && npm run build:umd", "build:browser": "rollup --config build/rollup.config.browser.js", "build:es": "rollup --config build/rollup.config.es.js", "build:umd": "rollup --config build/rollup.config.umd.js", "prepublishOnly": "npm run build", "serve": "serve --open ./dist"}, "repository": {"type": "git", "url": "git+https://github.com/Akryum/vue-observe-visibility.git"}, "bugs": {"url": "https://github.com/Akryum/vue-observe-visibility/issues"}, "homepage": "https://github.com/Akryum/vue-observe-visibility#readme", "devDependencies": {"@babel/core": "^7.4.0", "@babel/plugin-transform-runtime": "^7.4.0", "@babel/polyfill": "^7.4.3", "@babel/preset-env": "^7.4.2", "cross-env": "^5.2.0", "eslint": "^5.16.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-html": "^5.0.3", "eslint-plugin-import": "^2.17.2", "eslint-plugin-node": "^9.0.1", "eslint-plugin-promise": "^4.1.1", "eslint-plugin-standard": "^4.0.0", "nodemon": "^1.19.0", "rollup": "^1.7.0", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-commonjs": "^9.2.1", "rollup-plugin-node-resolve": "^4.0.1", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-terser": "^4.0.4", "serve": "^6.5.8"}}