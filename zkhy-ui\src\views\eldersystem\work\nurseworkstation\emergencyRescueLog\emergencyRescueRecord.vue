<template>
    <div class="nurse-log">
        <el-button type="primary" @click="goBack">
            返回工作台
        </el-button>
        <h2 class="titleLog">老人意外情况记录表</h2>
        <table class="table-style">
            <tbody>
                <tr>
                    <td style="text-align: center;">老人姓名:<el-input v-model="nurseLog.roomInfo" placeholder="请输入" style="width: 80%"></el-input></td>
                    <td style="text-align: center;">老人性别:<el-input v-model="nurseLog.roomInfo" placeholder="请输入" style="width: 80%"></el-input></td>
                    <td style="text-align: center;">老人年龄：<el-input v-model="nurseLog.roomInfo" placeholder="请输入" style="width: 80%"></el-input>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">房间信息:<el-input v-model="nurseLog.roomInfo" placeholder="请输入" style="width: 80%"></el-input></td>
                    <td style="text-align: center;">入住时间：<el-date-picker v-model="nurseLog.roundTime" type="date" placeholder="选择日期" style="width: 80%" value-format="YYYY-MM-DD"></el-date-picker></td>
                    <td style="text-align: center;">能力等级：<el-input v-model="nurseLog.roomInfo" placeholder="请输入" style="width: 80%"></el-input>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">护理等级:<el-input v-model="nurseLog.roomInfo" placeholder="请输入" style="width: 80%"></el-input></td>
                    <td style="text-align: center;">照护等级：<el-input v-model="nurseLog.roomInfo" placeholder="请输入" style="width: 80%"></el-input></td>
                    <td style="text-align: center;">当天护理员：<el-input v-model="nurseLog.roomInfo" placeholder="请输入" style="width: 80%"></el-input>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">意外发生时间</td>
                    <td colspan="2">
                        <el-date-picker
                            v-model="nurseLog.roundTime"
                            type="datetime"
                            placeholder="请选择时间"
                            format="YYYY-MM-DD HH:mm"
                            value-format="YYYY-MM-DD HH:mm"
                            style="width: 100%;"
                        />
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">意外发生地址</td>
                    <td colspan="2">
                        <el-input placeholder="请输入" v-model="nurseLog.recorder2"></el-input>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">伤情描述</td>
                    <td colspan="2">
                        <el-input placeholder="请输入" v-model="nurseLog.recorder3" type="textarea" :autosize="{ minRows: 4, maxRows: 8}"></el-input>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">身体处置情况</td>
                    <td colspan="2">
                        <el-input placeholder="请输入" v-model="nurseLog.recorder3" type="textarea" :autosize="{ minRows: 4, maxRows: 8}"></el-input>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">生命体征情况</td>
                    <td colspan="2">
                        <el-input placeholder="请输入" v-model="nurseLog.recorder3" type="textarea" :autosize="{ minRows: 4, maxRows: 8}"></el-input>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">送往医院方式及医院名称</td>
                    <td colspan="2">
                        <el-input placeholder="请输入" v-model="nurseLog.recorder3" type="textarea" :autosize="{ minRows: 4, maxRows: 8}"></el-input>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">通知监护人情况</td>
                    <td colspan="2">
                        <el-input placeholder="请输入" v-model="nurseLog.recorder3" type="textarea" :autosize="{ minRows: 4, maxRows: 8}"></el-input>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">发生意外情况描述</td>
                    <td colspan="2">
                        <el-input placeholder="请输入" v-model="nurseLog.recorder3" type="textarea" :autosize="{ minRows: 4, maxRows: 8}"></el-input>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">意外处置参与人员</td>
                    <td colspan="2">
                        <el-input placeholder="请输入" v-model="nurseLog.recorder3" type="textarea" :autosize="{ minRows: 4, maxRows: 8}"></el-input>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">谈话记录</td>
                    <td colspan="2">
                        <el-input placeholder="请输入" v-model="nurseLog.recorder3" type="textarea" :autosize="{ minRows: 4, maxRows: 8}"></el-input>
                    </td>
                </tr>
            </tbody>
        </table>
        <div style="text-align: center;margin-top: 20px;">
            <el-button type="primary" @click="submit">提交</el-button>
            <el-button @click="goBack">取消</el-button>
        </div>
    </div>
    </template>
    
    <script setup>
    import moment from 'moment';
    const router = useRouter()
    const nurseLog = ref({
        roundTime:moment().format('YYYY-MM-DD'),
    })
    // 返回工作台
     const goBack = () => {
        router.push('/work/nurseworkstation')
      }
      const submit = () => {
          
      }
    </script>
    
    <style lang="scss" scoped>
    .nurse-log {
        padding: 20px;
    
        .titleLog {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #D9001B;
            text-align: center;
        }
    }
    
    .table-style {
        border: 1px solid #ddd;
        border-collapse: collapse;
        width: 100%;
    
        td {
            border: 1px solid #ddd;
            padding: 8px;
            font-size: 14px;
        }
    }
    </style>
    