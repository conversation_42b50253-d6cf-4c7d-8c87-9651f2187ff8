<template>
  <div class="app-container nurse-workstation">
    <!-- 引入SSE提醒组件 -->
    <!-- <NurseReminder /> -->
    <!-- 主要内容区域 -->
    <div class="main-content">

      <!-- 左侧日历区域 - 在所有选项卡中都显示 -->
      <div class="left-panel">

        <div class="calendar-container">
                <!-- 顶部欢迎信息 -->
        <div class="welcome-section">
          <div class="user-info">
            <div class="greeting">您好，{{ userInfoAll.userName }}</div>
            <div class="date-info">今天是 {{ currentDate }} {{ weekDay }}</div>
          </div>
        </div>
          <el-calendar v-model="calendarValue">
            <template #header="{ date }">
              <div class="calendar-header" style="display: flex; justify-content: center; align-items: center; gap: 8px; width: 100%;">
                <div class="month-selector" style="display: flex; align-items: center; justify-content: center; width: 100%;">
                  <el-button size="small" @click="selectDate('prev-month')">
                    <el-icon><ArrowLeft /></el-icon>
                  </el-button>
                  <span>{{ date }}</span>
                  <el-button size="small" @click="selectDate('next-month')">
                    <el-icon><ArrowRight /></el-icon>
                  </el-button>
                </div>
              </div>
            </template>
            <template #date-cell="{ data }">
              <div class="calendar-cell" :class="{ 'is-today': isToday(data.day),'is-selected': isSelected(data.day)}" @click.stop="selectDateGetList(data.day)">
                <div class="date">{{ data.day.split('-')[2] }}</div>
                <!-- <div v-if="hasEvents(data.day)" class="event-indicator"></div> -->
              </div>
            </template>
          </el-calendar>
        </div>
        <!-- 我的提醒区域  - 在所有选项卡中都显示 -->
        <div class="my-reminders mb20">
          <div class="section-title">
            <el-badge :value="reminders.length" class="item"  :offset="[12, 11]">
              <span>我的提醒</span>
            </el-badge>
          </div>
          <div class="reminder-list">
            <el-scrollbar max-height="165">
              <div v-for="(reminder, index) in reminders" :key="index" class="reminder-item reminder-item-tx" @click="editReminder(reminder)" v-if="reminders.length > 0">
                <div class="reminder-time-tx">
                    <span>{{ reminder.reminderTime }}</span>
                    <el-icon :size="16" :style="reminder.status=='0' ? 'color: #999;' : 'color: #f00;'"><MuteNotification /></el-icon>
                </div>
                <div class="reminder-desc-tx">{{ reminder.content }}</div>
              </div>
              <div v-else class="no-reminder">暂无数据</div>
            </el-scrollbar>
          </div>
        </div>
        <!-- 我的待办区域 - 在所有选项卡中都显示 -->
        <div class="my-reminders">
          <div class="section-title">
            <el-badge :value="inProgressTasks.length" class="item"  :offset="[12, 11]">
              <span>我的待办</span>
            </el-badge>
          </div>
          <div class="reminder-list">
            <el-scrollbar max-height="185">
              <div v-for="(reminder, index) in inProgressTasks" :key="index" class="reminder-item" v-if="inProgressTasks.length > 0">
              <div class="reminder-content">
                <div class="reminder-desc-tx">{{ reminder.content }}</div>
              </div>
              <div class="reminder-status in-progress" @click="handleReminderClick(reminder)">
                <el-button type="primary" round>已完成</el-button>
              </div>
            </div>
            <div v-else class="no-reminder">暂无待办事项</div>
            </el-scrollbar>
          
          </div>
        </div>
      </div>

      <!-- 右侧内容区域 - 根据选项卡切换显示 -->
      <div class="right-content">
        <!-- 选项卡导航 -->
        <div class="tab-navigation">
          <el-tabs v-model="activeTab" class="workstation-tabs">
            <el-tab-pane label="工作记录" name="workRecord"></el-tab-pane>
            <el-tab-pane label="我的提醒" name="myReminder"></el-tab-pane>
            <el-tab-pane label="我的待办" name="myTodo"></el-tab-pane>
          </el-tabs>
        </div>
        <!-- 工作记录选项卡内容 -->
        <div v-if="activeTab === 'workRecord'" class="work-record-content">
          <!-- 工作记录卡片网格 -->
          <div class="record-cards-grid">
            <!-- 护士交接班表 -->
            <div class="record-card blue">
              <div class="card-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="card-content">
                <div class="card-title">护士交接班表</div>
                <div class="card-time">最近记录：今天 08:30</div>
                <div class="card-staff">交接人：张护士</div>
              </div>
              <div class="card-status">待交接</div>
              <div class="card-actions">
                <el-button type="primary" size="small" plain @click="viewHistory({type: 'handover'})">查看历史记录</el-button>
                <el-button type="primary" size="small" @click="editRecord({type: 'handover'})" style="background-color: #409eff; border-color: #409eff;">填写</el-button>
              </div>
            </div>

            <!-- 巡房表 -->
            <div class="record-card purple">
              <div class="card-icon">
                <el-icon><Location /></el-icon>
              </div>
              <div class="card-content">
                <div class="card-title">巡房表</div>
                <div class="card-time">最近记录：今天 08:30</div>
                <div class="card-staff">记录人：张</div>
              </div>
              <div class="card-status today-status">今日已完</div>
              <div class="card-actions">
                <el-button type="primary" size="small" plain @click="viewHistory({type: 'patrol'})">查看历史记录</el-button>
                <el-button type="primary" size="small" @click="editRecord({type: 'patrol'})" style="background-color: #9093fd; border-color: #9093fd;">填写</el-button>
              </div>
            </div>

            <!-- 更换易耗品表 -->
            <div class="record-card indigo">
              <div class="card-icon">
                <el-icon><Box /></el-icon>
              </div>
              <div class="card-content">
                <div class="card-title">更换易耗品表</div>
                <div class="card-time">最近记录：2025-06-26 12:00</div>
                <div class="card-staff">记录人：张护士</div>
              </div>
              <div class="card-status week-status">本周新增</div>
              <div class="card-actions">
                <el-button type="primary" size="small" plain @click="viewHistory({type: 'supplies'})">查看历史记录</el-button>
                <el-button type="primary" size="small" @click="editRecord({type: 'supplies'})" style="background-color: #673ab7; border-color: #673ab7;">填写</el-button>
              </div>
            </div>

            <!-- 日常日志 -->
            <div class="record-card green">
              <div class="card-icon">
                <el-icon><Notebook /></el-icon>
              </div>
              <div class="card-content">
                <div class="card-title">日常日志</div>
                <div class="card-time">最近记录：今天 08:30</div>
                <div class="card-staff">记录人：张护士</div>
              </div>
              <div class="card-status week-status">本周新增</div>
              <div class="card-actions">
                <el-button type="primary" size="small" plain @click="viewHistory({type: 'daily'})">查看历史记录</el-button>
                <el-button type="primary" size="small" @click="editRecord({type: 'daily'})" style="background-color: #67c23a; border-color: #67c23a;">填写</el-button>
              </div>
            </div>

            <!-- 紫外线表 -->
            <div class="record-card yellow">
              <div class="card-icon">
                <el-icon><Sunny /></el-icon>
              </div>
              <div class="card-content">
                <div class="card-title">紫外线表</div>
                <div class="card-time">最近记录：2025-06-03 12:00</div>
                <div class="card-staff">记录人：张护士</div>
              </div>
              <div class="card-status week-status">本周新增</div>
              <div class="card-actions">
                <el-button type="primary" size="small" plain @click="viewHistory({type: 'uv'})">查看历史记录</el-button>
                <el-button type="primary" size="small" @click="editRecord({type: 'uv'})" style="background-color: #e6a23c; border-color: #e6a23c;">填写</el-button>
              </div>
            </div>

            <!-- 紧急救护记录表 -->
            <div class="record-card red">
              <div class="card-icon">
                <el-icon><FirstAidKit /></el-icon>
              </div>
              <div class="card-content">
                <div class="card-title">紧急救护记录表</div>
                <div class="card-time">最近记录：2025-05-25 15:00</div>
                <div class="card-staff">记录人：张护士</div>
              </div>
              <div class="card-status week-status">本周新增</div>
              <div class="card-actions">
                <el-button type="primary" size="small" plain @click="viewHistory({type: 'emergency'})">查看历史记录</el-button>
                <el-button type="primary" size="small" @click="editRecord({type: 'emergency'})" style="background-color: #f56c6c; border-color: #f56c6c;">填写</el-button>
              </div>
            </div>
          </div>

          <!-- 今日工作记录 -->
          <div class="today-work-records">
            <div class="section-title"> {{ formatWorkRecordDate(currentDateToday || moment(calendarValue).format('YYYY-MM-DD')) }}工作记录</div>

            <!-- <div class="record-item">
              <div class="record-icon blue">
                <el-icon><Document /></el-icon>
              </div>
              <div class="record-content">
                <div class="record-header">
                  <span class="record-title">护士交接班表</span>
                  <el-button type="primary" @click="viewDetail({type: 'handover'})" link>查看详情</el-button>
                </div>
                <div class="record-info">
                  <div>张护士 → 李护士 交接完成</div>
                  <div>15项注意事项</div>
                  <div class="record-time">今天 08:00</div>
                </div>
              </div>
            </div> -->
            <el-scrollbar max-height="400">
              <div class="record-item" v-for="(record, index) in workStation" :key="index" v-if="workStation.length > 0">
                <div class="record-icon purple">
                  <el-icon><Location /></el-icon>
                </div>
                <div class="record-content">
                  <div class="record-header">
                    <span class="record-title">巡房表-{{ getRetrieveWardRound(record.type)}}</span>
                    <el-button type="primary" @click="viewDetail({type: 'patrol',rows: record})" link>查看详情</el-button>
                  </div>
                  <div class="record-info">
                    <div>巡房完成</div>
                    <div class="record-time">{{ record.data.roundDate }}</div>
                  </div>
                </div>
              </div>
              <div v-else class="no-reminder">暂无数据</div>
            </el-scrollbar>
<!-- 
            <div class="record-item">
              <div class="record-icon indigo">
                <el-icon><Box /></el-icon>
              </div>
              <div class="record-content">
                <div class="record-header">
                  <span class="record-title">更换易耗品表</span>
                  <el-button type="primary" @click="viewDetail({type: 'supplies'})" link>查看详情</el-button>
                </div>
                <div class="record-info">
                  <div>更换完成</div>
                  <div class="record-time">今天 08:00</div>
                </div>
              </div>
            </div> -->
          </div>
        </div>

        <!-- 我的提醒选项卡内容 -->
        <div v-else-if="activeTab === 'myReminder'" class="my-reminder-content">
          <div class="reminder-cards">
            <div v-for="(reminder, index) in reminders" :key="index" class="reminder-card">
              <div class="reminder-header">
                <div class="reminder-title">{{ reminder.content }}</div>
                <div class="reminder-status" :class="reminder.status">
                  {{ reminder.status === '0' ? '待处理' : '已完成' }}
                </div>
              </div>
              <div class="reminder-body">
                <div class="reminder-time">{{ reminder.createTime }}</div>
              </div>
              <div class="reminder-actions">
                <el-button type="primary" size="small" plain>标记为已完成</el-button>
                <el-button type="danger" size="small" plain>删除</el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 我的待办选项卡内容 -->
        <div v-else-if="activeTab === 'myTodo'" class="my-todo-content">
          <!-- <div class="todo-header">
            <el-button type="primary" size="small" @click="addTask">
              <el-icon><Plus /></el-icon>
              新建事项
            </el-button>
          </div> -->

          <div class="todo-columns">
            <!-- 计划中任务列 -->
            <div class="todo-column">
              <div class="column-header">
                <div class="column-title">计划中 <span class="count">{{ plannedTasks.length }}</span></div>
                <el-button type="primary" size="small" @click="addTask('planned')">
                  <el-icon><Plus /></el-icon>
                   新建事项
              </el-button>
              </div>
              <draggable
                id="planned-list"
                class="task-list"
                :list="plannedTasks"
                :group="{ name: 'tasks', pull: true, put: true }"
                item-key="id"
                ghost-class="ghost-task"
                chosen-class="chosen-task"
                drag-class="drag-task"
                @change="handleTaskChange"
                @end="handleDragEnd"
              >
                <template #item="{ element }">
                  <div class="task-item taskBorder1" :class="{'important': element.isImportant}">                    
                    <div class="task-content">
                      <div class="task-title">{{ element.content }}</div>
                       <div class="right_delete" @click="deleteTask(element,'planned')">
                            <el-icon><MoreFilled /></el-icon>
                       </div>
                    </div>
                    <div class="task-actions">
                      <div class="task-date"><el-icon><Document /></el-icon>{{ element.todoDate }}</div>
                      <div class="task-status planned">{{ element.statusText }}</div>
                    </div>
                  </div>
                </template>
              </draggable>
            </div>

            <!-- 进行中任务列 -->
            <div class="todo-column">
              <div class="column-header">
                <div class="column-title">进行中 <span class="count">{{ inProgressTasks.length }}</span></div>
                <el-button type="primary" size="small" @click="addTask('inProgress')">
                  <el-icon><Plus /></el-icon>
                   新建事项
              </el-button>
              </div>
              <draggable
                id="inprogress-list"
                class="task-list"
                :list="inProgressTasks"
                :group="{ name: 'tasks', pull: true, put: true }"
                item-key="id"
                ghost-class="ghost-task"
                chosen-class="chosen-task"
                drag-class="drag-task"
                @change="handleTaskChange"
                @end="handleDragEnd"
              >
                <template #item="{ element }">
                  <div class="task-item taskBorder2" :class="{'important': element.isImportant}">                    
                    <div class="task-content">
                      <div class="task-title">{{ element.content }}</div>
                      <div class="right_delete"  @click="deleteTask(element,'inProgress')">
                            <el-icon><MoreFilled /></el-icon>
                       </div>
                    </div>
                    <div class="task-actions">
                      <div class="task-date"><el-icon><Document /></el-icon>{{ element.todoDate }}</div>
                      <div class="task-status inProgress">{{ element.statusText }}</div>
                    </div>
                  </div>
                </template>
              </draggable>
            </div>
            <!-- 已完成任务列 -->
            <div class="todo-column">
              <div class="column-header">
                <div class="column-title">已完成 <span class="count">{{ completedTasks.length }}</span></div>
                <el-button type="primary" size="small" @click="addTask('completed')">
                  <el-icon><Plus /></el-icon>
                   新建事项
              </el-button>
              </div>
              <draggable
                id="completed-list"
                class="task-list"
                :list="completedTasks"
                :group="{ name: 'tasks', pull: true, put: true }"
                item-key="id"
                ghost-class="ghost-task"
                chosen-class="chosen-task"
                drag-class="drag-task"
                @change="handleTaskChange"
                @end="handleDragEnd"
              >
                <template #item="{ element }">
                  <div class="task-item taskBorder3" :class="{'important': element.isImportant}">                    
                    <div class="task-content">
                      <div class="task-title">{{ element.content }}</div>
                      <div class="right_delete"  @click="deleteTask(element,'completed')">
                            <el-icon><MoreFilled /></el-icon>
                       </div>
                    </div>
                    <div class="task-actions">
                       <div class="task-date"><el-icon><Document /></el-icon>{{ element.todoDate }}</div>
                       <div class="task-status completed">{{ element.statusText }}</div>
                    </div>
                  </div>
                </template>
              </draggable>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog :title="titleTask+'-新建事项'" v-model="dialogVisible" width="40%">
      <el-form :model="formCreate" label-width="120px" ref="formCreateRef">
        <el-form-item label="任务标题" prop="title" :rules="[{ required: true, message: '请输入任务标题', trigger: 'blur' }]">
            <el-input v-model="formCreate.title" placeholder="请输入" type="textarea" :row="2"/>
        </el-form-item>
        <el-form-item label="任务日期" prop="todoDate" :rules="[{ required: true, message: '请选择任务日期', trigger: 'blur' }]">
          <el-date-picker v-model='formCreate.todoDate' clearable placeholder='请选择日期' style='width: 100%' type='date' value-format='YYYY-MM-DD'></el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <Dialog 
    v-model:visible="showDialog" 
    title="提示" 
    width="50%"
    @close="handleClose"
  >
    <div class="dialog-content">
       <svg t="1747910526596" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1493" width="120" height="120"><path d="M236.539274 477.852272c17.253966 0 31.233352-13.980409 31.233352-31.233352 0-110.680798 64.816215-197.723224 173.372629-232.82058 14.792914-4.77884 23.811312-19.713994 21.168112-35.026748-0.426719-2.480494-0.64059-4.900613-0.64059-7.197936 0-24.614607 22.683628-44.63457 50.561559-44.63457 27.858488 0 50.53086 20.018939 50.53086 44.63457 0 2.379187-0.203638 4.727675-0.599657 7.005554-2.745531 15.535835 6.537903 30.674627 21.615297 35.290761 110.202914 33.714869 173.402305 118.550023 173.402305 232.748948 0 17.253966 13.980409 31.233352 31.234375 31.233352s31.233352-13.980409 31.233352-31.233352c0-133.414569-72.349795-238.259452-194.876386-284.724717-5.174859-54.537104-53.673433-97.422843-112.541169-97.422843-59.062164 0-107.691721 43.149752-112.623034 97.921193-120.482025 47.999201-194.306404 154.959258-194.306404 284.226367C205.305923 463.871863 219.285309 477.852272 236.539274 477.852272z" fill="#ffffff" p-id="1494"></path><path d="M819.911812 602.309842l0-55.889915c0-17.253966-13.980409-31.233352-31.233352-31.233352s-31.234375 13.980409-31.234375 31.233352l0 67.988464c0 7.80885 2.928702 15.34243 8.204869 21.097504 40.10951 43.780109 86.381369 99.497085 95.105055 116.89329-0.315178 10.827603-3.345188 13.552667-32.586163 13.552667L196.820668 765.951853c-26.983561 0-31.671327-7.259334-32.484855-13.735839 9.17087-17.701151 55.381331-73.072249 95.449909-116.669186 5.306866-5.765307 8.245801-13.30912 8.245801-21.137413l0-67.988464c0-17.253966-13.980409-31.233352-31.233352-31.233352s-31.233352 13.980409-31.233352 31.233352l0 55.848982C101.69617 716.315362 101.69617 737.829352 101.69617 748.300844c0 38.727023 24.991184 80.117712 95.124498 80.117712l631.347179 0c23.5166 0 95.094822 0 95.094822-80.117712C923.262668 737.788419 923.262668 716.202799 819.911812 602.309842z" fill="#ffffff" p-id="1495"></path><path d="M400.99993 366.001835c-17.253966 0-31.233352 13.980409-31.233352 31.234375l0 30.470989c0 17.253966 13.980409 31.234375 31.233352 31.234375s31.234375-13.980409 31.234375-31.234375l0-30.470989C432.234305 379.982244 418.253896 366.001835 400.99993 366.001835z" fill="#ffffff" p-id="1496"></path><path d="M623.957885 366.001835c-17.253966 0-31.234375 13.980409-31.234375 31.234375l0 30.470989c0 17.253966 13.980409 31.234375 31.234375 31.234375 17.253966 0 31.233352-13.980409 31.233352-31.234375l0-30.470989C655.19226 379.982244 641.21185 366.001835 623.957885 366.001835z" fill="#ffffff" p-id="1497"></path><path d="M512.170892 598.435605c43.963281 0 75.105558-30.318516 86.574774-48.223305 9.222035-14.396895 5.03262-33.358759-9.242502-42.763966-14.305821-9.405207-33.593096-5.38873-43.159986 8.764618-0.132006 0.193405-13.614066 19.754926-34.172287 19.754926-19.989263 0-32.43369-18.117636-33.267685-19.378349-9.181103-14.407128-28.285207-18.809391-42.834574-9.750061-14.650675 9.099239-19.155269 28.356838-10.044774 43.007513C437.238272 567.892985 467.99374 598.435605 512.170892 598.435605z" fill="#ffffff" p-id="1498"></path><path d="M601.661066 856.999498c-15.179724-8.225335-34.131355-2.593058-42.346457 12.576433-9.292644 17.142425-27.248597 27.79709-46.871517 27.79709-19.530822 0-37.476543-10.67513-46.830585-27.848255-8.256034-15.149025-27.217898-20.741393-42.366923-12.495592-15.149025 8.256034-20.741393 27.217898-12.495592 42.366923 20.304442 37.283138 59.275012 60.444651 101.6931 60.444651 42.560328 0 81.561597-23.180955 101.794407-60.494793C622.453624 884.176464 616.821347 865.224834 601.661066 856.999498z" fill="#ffffff" p-id="1499"></path></svg>      
    </div>
    <div class="content_tip">      
           {{ contentTip.content }}
    </div>
    <template #footer>
      <el-button plain @click="delayOneHour">延迟1小时</el-button>
      <el-button type="primary" @click="understoodMethod">知道了</el-button>
    </template>
  </Dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import Dialog from './components/noticeDialog.vue'
// import NurseReminder from './reminder/index.vue'
import { useEventBus } from '@/utils/eventBus'
import {
  ArrowLeft,
  ArrowRight,
  Document,
  Location,
  Box,
  Notebook,
  Sunny,
  Plus
} from '@element-plus/icons-vue'
import moment from 'moment'
import draggable from 'vuedraggable'
import { ElMessageBox,ElMessage } from 'element-plus'
import {
  addNurseTodo,
  getNurseTodoList,
  updateNurseTodo,
  deleteNurseTodo,
  getMyRemindList,
  updateMyRemind,
  getNurseWorkStation
} from '@/api/nurseworkstation/index'
import useUserStore from '@/store/modules/user'
const userStore = useUserStore();
const {
    proxy
} = getCurrentInstance()
const contentTip = ref({})
const showConfirmDialog = ref(true)
console.log(userStore,'userStore')
const router = useRouter()
const titleTask = ref('')
const dialogVisible = ref(false)
const formCreate = ref({})
const showDialog = ref(false)
const workStation = ref([])
// 当前日期和星期
const currentDate = computed(() => {
  return moment().format('YYYY年MM月DD日')
})
const currentDateFormat = computed(() => {
  return moment().format('YYYY-MM-DD HH:mm:ss')
})

const weekDay = computed(() => {
  const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  return weekDays[moment().day()]
})
const handleClose = () => {
  console.log('弹框已关闭')
}
const userInfoAll = ref(JSON.parse(localStorage.getItem('userInfo')))
// 标签页
const activeTab = ref('workRecord')

// 日历相关
const calendarValue = ref(new Date())

// 选择日期
const selectDate = (type) => {
  const date = new Date(calendarValue.value)
  if (type === 'prev-month') {
    date.setMonth(date.getMonth() - 1)
  } else if (type === 'next-month') {
    date.setMonth(date.getMonth() + 1)
  }
  calendarValue.value = date
}
const currentDateToday=ref('')
//选则具体天
const selectDateGetList = (val) => {
  currentDateToday.value = val
  getNurseWorkStationData(val)
}
const formatWorkRecordDate = (date) => {
  const formattedDate = moment(date).format('YYYY-MM-DD');
  return formattedDate === moment().format('YYYY-MM-DD') ? '今日' : formattedDate;
}
// 获取日期
const getZeroFillDate = () => {
    const date = new Date()
    const year = date.getFullYear()
    const month = date.getMonth() + 1
    const day = date.getDate()
    return `${year}年${month < 10 ? '0' + month : month}月${day < 10 ? '0' + day : day}日`
}
// 判断是否是今天
const isToday = (day) => {
  return day === moment().format('YYYY-MM-DD')
}
const isSelected = (day) => {
  // 你的选中逻辑
  return day === currentDateToday.value;
};
// 判断日期是否有事件
const hasEvents = (day) => {
  return eventDays.value.includes(day)
}

// 格式化日历头部
const formatCalendarHeader = (date) => {
  // 如果date是字符串，尝试解析它
  
}
const editReminder = (reminder) => {
  showDialog.value = true
  contentTip.value = reminder
}
//知道了
const understoodMethod = async () => {
   const res = await updateMyRemind({
      nurseId:userInfoAll.value.userId,
      nurseName:userInfoAll.value.userName,
      status:1,
      id:contentTip.value.id
   })
   if(res.code === 200){
      contentTip.value = {}
      showDialog.value = false
      getMyRemindListData()
   }
}
//延迟1小时
const delayOneHour = async () => {
  try {
    // 获取当前时间并加1小时
    const now = new Date();
    const delayedTime = new Date(now.getTime() + 60 * 60 * 1000); // 当前时间加1小时
    
    // 格式化时间为接口需要的格式 (YYYY-MM-DD HH:mm:ss)
    const formattedTime = moment(delayedTime).format('YYYY-MM-DD HH:mm:ss');
    
    // 调用接口更新提醒时间
    const res = await updateMyRemind({
      id: contentTip.value.id,
      nurseId: userInfoAll.value.userId,
      nurseName: userInfoAll.value.userName,
      status: '0', // 保持未完成状态
      reminderTime: formattedTime // 设置新的提醒时间
    });
    
    if (res.code === 200) {
      ElMessage.success('提醒已延迟1小时');
      showDialog.value = false;
      getMyRemindListData(); // 刷新提醒列表
    } else {
      ElMessage.error('操作失败，请重试');
    }
  } catch (error) {
    console.error('延迟提醒出错:', error);
    ElMessage.error('操作失败，请重试');
  }
};
// 有事件的日期
const eventDays = ref([
  moment().format('YYYY-MM-DD'),
  moment().add(1, 'days').format('YYYY-MM-DD'),
  moment().add(3, 'days').format('YYYY-MM-DD'),
  moment().subtract(2, 'days').format('YYYY-MM-DD')
])

// 提醒列表
const reminders = ref([
  // {
  //   time: '今天 08:30',
  //   title: '护士交接班',
  //   description: '张护士 → 李护士 交接完成',
  //   status: 'completed'
  // },
  // {
  //   time: '今天 09:30',
  //   title: '巡房表',
  //   description: '记录人：郭',
  //   status: 'pending'
  // },
  // {
  //   time: '2020-06-20 12:00',
  //   title: '更换易耗品表',
  //   description: '记录人：张护士',
  //   status: 'completed'
  // }
])

// 工作记录数据已直接在模板中硬编码，不再需要这些变量

// 待办任务
const tasks = ref([
  // {
  //   id: 1,
  //   date: '7月25日',
  //   title: '本月20号新成员入住房间紧外线消毒工作',
  //   description: '',
  //   status: 'planned',
  //   statusText: '计划中'
  // },
  // {
  //   id: 2,
  //   date: '7月25日',
  //   title: '305-02床本月25号左右办理离院手续',
  //   description: '',
  //   status: 'planned',
  //   statusText: '计划中'
  // },
  // {
  //   id: 3,
  //   date: '7月25日',
  //   title: '本月15号街道办领导来树树问题老人，做好接待工作',
  //   description: '',
  //   status: 'planned',
  //   statusText: '计划中'
  // },
  // {
  //   id: 4,
  //   date: '7月25日',
  //   title: '月底生日会整理完2个，增加唱歌环节，院长参加',
  //   description: '',
  //   status: 'planned',
  //   statusText: '计划中'
  // },
  // {
  //   id: 5,
  //   date: '7月25日',
  //   title: '下月公示节，请假一天，回家探望父母',
  //   description: '',
  //   status: 'planned',
  //   statusText: '计划中',
  //   isImportant: true
  // },
  // {
  //   id: 6,
  //   date: '7月25日',
  //   title: '月底新成本年度护士长述职工作，开好下午的工作计划表',
  //   description: '',
  //   status: 'planned',
  //   statusText: '计划中'
  // },
  // {
  //   id: 7,
  //   date: '7月25日',
  //   title: '住房备品工作，统计易耗品入库出库数据',
  //   description: '',
  //   status: 'inProgress',
  //   statusText: '进行中'
  // },
  // {
  //   id: 8,
  //   date: '7月25日',
  //   title: '本周厨房卫生大检查，护理人员按能培训',
  //   description: '',
  //   status: 'inProgress',
  //   statusText: '进行中'
  // },
  // {
  //   id: 9,
  //   date: '7月25日',
  //   title: '302-02床本次白天外出，下午4点后检查老苗回院',
  //   description: '',
  //   status: 'inProgress',
  //   statusText: '进行中',
  //   isImportant: true
  // },
  // {
  //   id: 10,
  //   date: '7月25日',
  //   title: '3层入住大本周完成全部身体检查工作',
  //   description: '',
  //   status: 'completed',
  //   statusText: '已完成'
  // },
  // {
  //   id: 11,
  //   date: '7月25日',
  //   title: '完成本月药品整理及有效期检查，药品及药柜检查，药品分类',
  //   description: '',
  //   status: 'completed',
  //   statusText: '已完成'
  // },
  // {
  //   id: 12,
  //   date: '7月25日',
  //   title: '完成老人入院档案整理工作——安排护理人员增加文娱工作',
  //   description: '',
  //   status: 'completed',
  //   statusText: '已完成'
  // },
  // {
  //   id: 13,
  //   date: '7月25日',
  //   title: '上月共保险登录数据老人安排生活主管，王大夫安排外出病房',
  //   description: '',
  //   status: 'completed',
  //   statusText: '已完成'
  // }
])
const addType = ref('')
// 任务列表
const plannedTasks = ref([])
const inProgressTasks = ref([])
const completedTasks = ref([])

// 初始化任务列表
const initTaskLists = () => {
  plannedTasks.value = tasks.value.filter(task => task.status === 'planned')
  inProgressTasks.value = tasks.value.filter(task => task.status === 'inProgress')
  completedTasks.value = tasks.value.filter(task => task.status === 'completed')
}

// 查看历史记录
const viewHistory = (record) => {
  console.log('查看历史记录', record)
  if(record.type === 'patrol') {
    router.push({ path: '/nurseworkstation/recordlist/add/0/add'})
  }else if(record.type === 'handover') {
     //护士交接班表
     router.push({ path: '/nurseShiftHistory/nurseShiftHistoryForm/add/0/add'})
  } else if(record.type === 'supplies') {
    //易耗品
    router.push({ path: '/nurseShiftChangeReport/replaceConsumablesRecordHistory/add/0/add'})
  } else if(record.type === 'daily') {
    // 护士日志
    router.push({ path: '/nurseLogs/nurseLogHistory/add/0/add'})
  } else if(record.type === 'uv') {
    //紫外线
    router.push({ path: '/ultravioletDisinfectionLog/uvDisinfectionRecordHistory/add/0/add'})
  }else if(record.type === 'emergency') {
    router.push({ path: '/emergencyRescueLog/emergencyRescueRecordHistory/add/0/add'})
  }
}

// 填写记录
const editRecord = (record) => {
  console.log('填写记录', record)
  if(record.type === 'patrol') {
    router.push({
      path: '/roomInspection/roomInspectionForm/add/0/add'
    })
  } else if(record.type === 'handover') {
    //护士交接班表
    router.push({ path: '/nurseShiftChangeReport/nurseShiftAdd/add/0/add'})
  } else if(record.type === 'supplies') {
    router.push({ path: '/nurseShiftChangeReport/replaceConsumablesRecord/add/0/add'})
  } else if(record.type === 'daily') {
    //护士日志
    router.push({ path: '/nurseLogs/nurseLog/add/0/add'})
  } else if(record.type === 'uv') {
    //紫外线
    router.push({ path: '/ultravioletDisinfectionLog/uvDisinfectionRecord/add/0/add'})
  }else if(record.type === 'emergency') {
    //紧急救护
    router.push({ path: '/emergencyRescueLog/emergencyRescueRecord/add/0/add'})
  }
}

// 查看详情
const viewDetail = (record) => {
  if(record.type === 'patrol') {
    console.log('查看详情', record)
    console.log('查看详情', record.rows.type)
    // return;
    router.push({ path: `/nurseworkstation/recordlist/add/0/add`,query:{type:record.rows.type}})
  }
}

// 添加任务
const addTask = (type) => {
  console.log('添加任务')
  formCreate.value = {
    todoDate: '',
    title: '',
  }
  dialogVisible.value = true
  addType.value = type
  // const newId = Math.max(...tasks.value.map(task => task.id)) + 1
  // const newTask = {
  //   id: newId,
  //   date: '7月25日',
  //   title: '新任务',
  //   description: '',
  //   status: 'planned',
  //   statusText: '计划中'
  // }

  // 添加到原始任务数组
  // tasks.value.push(newTask)

  // // 添加到计划中列表
  // plannedTasks.value.push(newTask)

  // console.log('新任务已添加:', newTask)
  if(type === 'planned') {
     titleTask.value = '计划中'
  }else if(type === 'inProgress') {
     titleTask.value = '进行中'
  }else if(type === 'completed') {
     titleTask.value = '已完成'
  }
}
//删除
const deleteTask = (task,type) => {
  console.log('删除任务', task)
  ElMessageBox.confirm('确定删除该任务吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    if(type === 'planned') {
        deleteNurseTodoList(task.id)
      }else if(type === 'inProgress') {
        deleteNurseTodoList(task.id)
      }else if(type === 'completed') {
        deleteNurseTodoList(task.id)
      }
  })
  
}
const deleteNurseTodoList = async (id) => {
  const res = await deleteNurseTodo(id)
  if(res.code === 200) {
    ElMessage.success('删除成功')
    getNurseTodoListData()
  }else {
    ElMessage.error('删除失败')
  }
}
// 新建事项提交
const handleSubmit = (form) => {
  proxy.$refs.formCreateRef.validate(async (valid) => {
    if (valid) {
       let params = {
        todoDate: formCreate.value.todoDate,
        content: formCreate.value.title,
        createBy:userInfoAll.value.nickName,
        nurseId:userInfoAll.value.userId,
        nurseName:userInfoAll.value.userName,
        status:addType.value === 'planned' ? 1:addType.value === 'inProgress' ? 2:addType.value === 'completed' ? 3:'',
      }
      const newTask = await addNurseTodo(params)
      console.log(newTask,'newTask')
      if (newTask.code === 200) {
        dialogVisible.value = false
        ElMessage.success('新建成功')
        getNurseTodoListData()
      }else{
        ElMessage.error('新建失败')
      }
    }
  })
  
}
const getNurseTodoListData = async () => {
  const res = await getNurseTodoList({
    nurseId:userInfoAll.value.userId,
    nurseName:userInfoAll.value.userName,
  })
  tasks.value = res.rows.map(task => ({
    ...task,
    status: task.status === "1" ? 'planned' : task.status === "2" ? 'inProgress' : task.status === "3" ? 'completed' : '',
    statusText: task.status === "1" ? '计划中' : task.status === "2" ? '进行中' : task.status === "3" ? '已完成' : '',
  })
  ) || []
  plannedTasks.value = tasks.value.filter(task => task.status === 'planned')
  inProgressTasks.value = tasks.value.filter(task => task.status === 'inProgress')
  completedTasks.value = tasks.value.filter(task => task.status === 'completed')
}
const handleReminderClick =async (task) => {
  console.log('Reminder clicked for task:', task)
 const res =  await updateNurseTodo({ id: task.id, status: '3'})
  if (res.code === 200) {
    // 请求成功，更新任务状态
    ElMessage.success('任务状态更新成功')
    getNurseTodoListData()
  }else{
    ElMessage.error('任务状态更新失败')
  }
}
// 处理任务拖拽变化
const handleTaskChange = (evt) => {
  console.log('Task change event:', evt)

  // 当任务被添加到新列表时，更新其状态
  if (evt.added) {
    const task = evt.added.element

    // 根据目标列表更新任务状态
    if (evt.to?.id === 'planned-list') {
      task.status = 'planned'
      task.statusText = '计划中'
    } else if (evt.to?.id === 'inprogress-list') {
      task.status = 'inProgress'
      task.statusText = '进行中'
    } else if (evt.to?.id === 'completed-list') {
      task.status = 'completed'
      task.statusText = '已完成'
    }

    // 更新原始任务数组中的状态
    const originalTask = tasks.value.find(t => t.id === task.id)
    if (originalTask) {
      originalTask.status = task.status
      originalTask.statusText = task.statusText
    }

    console.log(`任务 "${task.title}" 已添加到 ${task.statusText} 列表`)
  }

  // 当任务被移除时
  if (evt.removed) {
    const task = evt.removed.element
    console.log(`任务 "${task.title}" 已从列表中移除`)
  }

  // 当任务被移动时（同一列表内）
  if (evt.moved) {
    const task = evt.moved.element
    console.log(`任务 "${task.title}" 在同一列表内移动`)
  }
}

// 拖拽结束后的处理
const handleDragEnd = async (evt) => {
  console.log('Drag end event:', evt)

  // 确保拖拽后的状态正确
  if (evt.item && evt.to) {
    try {
      const task = evt.item.__draggable_context.element
      let newStatus = task.status
      // 根据目标列表ID更新任务状态
      if (evt.to.id === 'planned-list' && task.status !== 'planned') {
        task.status = 'planned'
        task.statusText = '计划中'
          newStatus = '1'
      } else if (evt.to.id === 'inprogress-list' && task.status !== 'inProgress') {
        task.status = 'inProgress'
        task.statusText = '进行中'
        newStatus = '2'
      } else if (evt.to.id === 'completed-list' && task.status !== 'completed') {
        task.status = 'completed'
        task.statusText = '已完成'
        newStatus = '3'
      }
      if (task.status !== newStatus) {
        // 调用API更新任务状态
        const res = await updateNurseTodo({ id: task.id, status: newStatus})
        
        if (res.code === 200) {
          // 更新本地任务状态
          task.status = newStatus
          task.statusText = newStatus === 'planned' ? '计划中' 
                         : newStatus === 'inProgress' ? '进行中' 
                         : '已完成'
          
          // 更新原始任务数组中的状态
          const originalTask = tasks.value.find(t => t.id === task.id)
          if (originalTask) {
            originalTask.status = task.status
            originalTask.statusText = task.statusText
          }
          
          ElMessage.success('任务状态更新成功')
        } else {
          ElMessage.error('任务状态更新失败')
          // 如果API调用失败，可以在这里恢复任务到原来的位置
        }
      }

      console.log(`任务 "${task.title}" 已移动到 ${task.statusText} 列表`)
    } catch (error) {
      console.error('处理拖拽结束事件时出错:', error)
    }
  }
}
// 我的提醒列表
const getMyRemindListData = async () => {
  const response = await getMyRemindList({
    nurseId:userInfoAll.value.userId,
    nurseName:userInfoAll.value.userName,
    status:'0'
  })
  console.log(response,'999999999999999')
  reminders.value = response.rows || []
}
// 获取工作记录
const getNurseWorkStationData = async (datepamers) => {
  const response = await getNurseWorkStation({
    nurseId:userInfoAll.value.userId,
    workDate:datepamers?datepamers:moment(calendarValue.value).format('YYYY-MM-DD')
  })
  workStation.value = transformData(response.data) || []
  console.log(workStation.value,'999999999999999')
}
function transformData(originalData) {
  const result = [];
  
  // Process hfRecord
  if (originalData.hfRecord) {
    const hfRecord = {
      type: 'hfRecord',
      data: {...originalData.hfRecord},
      visits: originalData.hfRecord.visits || []
    };
    // Remove visits from data to avoid duplication
    delete hfRecord.data.visits;
    result.push(hfRecord);
  }
  
  // Process orgRecord
  if (originalData.orgRecord) {
    result.push({
      type: 'orgRecord',
      data: {...originalData.orgRecord}
    });
  }
  
  // Process xzRecord if not null
  if (originalData.xzRecord) {
    result.push({
      type: 'xzRecord',
      data: {...originalData.xzRecord}
    });
  }
  
  // Process hlrecord
  if (originalData.hlrecord) {
    result.push({
      type: 'hlrecord',
      data: {...originalData.hlrecord}
    });
  }
  
  // Process hlzzrecord
  if (originalData.hlzzrecord) {
    result.push({
      type: 'hlzzrecord',
      data: {...originalData.hlzzrecord}
    });
  }
  
  return result;
}
const getRetrieveWardRound = (type) =>{
   const typeName = [
    { label: '和孚护理查房记录', value: "hfRecord" },
    { label: '机构综合查房记录', value: "orgRecord" },
    { label: '行政查房记录', value: "xzRecord" },
    { label: '护理查房记录', value: "hlrecord"},
    { label: '护理组长查房记录', value: "hlzzrecord" },
    { label: '安全查房', value:"aqRecord" }
  ]
    return typeName.find(item => item.value === type)?.label || ''
}
// 监听刷新提醒通知事件
useEventBus.on('refresh-reminders', (payload) => {
  getMyRemindListData()
})
onMounted(() => {
  console.log('护士工作站页面已加载')
  // initTaskLists()
  getMyRemindListData()
  getNurseTodoListData()
  getNurseWorkStationData()
})
</script>

<style lang="scss" scoped>
.nurse-workstation {
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
  padding: 16px;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;

  .welcome-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 16px 5px;
    border-bottom: 1px solid #eee;

    .user-info {
      .greeting {
        font-size: 18px;
        font-weight: bold;
        color: #303133;
      }

      .date-info {
        font-size: 14px;
        color: #606266;
        margin-top: 4px;
      }
    }
  }

  .tab-navigation {
    margin-bottom: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;

    .workstation-tabs {
      margin-bottom: 0;

      :deep(.el-tabs__header) {
        margin: 0;
        border-bottom: none;
      }

      :deep(.el-tabs__nav-wrap::after) {
        display: none;
      }

      :deep(.el-tabs__active-bar) {
        height: 3px;
        border-radius: 3px;
      }

      :deep(.el-tabs__item) {
        padding: 0 30px;
        height: 50px;
        line-height: 50px;
        font-size: 15px;
        color: #606266;

        &.is-active {
          color: #409eff;
          font-weight: bold;
        }

        &:hover {
          color: #409eff;
        }
      }
    }
  }

  .main-content {
    display: flex;
    gap: 16px;

    .left-panel {
      width: 300px;
      flex-shrink: 0;

      .calendar-container {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        margin-bottom: 16px;
        overflow: hidden;

        .calendar-header {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 8px 0;

          .month-selector {
            display: flex;
            align-items: center;
            gap: 8px;
          }
        }

        .calendar-cell {
          height: 32px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          position: relative;

          &.is-today {
            background-color: #ecf5ff;
            color: #409eff;
            font-weight: bold;
          }

          .date {
            font-size: 14px;
          }

          .event-indicator {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: #409eff;
            position: absolute;
            bottom: 2px;
          }
        }
      }

      .my-reminders {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        padding: 16px;
       
        .section-title {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 16px;
          display: flex;
          align-items: center;

          .badge {
            background-color: #f56c6c;
            color: #fff;
            border-radius: 10px;
            padding: 0 6px;
            font-size: 12px;
            margin-left: 8px;
          }
        }

        .reminder-list {
          .reminder-item {
            display: flex;
            margin-bottom: 16px;
            padding-bottom: 16px;
            border-bottom: 1px solid #eee;
            position: relative;
            min-height: 40px;
            &:hover{
              .in-progress{
                display: block;
              }
            }
            &:last-child {
              margin-bottom: 0;
              padding-bottom: 0;
              border-bottom: none;
            }

            .reminder-time {
              font-size: 12px;
              color: #999;
              width: 80px;
              flex-shrink: 0;
            }

            .reminder-content {
              flex-grow: 1;

              .reminder-title {
                font-size: 14px;
                font-weight: bold;
                margin-bottom: 4px;
                // 多行文本溢出显示省略号
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
              }

              .reminder-desc {
                font-size: 12px;
                color: #666;
              }
            }
             .in-progress{
              display: none;
              cursor: pointer;
              position: absolute;
              bottom: 2px;
              right: 3px;
             }
            .reminder-status {
              font-size: 12px;
              padding: 2px 6px;
              border-radius: 4px;
              margin-left: 8px;
   
              &.completed {
                background-color: #f0f9eb;
                color: #67c23a;
                height: 35px;
                line-height: 35px;
                white-space: nowrap;
              }

              &.pending {
                background-color: #fdf6ec;
                color: #e6a23c;
                height: 35px;
                line-height: 35px;
                white-space: nowrap;
              }
            }
          }
        }
      }
    }

    .right-content {
      flex-grow: 1;

      // 工作记录选项卡样式
      .work-record-content {
        // 工作记录卡片网格
        .record-cards-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 16px;
          margin-bottom: 24px;

          .record-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            padding: 16px;
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden;
            transition: transform 0.2s, box-shadow 0.2s;

            &:hover {
              transform: translateY(-4px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
            }

            &.blue {
              .card-icon {
                background-color: rgba(64, 158, 255, 0.1);
                color: #409eff;
              }
              .card-title {
                color: #409eff;
              }
              .card-actions {
                .el-button[plain] {
                  color: #409eff;
                  border-color: #409eff;
                }
              }
            }

            &.purple {
              .card-icon {
                background-color: rgba(144, 147, 253, 0.1);
                color: #9093fd;
              }
              .card-title {
                color: #9093fd;
              }
              .card-actions {
                .el-button[plain] {
                  color: #9093fd;
                  border-color: #9093fd;
                }
              }
            }

            &.indigo {
              .card-icon {
                background-color: rgba(103, 58, 183, 0.1);
                color: #673ab7;
              }
              .card-title {
                color: #673ab7;
              }
              .card-actions {
                .el-button[plain] {
                  color: #673ab7;
                  border-color: #673ab7;
                }
              }
            }

            &.green {
              .card-icon {
                background-color: rgba(103, 194, 58, 0.1);
                color: #67c23a;
              }
              .card-title {
                color: #67c23a;
              }
              .card-actions {
                .el-button[plain] {
                  color: #67c23a;
                  border-color: #67c23a;
                }
              }
            }

            &.yellow {
              .card-icon {
                background-color: rgba(230, 162, 60, 0.1);
                color: #e6a23c;
              }
              .card-title {
                color: #e6a23c;
              }
              .card-actions {
                .el-button[plain] {
                  color: #e6a23c;
                  border-color: #e6a23c;
                }
              }
            }

            &.red {
              .card-icon {
                background-color: rgba(245, 108, 108, 0.1);
                color: #f56c6c;
              }
              .card-title {
                color: #f56c6c;
              }
              .card-actions {
                .el-button[plain] {
                  color: #f56c6c;
                  border-color: #f56c6c;
                }
              }
            }

            .card-icon {
              width: 40px;
              height: 40px;
              display: flex;
              justify-content: center;
              align-items: center;
              margin-bottom: 16px;
              font-size: 20px;              
              border-radius: 50%;
            }

            .card-content {
              flex-grow: 1;
              margin-bottom: 16px;

              .card-title {
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
                color: #303133;
              }

              .card-time, .card-staff {
                font-size: 13px;
                color: #909399;
                margin-bottom: 4px;
              }
            }

            .card-status {
              position: absolute;
              top: 16px;
              right: 16px;
              font-size: 12px;
              padding: 2px 8px;
              border-radius: 12px;
              background-color: #ecf5ff;
              color: #409eff;

              &.today-status {
                background-color: #f0f9eb;
                color: #67c23a;
              }

              &.week-status {
                background-color: #f2f6fc;
                color: #606266;
              }
            }

            .card-actions {
              display: flex;
              justify-content: space-between;

              .el-button {
                padding: 8px 12px;
                font-size: 13px;
              }
            }
          }
        }

        // 今日工作记录
        .today-work-records {
          background-color: #fff;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
          padding: 20px;

          .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #303133;
            position: relative;
            padding-bottom: 10px;

            &:after {
              content: '';
              position: absolute;
              bottom: 0;
              left: 0;
              width: 40px;
              height: 3px;
              background-color: #409eff;
              border-radius: 3px;
            }
          }

          .record-item {
            display: flex;
            padding: 16px 0;
            border-bottom: 1px solid #eee;

            &:last-child {
              border-bottom: none;
            }

            .record-icon {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              display: flex;
              justify-content: center;
              align-items: center;
              margin-right: 16px;
              font-size: 20px;

              &.blue {
                background-color: rgba(64, 158, 255, 0.1);
                color: #409eff;
                & + .record-content .record-header .record-title {
                  color: #409eff;
                }
              }

              &.purple {
                background-color: rgba(144, 147, 253, 0.1);
                color: #9093fd;
                & + .record-content .record-header .record-title {
                  color: #9093fd;
                }
              }

              &.indigo {
                background-color: rgba(103, 58, 183, 0.1);
                color: #673ab7;
                & + .record-content .record-header .record-title {
                  color: #673ab7;
                }
              }
            }

            .record-content {
              flex-grow: 1;

              .record-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;

                .record-title {
                  font-size: 16px;
                  font-weight: bold;
                  color: #303133;
                }
              }

              .record-info {
                font-size: 14px;
                color: #606266;

                .record-time {
                  font-size: 12px;
                  color: #909399;
                  margin-top: 4px;
                }
              }
            }
          }
        }
      }

      // 我的提醒选项卡样式
      .my-reminder-content {
        .reminder-cards {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          gap: 16px;
          
          .reminder-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            padding: 16px;

            .reminder-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 12px;

              .reminder-title {
                font-size: 16px;
                font-weight: bold;
              }

              .reminder-status {
                font-size: 12px;
                padding: 2px 6px;
                border-radius: 4px;

                &.completed {
                  background-color: #f0f9eb;
                  color: #67c23a;
                }

                &.pending {
                  background-color: #fdf6ec;
                  color: #e6a23c;
                }
              }
            }

            .reminder-body {
              margin-bottom: 16px;

              .reminder-time {
                font-size: 12px;
                color: #999;
                margin-bottom: 8px;
              }

              .reminder-desc {
                font-size: 14px;
                color: #666;
              }
            }

            .reminder-actions {
              display: flex;
              justify-content: flex-end;
              gap: 8px;
            }
          }
        }
      }

      // 我的待办选项卡样式
      .my-todo-content {
        .todo-header {
          margin-bottom: 20px;
          display: flex;
          justify-content: flex-end;
          
          .el-button {
            background-color: #409eff;
            border-color: #409eff;
            color: #fff;
            border-radius: 4px;
            padding: 8px 16px;
            font-size: 14px;
            
            .el-icon {
              margin-right: 4px;
            }
            
            &:hover {
              background-color: #66b1ff;
              border-color: #66b1ff;
            }
          }
        }

        .todo-columns {
          display: flex;
          gap: 20px;
          height: calc(100vh - 40px);
          .todo-column {
            flex: 1;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            display: flex;
            flex-direction: column;
            overflow: hidden;

            &:nth-child(1) {
              border-top: 4px solid #409eff;
              .column-header {
                .column-title {
                  color: #409eff;
                  .count {
                    background-color: #409eff;
                  }
                }
              }
              .task-item {
                border-left-color: #409eff;
              }
            }

            &:nth-child(2) {
              border-top: 4px solid #e6a23c;
              .column-header {
                .column-title {
                  color: #e6a23c;
                  .count {
                    background-color: #e6a23c;
                  }
                }
              }
              .task-item {
                border-left-color: #e6a23c;
              }
            }

            &:nth-child(3) {
              border-top: 4px solid #67c23a;
              .column-header {
                .column-title {
                  color: #67c23a;
                  .count {
                    background-color: #67c23a;
                  }
                }
              }
              .task-item {
                border-left-color: #67c23a;
              }
            }

            .column-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 16px;
              border-bottom: 1px solid #ebeef5;
              background-color: #fafafa;

              .column-title {
                font-size: 16px;
                font-weight: bold;
                display: flex;
                align-items: center;

                .count {
                  color: #fff;
                  border-radius: 12px;
                  padding: 0 8px;
                  font-size: 12px;
                  margin-left: 8px;
                  height: 20px;
                  line-height: 20px;
                  display: inline-flex;
                  align-items: center;
                  justify-content: center;
                }
              }
            }

            .task-list {
              padding: 16px;
              flex: 1;
              overflow-y: auto;
              min-height: 200px;
              
              &::-webkit-scrollbar {
                width: 6px;
              }
              
              &::-webkit-scrollbar-thumb {
                background-color: #dcdfe6;
                border-radius: 3px;
              }
              
              &::-webkit-scrollbar-track {
                background-color: #f5f7fa;
              }

              // 拖拽时的样式
              .ghost-task {
                opacity: 0.5;
                background: #f0f7ff;
                border: 1px dashed #409eff;
              }

              .chosen-task {
                background-color: #ecf5ff;
              }

              .drag-task {
                transform: rotate(1deg);
                box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
              }

              .task-item {
                display: flex;
                flex-direction: column;
                padding: 12px 16px;
                background-color: #fff;
                border-radius: 6px;
                margin-bottom: 12px;
                cursor: move;
                transition: all 0.2s ease;
                position: relative;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
                border-left: 4px solid #409eff;
                position: relative;
                .right_delete {
                  position: absolute;
                  right: 0;
                  top: 10px;
                  transform: rotate(90deg);
                  display: none;
                }
                &:hover {
                  background-color: #f9fbff;
                  transform: translateY(-2px);
                  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
                  .right_delete{
                    display: block;
                    cursor: pointer;
                  }
                }

                &.important {
                  border-left: 4px solid #f56c6c;
                  
                  &:before {
                    content: '';
                    position: absolute;
                    top: 8px;
                    right: 8px;
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    background-color: #f56c6c;
                  }
                }

                &:last-child {
                  margin-bottom: 0;
                }

                .task-date {
                  font-size: 12px;
                  color: #909399;
                  margin-bottom: 8px;
                  display: flex;
                  align-items: center;
                  
                  // &:before {
                  //   content: '';
                  //   display: inline-block;
                  //   width: 12px;
                  //   height: 12px;
                  //   border-radius: 50%;
                  //   margin-right: 6px;
                  // }
                }
                
                // 根据所在列设置日期指示器的颜色
                #planned-list & .task-date:before {
                  background-color: #409eff;
                }
                
                #inprogress-list & .task-date:before {
                  background-color: #e6a23c;
                }
                
                #completed-list & .task-date:before {
                  background-color: #67c23a;
                }

                .task-content {
                  flex-grow: 1;

                  .task-title {
                    font-size: 14px;
                    margin-bottom: 4px;
                    font-weight: 500;
                    color: #303133;
                    line-height: 1.4;
                    word-break: break-all;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                  }

                  .task-desc {
                    font-size: 12px;
                    color: #606266;
                  }
                }
                .task-actions{
                  display: flex;
                  justify-content: space-between;
                }
                .task-status {
                  font-size: 12px;
                  padding: 2px 8px;
                  border-radius: 12px;
                  text-align: center;
                  // position: absolute;
                  // top: 12px;
                  // right: 12px;

                  &.planned {
                    background-color: #ecf5ff;
                    color: #409eff;
                  }

                  &.inProgress {
                    background-color: #fdf6ec;
                    color: #e6a23c;
                  }

                  &.completed {
                    background-color: #f0f9eb;
                    color: #67c23a;
                  }
                }
                
                // 根据所在列设置任务项的左边框颜色
                #planned-list & {
                  border-left-color: #409eff;
                }
                
                #inprogress-list & {
                  border-left-color: #e6a23c;
                }
                
                #completed-list & {
                  border-left-color: #67c23a;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 覆盖Element Plus日历样式
:deep(.el-calendar) {
  --el-calendar-cell-width: 40px;
  --el-calendar-header-height: auto;

  .el-calendar__header {
    padding: 12px 0;
  }

  .el-calendar__body {
    padding: 0;
  }

  .el-calendar-day {
    height: 40px;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.mb20{
  margin-bottom: 30px;
}
.no-reminder{
  text-align: center;
  padding:10px 0;
  color: #999999;
}
.reminder-item-tx{
  display: flex;
  flex-direction: column;
  cursor: pointer;
  margin-bottom: 16px!important;
  padding-bottom: 16px!important;
  border-bottom: 1px solid #eee!important;
}
.reminder-time-tx{
  display: flex;
  justify-content: space-between;
  width: 100%;
  font-size: 14px;
  cursor: pointer;
  color:#999999
}
.reminder-desc-tx{
    font-size: 14px;
    color: #666;
    padding-top: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .dialog-content{
    background: rgba(71, 96, 243, 1);
    height: 147px;
    line-height: 147px;
    text-align: center;
    font-size:40px;
  }
  .content_tip{
    font-size: 18px;
    color:#333333;
    text-align: center;
    padding: 80px 0;
    font-weight: bold;
  }
  .taskBorder2{
    border-left:4px solid#e6a23c!important;
  }
  .taskBorder3{
    border-left:4px solid #67c23a!important;
  }
  :deep(.el-calendar) {
  .el-calendar__body {
    padding: 0;
    
    .el-calendar-table {
      .el-calendar-day {
        padding: 0;
        height: 40px;
        
        .calendar-cell {
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;
          
          // 保持选中状态样式
          &.is-selected {
            background-color: var(--el-color-primary-light-9);
            
            .date {
              color: var(--el-color-primary);
              font-weight: bold;
            }
          }
          
          // 保持今天状态样式
          &.is-today {
            .date {
              color: var(--el-color-primary);
            }
          }
        }
      }
    }
  }
}
</style>
