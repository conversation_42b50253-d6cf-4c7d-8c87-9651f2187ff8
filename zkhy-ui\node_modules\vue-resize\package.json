{"name": "vue-resize", "description": "Detect DOM element resizing", "version": "0.4.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["vue", "v<PERSON><PERSON><PERSON>", "plugin"], "license": "MIT", "main": "dist/vue-resize.umd.js", "module": "dist/vue-resize.esm.js", "unpkg": "dist/vue-resize.min.js", "scripts": {"build": "yarn run build:browser && yarn run build:es && yarn run build:umd", "build:browser": "rollup --config build/rollup.config.browser.js", "build:es": "rollup --config build/rollup.config.es.js", "build:umd": "rollup --config build/rollup.config.umd.js", "prepublishOnly": "yarn run test", "dev": "cross-env NODE_ENV=development rollup --config build/rollup.config.es.js --watch", "test": "yarn run build && cd ./docs-src && yarn run test"}, "watch": {"build": "src/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/Akryum/vue-resize.git"}, "bugs": {"url": "https://github.com/Akryum/vue-resize/issues"}, "homepage": "https://github.com/Akryum/vue-resize#readme", "devDependencies": {"babel-core": "^6.26.0", "babel-eslint": "^8.0.1", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-env": "^1.6.1", "babel-preset-stage-0": "^6.16.0", "clean-css": "^4.1.8", "cross-env": "^5.0.5", "eslint": "^4.6.1", "eslint-config-standard": "^10.2.1", "eslint-plugin-html": "^3.2.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.1.1", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^3.0.1", "npm-watch": "^0.3.0", "rollup": "^0.50.0", "rollup-plugin-babel": "^3.0.2", "rollup-plugin-commonjs": "^8.2.0", "rollup-plugin-css-only": "^0.2.0", "rollup-plugin-css-porter": "^0.1.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-stylus-css-modules": "^1.5.0", "rollup-plugin-uglify": "^2.0.1", "rollup-plugin-vue": "^2.5.2", "uglify-es": "^3.0.28", "vue-template-compiler": "^2.5.2"}, "peerDependencies": {"vue": "^2.3.0"}}