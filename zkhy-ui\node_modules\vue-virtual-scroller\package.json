{"name": "vue-virtual-scroller", "description": "Smooth scrolling for any amount of data", "version": "1.1.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["vue", "v<PERSON><PERSON><PERSON>", "plugin"], "license": "MIT", "main": "dist/vue-virtual-scroller.umd.js", "module": "dist/vue-virtual-scroller.esm.js", "unpkg": "dist/vue-virtual-scroller.min.js", "scripts": {"build": "npm run build:browser && npm run build:es && npm run build:umd", "build:browser": "rollup --config build/rollup.config.browser.js", "build:es": "rollup --config build/rollup.config.es.js", "build:umd": "rollup --config build/rollup.config.umd.js", "prepublishOnly": "npm run lint && npm run build", "dev": "cross-env NODE_ENV=development rollup --config build/rollup.config.es.js --watch", "lint": "eslint --ext .js,.vue src"}, "repository": {"type": "git", "url": "git+https://github.com/Akryum/vue-virtual-scroller.git"}, "bugs": {"url": "https://github.com/Akryum/vue-virtual-scroller/issues"}, "homepage": "https://github.com/Akryum/vue-virtual-scroller#readme", "dependencies": {"scrollparent": "^2.0.1", "vue-observe-visibility": "^0.4.4", "vue-resize": "^0.4.5"}, "peerDependencies": {"vue": "^2.6.11"}, "devDependencies": {"@babel/core": "^7.9.0", "@babel/preset-env": "^7.9.0", "@rollup/plugin-commonjs": "^11.0.2", "@rollup/plugin-node-resolve": "^7.1.1", "@rollup/plugin-replace": "^2.3.1", "autoprefixer": "^9.7.5", "clean-css": "^4.1.8", "cross-env": "^7.0.2", "fs-extra": "^10.1.0", "rollup": "^2.2.0", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-css-only": "^2.0.0", "rollup-plugin-css-porter": "^1.0.2", "rollup-plugin-terser": "^5.3.0", "rollup-plugin-vue": "^5.1.6", "vue": "^2.5.17", "vue-template-compiler": "^2.6.11"}, "files": ["dist"], "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}