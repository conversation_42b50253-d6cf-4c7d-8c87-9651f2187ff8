<template>
  <div class="drug-receive-record-container">
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="收药时间:">
        <el-date-picker
          v-model="searchForm.receiveTime"
          type="date"
          placeholder="选择日期"
          value-format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="老人姓名:">
        <el-input v-model="searchForm.elderName" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="楼栋信息:">
        <el-select v-model="searchForm.buildingInfo" placeholder="全部">
          <el-option label="全部" value=""></el-option>
          <el-option label="1号楼" value="1"></el-option>
          <el-option label="2号楼" value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="楼栋层数:">
        <el-select v-model="searchForm.buildingFloor" placeholder="全部">
          <el-option label="全部" value=""></el-option>
          <el-option label="1层" value="1"></el-option>
          <el-option label="2层" value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="房间号:">
        <el-input v-model="searchForm.roomNumber" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="药品名称:">
        <el-input v-model="searchForm.drugName" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="有效期:">
        <el-date-picker
          v-model="searchForm.validityPeriod"
          type="date"
          placeholder="选择日期"
          value-format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="送药人:">
        <el-input v-model="searchForm.deliverer" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="收取人:">
        <el-input v-model="searchForm.receiver" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item class="button-group">
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button @click="onReset">重置</el-button>
        <el-button type="primary" @click="onAddNewDrug">新增药品</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="id" label="序号" width="60"></el-table-column>
      <el-table-column prop="receiveTime" label="收药时间"></el-table-column>
      <el-table-column prop="elderName" label="老人姓名"></el-table-column>
      <el-table-column prop="buildingInfo" label="楼栋信息"></el-table-column>
      <el-table-column prop="roomNumber" label="房间号"></el-table-column>
      <el-table-column prop="bedNumber" label="床位号"></el-table-column>
      <el-table-column prop="drugName" label="药品名称"></el-table-column>
      <el-table-column prop="drugSpec" label="药品规格"></el-table-column>
      <el-table-column prop="drugCount" label="药品数量"></el-table-column>
      <el-table-column prop="usage" label="用量"></el-table-column>
      <el-table-column prop="method" label="服用方法"></el-table-column>
      <el-table-column prop="validityPeriod" label="有效期"></el-table-column>
      <el-table-column prop="producer" label="生产厂家"></el-table-column>
      <el-table-column prop="deliverer" label="送药人"></el-table-column>
      <el-table-column prop="receiver" label="收取人"></el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="onView(scope.row)">查看</el-button>
          <el-button link type="danger" @click="onDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container" v-if="pagination.total > 0">
      <el-pagination
        v-model:limit="pagination.pageSize"
        v-model:page="pagination.currentPage"
        :total="pagination.total"
        background
        layout="total, sizes, prev, pager, next, jumper"
        @pagination="handlePagination"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const searchForm = ref({
  receiveTime: '',
  elderName: '',
  buildingInfo: '',
  buildingFloor: '',
  roomNumber: '',
  drugName: '',
  validityPeriod: '',
  deliverer: '',
  receiver: '',
});

const tableData = ref([]);

const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

const onSearch = () => {
  console.log('查询', searchForm.value);
  fetchData();
};

const onReset = () => {
  searchForm.value = {
    receiveTime: '',
    elderName: '',
    buildingInfo: '',
    buildingFloor: '',
    roomNumber: '',
    drugName: '',
    validityPeriod: '',
    deliverer: '',
    receiver: '',
  };
  fetchData();
};

const onAddNewDrug = () => {
  console.log('新增药品');
  // Implement add new drug logic
};

const onView = (row) => {
  console.log('查看', row);
  // Implement view logic
};

const onDelete = (row) => {
  console.log('删除', row);
  // Implement delete logic
};

const handlePagination = ({ page, limit }) => {
  pagination.value.currentPage = page;
  pagination.value.pageSize = limit;
  fetchData();
};

const fetchData = () => {
  // Mock data based on the provided image
  const mockData = [
    {
      id: 1,
      receiveTime: '2025-08-25',
      elderName: '秦朝阳',
      buildingInfo: '3号楼',
      roomNumber: '301',
      bedNumber: '301-1',
      drugName: '硝苯地平缓释片',
      drugSpec: '10mg×30片/盒',
      drugCount: 30,
      usage: '1-2次,每次10-20mg',
      method: '口服',
      validityPeriod: '2027-05-25',
      producer: '北京同仁堂制药厂',
      deliverer: '张双井',
      receiver: '王护士',
    },
    {
      id: 2,
      receiveTime: '2025-08-25',
      elderName: '王海定',
      buildingInfo: '3号楼',
      roomNumber: '302',
      bedNumber: '302-2',
      drugName: '氯沙坦钾片',
      drugSpec: '10mg×30片/盒',
      drugCount: 30,
      usage: '1-2次,每次10-20mg',
      method: '口服',
      validityPeriod: '2027-05-25',
      producer: '北京同仁堂制药厂',
      deliverer: '张双井',
      receiver: '王护士',
    },
    {
      id: 3,
      receiveTime: '2025-08-25',
      elderName: '刘里桥',
      buildingInfo: '3号楼',
      roomNumber: '304',
      bedNumber: '304-3',
      drugName: '苯磺酸左旋氨氯地平片',
      drugSpec: '10mg×30片/盒',
      drugCount: 30,
      usage: '1-2次,每次10-20mg',
      method: '口服',
      validityPeriod: '2027-05-25',
      producer: '北京同仁堂制药厂',
      deliverer: '张双井',
      receiver: '王护士',
    },
    {
      id: 4,
      receiveTime: '2025-08-25',
      elderName: '李金台',
      buildingInfo: '3号楼',
      roomNumber: '305',
      bedNumber: '305-4',
      drugName: '硝酸咪康唑片',
      drugSpec: '10mg×30片/盒',
      drugCount: 30,
      usage: '1-2次,每次10-20mg',
      method: '口服',
      validityPeriod: '2027-05-25',
      producer: '北京同仁堂制药厂',
      deliverer: '张双井',
      receiver: '王护士',
    },
    {
      id: 5,
      receiveTime: '2025-08-25',
      elderName: '李长安',
      buildingInfo: '3号楼',
      roomNumber: '402',
      bedNumber: '402-5',
      drugName: '富马酸比索洛尔片',
      drugSpec: '10mg×30片/盒',
      drugCount: 30,
      usage: '1-2次,每次10-20mg',
      method: '口服',
      validityPeriod: '2027-05-25',
      producer: '北京同仁堂制药厂',
      deliverer: '张双井',
      receiver: '王护士',
    },
    {
      id: 6,
      receiveTime: '2025-08-25',
      elderName: '曾秀清',
      buildingInfo: '3号楼',
      roomNumber: '406',
      bedNumber: '406-1',
      drugName: '硝苯地平缓释片',
      drugSpec: '10mg×30片/盒',
      drugCount: 30,
      usage: '1-2次,每次10-20mg',
      method: '口服',
      validityPeriod: '2027-05-25',
      producer: '北京同仁堂制药厂',
      deliverer: '张双井',
      receiver: '王护士',
    },
    {
      id: 7,
      receiveTime: '2025-08-25',
      elderName: '杨洋',
      buildingInfo: '3号楼',
      roomNumber: '408',
      bedNumber: '408-2',
      drugName: '硝苯地平缓释片',
      drugSpec: '10mg×30片/盒',
      drugCount: 30,
      usage: '1-2次,每次10-20mg',
      method: '口服',
      validityPeriod: '2027-05-25',
      producer: '北京同仁堂制药厂',
      deliverer: '张双井',
      receiver: '王护士',
    },
    {
      id: 8,
      receiveTime: '2025-08-25',
      elderName: '杨洋',
      buildingInfo: '3号楼',
      roomNumber: '409',
      bedNumber: '409-3',
      drugName: '氯沙坦钾片',
      drugSpec: '10mg×30片/盒',
      drugCount: 30,
      usage: '1-2次,每次10-20mg',
      method: '口服',
      validityPeriod: '2027-05-25',
      producer: '北京同仁堂制药厂',
      deliverer: '张双井',
      receiver: '王护士',
    },
    {
      id: 9,
      receiveTime: '2025-08-25',
      elderName: '魏鼎通',
      buildingInfo: '3号楼',
      roomNumber: '501',
      bedNumber: '501-4',
      drugName: '苯磺酸左旋氨氯地平片',
      drugSpec: '10mg×30片/盒',
      drugCount: 30,
      usage: '1-2次,每次10-20mg',
      method: '口服',
      validityPeriod: '2027-05-25',
      producer: '北京同仁堂制药厂',
      deliverer: '张双井',
      receiver: '王护士',
    },
    {
      id: 10,
      receiveTime: '2025-08-25',
      elderName: '魏鼎通',
      buildingInfo: '3号楼',
      roomNumber: '505',
      bedNumber: '505-5',
      drugName: '硝酸咪康唑片',
      drugSpec: '10mg×30片/盒',
      drugCount: 30,
      usage: '1-2次,每次10-20mg',
      method: '口服',
      validityPeriod: '2027-05-25',
      producer: '北京同仁堂制药厂',
      deliverer: '张双井',
      receiver: '王护士',
    },
  ];

  // Simulate pagination
  const start = (pagination.value.currentPage - 1) * pagination.value.pageSize;
  const end = start + pagination.value.pageSize;
  tableData.value = mockData.slice(start, end);
  pagination.value.total = mockData.length;
};

onMounted(() => {
  fetchData();
});
</script>

<style scoped>
.drug-receive-record-container {
  padding: 0px;
}

.search-form .el-form-item {
  margin-bottom: 10px;
  margin-right: 20px;
}

.search-form .button-group {
  display: flex;
  justify-content: flex-end;
  flex-grow: 1;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  background-color: #fff;
  padding: 10px 0;
  border-radius: 4px;
}
</style>