摆药卡大屏主要按照天和周展示老人的用药情况。
技术要求采用vue3+element-plus进行开发。
选项卡按钮：天、周。点击选项卡分别动态切换展示内容。
1、 按照天展示：可以选中日期，展示当天的用药情况，日期可以动态选择天。
    分为早、中、晚三部分，分别显示吃的药的名称、数量、状态（是否已服用）。
2、 按照周展示：可以选中周，展示当周的用药情况，日期可以动态选择周。
    分为七天，分别显示每天的早、中、晚三部分的药的名称、数量、状态。
3、顶部查询条件：
    根据页面实际情况进行合理布局，让其更加美观大方，只要求修改顶部样式，不要修改页面布局以及其他样式。
    3.1、 日期选择：
        3.1.1、 按照天展示：可以选中日期，展示当天的用药情况，日期可以动态选择天。要求日期添加按钮前一天和后一天。
        3.1.2、 按照周展示：可以选中周，展示当周的用药情况，日期可以动态选择周。要求日期添加按钮前y一周和后一周。
    3.2、 搜索框位置位于选项卡下面,中间要求有间隔:
        3.2.1、 搜索老人姓名。
        3.2.2、 搜索框可以动态搜索楼栋下拉框。
        3.2.3、 搜索框可以动态搜索楼层下拉框。
        3.2.4、 搜索框可以动态搜索老人姓名/房间号。
        3.2.5、 要求搜索条件可以进行动态筛选天和周的结果。
    3.3、 右侧有三个按钮：早上、中午、晚上。
        3.3.1、 点击早上、中午、晚上，可以动态切换展示早上、中午、晚上的周和天的用药情况。
        3.3.2、 按钮下方统计数量，统计该时间段内该老人已使用药品的个数，例如：早上：35人，中午：20人，晚上：15人。
        3.3.3、 统计样式要求：和搜索框在一行上面；
。

        
卡片展示要求：
    1、 天用药情况展示，按照老人的姓名展示，每个老人展示一个房间号和床位号的用药情况。
    2、 周用药情况展示，按照老人的姓名展示，每个老人展示一个房间号和床位号的，从周一到周日的用药情况。
    3、 卡片内展示形式要求。
        3.1、 早上、中午、 晚上三部分，分别显示吃的药的名称、数量、状态（是否已服用）。
        3.2、 布局形式：
            3.2.1、 横向排列，两侧对齐，最上面是早上的（早上左侧对齐，是否已服用右侧对齐），最下面是晚上的（晚上的左侧对齐，是否已服用右侧对齐），中间是中午的（中午左侧对齐，是否已服用右侧对齐）。
            3.2.2、 每个药的名称左侧对齐、数量右侧对齐。
            3.2.3、 每个药的名称和（早、中、晚）都是左侧对齐，数量和（早、中、晚右侧的是否已服用）都是右侧对齐。
           
        