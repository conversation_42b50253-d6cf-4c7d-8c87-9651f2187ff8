var VueVirtualScroller=function(e,t){"use strict";function i(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var s=i(t),r={itemsLimit:1e3};var n=void 0;function o(){o.init||(o.init=!0,n=-1!==function(){var e=window.navigator.userAgent,t=e.indexOf("MSIE ");if(t>0)return parseInt(e.substring(t+5,e.indexOf(".",t)),10);if(e.indexOf("Trident/")>0){var i=e.indexOf("rv:");return parseInt(e.substring(i+3,e.indexOf(".",i)),10)}var s=e.indexOf("Edge/");return s>0?parseInt(e.substring(s+5,e.indexOf(".",s)),10):-1}())}var l={render:function(){var e=this.$createElement;return(this._self._c||e)("div",{staticClass:"resize-observer",attrs:{tabindex:"-1"}})},staticRenderFns:[],_scopeId:"data-v-b329ee4c",name:"resize-observer",methods:{compareAndNotify:function(){this._w===this.$el.offsetWidth&&this._h===this.$el.offsetHeight||(this._w=this.$el.offsetWidth,this._h=this.$el.offsetHeight,this.$emit("notify"))},addResizeHandlers:function(){this._resizeObject.contentDocument.defaultView.addEventListener("resize",this.compareAndNotify),this.compareAndNotify()},removeResizeHandlers:function(){this._resizeObject&&this._resizeObject.onload&&(!n&&this._resizeObject.contentDocument&&this._resizeObject.contentDocument.defaultView.removeEventListener("resize",this.compareAndNotify),delete this._resizeObject.onload)}},mounted:function(){var e=this;o(),this.$nextTick((function(){e._w=e.$el.offsetWidth,e._h=e.$el.offsetHeight}));var t=document.createElement("object");this._resizeObject=t,t.setAttribute("aria-hidden","true"),t.setAttribute("tabindex",-1),t.onload=this.addResizeHandlers,t.type="text/html",n&&this.$el.appendChild(t),t.data="about:blank",n||this.$el.appendChild(t)},beforeDestroy:function(){this.removeResizeHandlers()}};var a={version:"0.4.5",install:function(e){e.component("resize-observer",l),e.component("ResizeObserver",l)}},c=null;function d(e){return(d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function h(e,t){for(var i=0;i<t.length;i++){var s=t[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}function u(e){return function(e){if(Array.isArray(e)){for(var t=0,i=new Array(e.length);t<e.length;t++)i[t]=e[t];return i}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}"undefined"!=typeof window?c=window.Vue:"undefined"!=typeof global&&(c=global.Vue),c&&c.use(a);var f=function(){function e(t,i,s){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.el=t,this.observer=null,this.frozen=!1,this.createObserver(i,s)}var t,i,s;return t=e,(i=[{key:"createObserver",value:function(e,t){var i=this;if(this.observer&&this.destroyObserver(),!this.frozen){var s;if(this.options="function"==typeof(s=e)?{callback:s}:s,this.callback=function(e,t){i.options.callback(e,t),e&&i.options.once&&(i.frozen=!0,i.destroyObserver())},this.callback&&this.options.throttle){var r=(this.options.throttleOptions||{}).leading;this.callback=function(e,t){var i,s,r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=function(o){for(var l=arguments.length,a=new Array(l>1?l-1:0),c=1;c<l;c++)a[c-1]=arguments[c];if(r=a,!i||o!==s){var d=n.leading;"function"==typeof d&&(d=d(o,s)),i&&o===s||!d||e.apply(void 0,[o].concat(u(r))),s=o,clearTimeout(i),i=setTimeout((function(){e.apply(void 0,[o].concat(u(r))),i=0}),t)}};return o._clear=function(){clearTimeout(i),i=null},o}(this.callback,this.options.throttle,{leading:function(e){return"both"===r||"visible"===r&&e||"hidden"===r&&!e}})}this.oldResult=void 0,this.observer=new IntersectionObserver((function(e){var t=e[0];if(e.length>1){var s=e.find((function(e){return e.isIntersecting}));s&&(t=s)}if(i.callback){var r=t.isIntersecting&&t.intersectionRatio>=i.threshold;if(r===i.oldResult)return;i.oldResult=r,i.callback(r,t)}}),this.options.intersection),t.context.$nextTick((function(){i.observer&&i.observer.observe(i.el)}))}}},{key:"destroyObserver",value:function(){this.observer&&(this.observer.disconnect(),this.observer=null),this.callback&&this.callback._clear&&(this.callback._clear(),this.callback=null)}},{key:"threshold",get:function(){return this.options.intersection&&this.options.intersection.threshold||0}}])&&h(t.prototype,i),s&&h(t,s),e}();function p(e,t,i){var s=t.value;if(s)if("undefined"==typeof IntersectionObserver)console.warn("[vue-observe-visibility] IntersectionObserver API is not available in your browser. Please install this polyfill: https://github.com/w3c/IntersectionObserver/tree/master/polyfill");else{var r=new f(e,s,i);e._vue_visibilityState=r}}function v(e){var t=e._vue_visibilityState;t&&(t.destroyObserver(),delete e._vue_visibilityState)}var m={bind:p,update:function(e,t,i){var s=t.value;if(!function e(t,i){if(t===i)return!0;if("object"===d(t)){for(var s in t)if(!e(t[s],i[s]))return!1;return!0}return!1}(s,t.oldValue)){var r=e._vue_visibilityState;s?r?r.createObserver(s,i):p(e,{value:s},i):v(e)}},unbind:v};var y={version:"0.4.6",install:function(e){e.directive("observe-visibility",m)}},b=null;"undefined"!=typeof window?b=window.Vue:"undefined"!=typeof global&&(b=global.Vue),b&&b.use(y);var g="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};var _=function(e,t){return e(t={exports:{}},t.exports),t.exports}((function(e){var t,i;t=g,i=function(){var e=/(auto|scroll)/,t=function(e,i){return null===e.parentNode?i:t(e.parentNode,i.concat([e]))},i=function(e,t){return getComputedStyle(e,null).getPropertyValue(t)},s=function(t){return e.test(function(e){return i(e,"overflow")+i(e,"overflow-y")+i(e,"overflow-x")}(t))};return function(e){if(e instanceof HTMLElement||e instanceof SVGElement){for(var i=t(e.parentNode,[]),r=0;r<i.length;r+=1)if(s(i[r]))return i[r];return document.scrollingElement||document.documentElement}}},e.exports?e.exports=i():t.Scrollparent=i()}));const $={items:{type:Array,required:!0},keyField:{type:String,default:"id"},direction:{type:String,default:"vertical",validator:e=>["vertical","horizontal"].includes(e)},listTag:{type:String,default:"div"},itemTag:{type:String,default:"div"}};function S(){return this.items.length&&"object"!=typeof this.items[0]}let z=!1;if("undefined"!=typeof window){z=!1;try{var w=Object.defineProperty({},"passive",{get(){z=!0}});window.addEventListener("test",null,w)}catch(e){}}let I=0;function x(e,t,i,s,r,n,o,l,a,c){"boolean"!=typeof o&&(a=l,l=o,o=!1);const d="function"==typeof i?i.options:i;let h;if(e&&e.render&&(d.render=e.render,d.staticRenderFns=e.staticRenderFns,d._compiled=!0,r&&(d.functional=!0)),s&&(d._scopeId=s),n?(h=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),t&&t.call(this,a(e)),e&&e._registeredComponents&&e._registeredComponents.add(n)},d._ssrRegister=h):t&&(h=o?function(e){t.call(this,c(e,this.$root.$options.shadowRoot))}:function(e){t.call(this,l(e))}),h)if(d.functional){const e=d.render;d.render=function(t,i){return h.call(i),e(t,i)}}else{const e=d.beforeCreate;d.beforeCreate=e?[].concat(e,h):[h]}return i}const T={name:"RecycleScroller",components:{ResizeObserver:l},directives:{ObserveVisibility:m},props:{...$,itemSize:{type:Number,default:null},gridItems:{type:Number,default:void 0},itemSecondarySize:{type:Number,default:void 0},minItemSize:{type:[Number,String],default:null},sizeField:{type:String,default:"size"},typeField:{type:String,default:"type"},buffer:{type:Number,default:200},pageMode:{type:Boolean,default:!1},prerender:{type:Number,default:0},emitUpdate:{type:Boolean,default:!1},skipHover:{type:Boolean,default:!1},listTag:{type:String,default:"div"},itemTag:{type:String,default:"div"},listClass:{type:[String,Object,Array],default:""},itemClass:{type:[String,Object,Array],default:""}},data:()=>({pool:[],totalSize:0,ready:!1,hoverKey:null}),computed:{sizes(){if(null===this.itemSize){const e={"-1":{accumulator:0}},t=this.items,i=this.sizeField,s=this.minItemSize;let r,n=1e4,o=0;for(let l=0,a=t.length;l<a;l++)r=t[l][i]||s,r<n&&(n=r),o+=r,e[l]={accumulator:o,size:r};return this.$_computedMinItemSize=n,e}return[]},simpleArray:S},watch:{items(){this.updateVisibleItems(!0)},pageMode(){this.applyPageMode(),this.updateVisibleItems(!1)},sizes:{handler(){this.updateVisibleItems(!1)},deep:!0},gridItems(){this.updateVisibleItems(!0)},itemSecondarySize(){this.updateVisibleItems(!0)}},created(){this.$_startIndex=0,this.$_endIndex=0,this.$_views=new Map,this.$_unusedViews=new Map,this.$_scrollDirty=!1,this.$_lastUpdateScrollPosition=0,this.prerender&&(this.$_prerender=!0,this.updateVisibleItems(!1)),this.gridItems&&!this.itemSize&&console.error("[vue-recycle-scroller] You must provide an itemSize when using gridItems")},mounted(){this.applyPageMode(),this.$nextTick(()=>{this.$_prerender=!1,this.updateVisibleItems(!0),this.ready=!0})},activated(){const e=this.$_lastUpdateScrollPosition;"number"==typeof e&&this.$nextTick(()=>{this.scrollToPosition(e)})},beforeDestroy(){this.removeListeners()},methods:{addView(e,t,i,s,r){const n={item:i,position:0},o={id:I++,index:t,used:!0,key:s,type:r};return Object.defineProperty(n,"nr",{configurable:!1,value:o}),e.push(n),n},unuseView(e,t=!1){const i=this.$_unusedViews,s=e.nr.type;let r=i.get(s);r||(r=[],i.set(s,r)),r.push(e),t||(e.nr.used=!1,e.position=-9999,this.$_views.delete(e.nr.key))},handleResize(){this.$emit("resize"),this.ready&&this.updateVisibleItems(!1)},handleScroll(e){this.$_scrollDirty||(this.$_scrollDirty=!0,requestAnimationFrame(()=>{this.$_scrollDirty=!1;const{continuous:e}=this.updateVisibleItems(!1,!0);e||(clearTimeout(this.$_refreshTimout),this.$_refreshTimout=setTimeout(this.handleScroll,100))}))},handleVisibilityChange(e,t){this.ready&&(e||0!==t.boundingClientRect.width||0!==t.boundingClientRect.height?(this.$emit("visible"),requestAnimationFrame(()=>{this.updateVisibleItems(!1)})):this.$emit("hidden"))},updateVisibleItems(e,t=!1){const i=this.itemSize,s=this.gridItems||1,n=this.itemSecondarySize||i,o=this.$_computedMinItemSize,l=this.typeField,a=this.simpleArray?null:this.keyField,c=this.items,d=c.length,h=this.sizes,u=this.$_views,f=this.$_unusedViews,p=this.pool;let v,m,y,b,g,_;if(d)if(this.$_prerender)v=b=0,m=g=Math.min(this.prerender,c.length),y=null;else{const e=this.getScroll();if(t){let t=e.start-this.$_lastUpdateScrollPosition;if(t<0&&(t=-t),null===i&&t<o||t<i)return{continuous:!0}}this.$_lastUpdateScrollPosition=e.start;const r=this.buffer;e.start-=r,e.end+=r;let n=0;if(this.$refs.before&&(n=this.$refs.before.scrollHeight,e.start-=n),this.$refs.after){const t=this.$refs.after.scrollHeight;e.end+=t}if(null===i){let t,i,s=0,r=d-1,o=~~(d/2);do{i=o,t=h[o].accumulator,t<e.start?s=o:o<d-1&&h[o+1].accumulator>e.start&&(r=o),o=~~((s+r)/2)}while(o!==i);for(o<0&&(o=0),v=o,y=h[d-1].accumulator,m=o;m<d&&h[m].accumulator<e.end;m++);for(-1===m?m=c.length-1:(m++,m>d&&(m=d)),b=v;b<d&&n+h[b].accumulator<e.start;b++);for(g=b;g<d&&n+h[g].accumulator<e.end;g++);}else{v=~~(e.start/i*s);v-=v%s,m=Math.ceil(e.end/i*s),b=Math.max(0,Math.floor((e.start-n)/i*s)),g=Math.floor((e.end-n)/i*s),v<0&&(v=0),m>d&&(m=d),b<0&&(b=0),g>d&&(g=d),y=Math.ceil(d/s)*i}}else v=m=b=g=y=0;m-v>r.itemsLimit&&this.itemsLimitError(),this.totalSize=y;const $=v<=this.$_endIndex&&m>=this.$_startIndex;if(this.$_continuous!==$){if($){u.clear(),f.clear();for(let e=0,t=p.length;e<t;e++)_=p[e],this.unuseView(_)}this.$_continuous=$}else if($)for(let t=0,i=p.length;t<i;t++)_=p[t],_.nr.used&&(e&&(_.nr.index=c.indexOf(_.item)),(-1===_.nr.index||_.nr.index<v||_.nr.index>=m)&&this.unuseView(_));const S=$?null:new Map;let z,w,I,x;for(let e=v;e<m;e++){z=c[e];const t=a?z[a]:z;if(null==t)throw new Error(`Key is ${t} on item (keyField is '${a}')`);_=u.get(t),i||h[e].size?(_?(_.nr.used=!0,_.item=z):(e===c.length-1&&this.$emit("scroll-end"),0===e&&this.$emit("scroll-start"),w=z[l],I=f.get(w),$?I&&I.length?(_=I.pop(),_.item=z,_.nr.used=!0,_.nr.index=e,_.nr.key=t,_.nr.type=w):_=this.addView(p,e,z,t,w):(x=S.get(w)||0,(!I||x>=I.length)&&(_=this.addView(p,e,z,t,w),this.unuseView(_,!0),I=f.get(w)),_=I[x],_.item=z,_.nr.used=!0,_.nr.index=e,_.nr.key=t,_.nr.type=w,S.set(w,x+1),x++),u.set(t,_)),null===i?(_.position=h[e-1].accumulator,_.offset=0):(_.position=Math.floor(e/s)*i,_.offset=e%s*n)):_&&this.unuseView(_)}return this.$_startIndex=v,this.$_endIndex=m,this.emitUpdate&&this.$emit("update",v,m,b,g),clearTimeout(this.$_sortTimer),this.$_sortTimer=setTimeout(this.sortViews,300),{continuous:$}},getListenerTarget(){let e=_(this.$el);return!window.document||e!==window.document.documentElement&&e!==window.document.body||(e=window),e},getScroll(){const{$el:e,direction:t}=this,i="vertical"===t;let s;if(this.pageMode){const t=e.getBoundingClientRect(),r=i?t.height:t.width;let n=-(i?t.top:t.left),o=i?window.innerHeight:window.innerWidth;n<0&&(o+=n,n=0),n+o>r&&(o=r-n),s={start:n,end:n+o}}else s=i?{start:e.scrollTop,end:e.scrollTop+e.clientHeight}:{start:e.scrollLeft,end:e.scrollLeft+e.clientWidth};return s},applyPageMode(){this.pageMode?this.addListeners():this.removeListeners()},addListeners(){this.listenerTarget=this.getListenerTarget(),this.listenerTarget.addEventListener("scroll",this.handleScroll,!!z&&{passive:!0}),this.listenerTarget.addEventListener("resize",this.handleResize)},removeListeners(){this.listenerTarget&&(this.listenerTarget.removeEventListener("scroll",this.handleScroll),this.listenerTarget.removeEventListener("resize",this.handleResize),this.listenerTarget=null)},scrollToItem(e){let t;t=null===this.itemSize?e>0?this.sizes[e-1].accumulator:0:Math.floor(e/this.gridItems)*this.itemSize,this.scrollToPosition(t)},scrollToPosition(e){const t="vertical"===this.direction?{scroll:"scrollTop",start:"top"}:{scroll:"scrollLeft",start:"left"};let i,s,r;if(this.pageMode){const n=_(this.$el),o="HTML"===n.tagName?0:n[t.scroll],l=n.getBoundingClientRect(),a=this.$el.getBoundingClientRect()[t.start]-l[t.start];i=n,s=t.scroll,r=e+o+a}else i=this.$el,s=t.scroll,r=e;i[s]=r},itemsLimitError(){throw setTimeout(()=>{console.log("It seems the scroller element isn't scrolling, so it tries to render all the items at once.","Scroller:",this.$el),console.log("Make sure the scroller has a fixed height (or width) and 'overflow-y' (or 'overflow-x') set to 'auto' so it can scroll correctly and only render the items visible in the scroll viewport.")}),new Error("Rendered items limit reached")},sortViews(){this.pool.sort((e,t)=>e.nr.index-t.nr.index)}}};var O=function(){var e,t,i=this,s=i.$createElement,r=i._self._c||s;return r("div",{directives:[{name:"observe-visibility",rawName:"v-observe-visibility",value:i.handleVisibilityChange,expression:"handleVisibilityChange"}],staticClass:"vue-recycle-scroller",class:(e={ready:i.ready,"page-mode":i.pageMode},e["direction-"+i.direction]=!0,e),on:{"&scroll":function(e){return i.handleScroll.apply(null,arguments)}}},[i.$slots.before?r("div",{ref:"before",staticClass:"vue-recycle-scroller__slot"},[i._t("before")],2):i._e(),i._v(" "),r(i.listTag,{ref:"wrapper",tag:"component",staticClass:"vue-recycle-scroller__item-wrapper",class:i.listClass,style:(t={},t["vertical"===i.direction?"minHeight":"minWidth"]=i.totalSize+"px",t)},[i._l(i.pool,(function(e){return r(i.itemTag,i._g({key:e.nr.id,tag:"component",staticClass:"vue-recycle-scroller__item-view",class:[i.itemClass,{hover:!i.skipHover&&i.hoverKey===e.nr.key}],style:i.ready?{transform:"translate"+("vertical"===i.direction?"Y":"X")+"("+e.position+"px) translate"+("vertical"===i.direction?"X":"Y")+"("+e.offset+"px)",width:i.gridItems?("vertical"===i.direction&&i.itemSecondarySize||i.itemSize)+"px":void 0,height:i.gridItems?("horizontal"===i.direction&&i.itemSecondarySize||i.itemSize)+"px":void 0}:null},i.skipHover?{}:{mouseenter:function(){i.hoverKey=e.nr.key},mouseleave:function(){i.hoverKey=null}}),[i._t("default",null,{item:e.item,index:e.nr.index,active:e.nr.used})],2)})),i._v(" "),i._t("empty")],2),i._v(" "),i.$slots.after?r("div",{ref:"after",staticClass:"vue-recycle-scroller__slot"},[i._t("after")],2):i._e(),i._v(" "),r("ResizeObserver",{on:{notify:i.handleResize}})],1)};O._withStripped=!0;const V=x({render:O,staticRenderFns:[]},void 0,T,void 0,!1,void 0,!1,void 0,void 0,void 0);const R={name:"DynamicScroller",components:{RecycleScroller:V},provide(){return"undefined"!=typeof ResizeObserver&&(this.$_resizeObserver=new ResizeObserver(e=>{requestAnimationFrame(()=>{if(Array.isArray(e))for(const t of e)if(t.target){const e=new CustomEvent("resize",{detail:{contentRect:t.contentRect}});t.target.dispatchEvent(e)}})})),{vscrollData:this.vscrollData,vscrollParent:this,vscrollResizeObserver:this.$_resizeObserver}},inheritAttrs:!1,props:{...$,minItemSize:{type:[Number,String],required:!0}},data(){return{vscrollData:{active:!0,sizes:{},validSizes:{},keyField:this.keyField,simpleArray:!1}}},computed:{simpleArray:S,itemsWithSize(){const e=[],{items:t,keyField:i,simpleArray:s}=this,r=this.vscrollData.sizes,n=t.length;for(let o=0;o<n;o++){const n=t[o],l=s?o:n[i];let a=r[l];void 0!==a||this.$_undefinedMap[l]||(a=0),e.push({item:n,id:l,size:a})}return e},listeners(){const e={};for(const t in this.$listeners)"resize"!==t&&"visible"!==t&&(e[t]=this.$listeners[t]);return e}},watch:{items(){this.forceUpdate(!1)},simpleArray:{handler(e){this.vscrollData.simpleArray=e},immediate:!0},direction(e){this.forceUpdate(!0)},itemsWithSize(e,t){const i=this.$el.scrollTop;let s=0,r=0;const n=Math.min(e.length,t.length);for(let o=0;o<n&&!(s>=i);o++)s+=t[o].size||this.minItemSize,r+=e[o].size||this.minItemSize;const o=r-s;0!==o&&(this.$el.scrollTop+=o)}},beforeCreate(){this.$_updates=[],this.$_undefinedSizes=0,this.$_undefinedMap={}},activated(){this.vscrollData.active=!0},deactivated(){this.vscrollData.active=!1},methods:{onScrollerResize(){this.$refs.scroller&&this.forceUpdate(),this.$emit("resize")},onScrollerVisible(){this.$emit("vscroll:update",{force:!1}),this.$emit("visible")},forceUpdate(e=!0){(e||this.simpleArray)&&(this.vscrollData.validSizes={}),this.$emit("vscroll:update",{force:!0})},scrollToItem(e){const t=this.$refs.scroller;t&&t.scrollToItem(e)},getItemSize(e,t){const i=this.simpleArray?null!=t?t:this.items.indexOf(e):e[this.keyField];return this.vscrollData.sizes[i]||0},scrollToBottom(){if(this.$_scrollingToBottom)return;this.$_scrollingToBottom=!0;const e=this.$el;this.$nextTick(()=>{e.scrollTop=e.scrollHeight+5e3;const t=()=>{e.scrollTop=e.scrollHeight+5e3,requestAnimationFrame(()=>{e.scrollTop=e.scrollHeight+5e3,0===this.$_undefinedSizes?this.$_scrollingToBottom=!1:requestAnimationFrame(t)})};requestAnimationFrame(t)})}}};var D=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("RecycleScroller",e._g(e._b({ref:"scroller",attrs:{items:e.itemsWithSize,"min-item-size":e.minItemSize,direction:e.direction,"key-field":"id","list-tag":e.listTag,"item-tag":e.itemTag},on:{resize:e.onScrollerResize,visible:e.onScrollerVisible},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.item,s=t.index,r=t.active;return[e._t("default",null,null,{item:i.item,index:s,active:r,itemWithSize:i})]}}],null,!0)},"RecycleScroller",e.$attrs,!1),e.listeners),[e._v(" "),i("template",{slot:"before"},[e._t("before")],2),e._v(" "),i("template",{slot:"after"},[e._t("after")],2),e._v(" "),i("template",{slot:"empty"},[e._t("empty")],2)],2)};D._withStripped=!0;const k=x({render:D,staticRenderFns:[]},void 0,R,void 0,!1,void 0,!1,void 0,void 0,void 0);const A=x({},void 0,{name:"DynamicScrollerItem",inject:["vscrollData","vscrollParent","vscrollResizeObserver"],props:{item:{required:!0},watchData:{type:Boolean,default:!1},active:{type:Boolean,required:!0},index:{type:Number,default:void 0},sizeDependencies:{type:[Array,Object],default:null},emitResize:{type:Boolean,default:!1},tag:{type:String,default:"div"}},computed:{id(){if(this.vscrollData.simpleArray)return this.index;if(this.item.hasOwnProperty(this.vscrollData.keyField))return this.item[this.vscrollData.keyField];throw new Error(`keyField '${this.vscrollData.keyField}' not found in your item. You should set a valid keyField prop on your Scroller`)},size(){return this.vscrollData.validSizes[this.id]&&this.vscrollData.sizes[this.id]||0},finalActive(){return this.active&&this.vscrollData.active}},watch:{watchData:"updateWatchData",id(){this.size||this.onDataUpdate()},finalActive(e){this.size||(e?this.vscrollParent.$_undefinedMap[this.id]||(this.vscrollParent.$_undefinedSizes++,this.vscrollParent.$_undefinedMap[this.id]=!0):this.vscrollParent.$_undefinedMap[this.id]&&(this.vscrollParent.$_undefinedSizes--,this.vscrollParent.$_undefinedMap[this.id]=!1)),this.vscrollResizeObserver?e?this.observeSize():this.unobserveSize():e&&this.$_pendingVScrollUpdate===this.id&&this.updateSize()}},created(){if(!this.$isServer&&(this.$_forceNextVScrollUpdate=null,this.updateWatchData(),!this.vscrollResizeObserver)){for(const e in this.sizeDependencies)this.$watch(()=>this.sizeDependencies[e],this.onDataUpdate);this.vscrollParent.$on("vscroll:update",this.onVscrollUpdate),this.vscrollParent.$on("vscroll:update-size",this.onVscrollUpdateSize)}},mounted(){this.vscrollData.active&&(this.updateSize(),this.observeSize())},beforeDestroy(){this.vscrollParent.$off("vscroll:update",this.onVscrollUpdate),this.vscrollParent.$off("vscroll:update-size",this.onVscrollUpdateSize),this.unobserveSize()},methods:{updateSize(){this.finalActive?this.$_pendingSizeUpdate!==this.id&&(this.$_pendingSizeUpdate=this.id,this.$_forceNextVScrollUpdate=null,this.$_pendingVScrollUpdate=null,this.computeSize(this.id)):this.$_forceNextVScrollUpdate=this.id},updateWatchData(){this.watchData&&!this.vscrollResizeObserver?this.$_watchData=this.$watch("item",()=>{this.onDataUpdate()},{deep:!0}):this.$_watchData&&(this.$_watchData(),this.$_watchData=null)},onVscrollUpdate({force:e}){!this.finalActive&&e&&(this.$_pendingVScrollUpdate=this.id),this.$_forceNextVScrollUpdate!==this.id&&!e&&this.size||this.updateSize()},onDataUpdate(){this.updateSize()},computeSize(e){this.$nextTick(()=>{if(this.id===e){const e=this.$el.offsetWidth,t=this.$el.offsetHeight;this.applySize(e,t)}this.$_pendingSizeUpdate=null})},applySize(e,t){const i=~~("vertical"===this.vscrollParent.direction?t:e);i&&this.size!==i&&(this.vscrollParent.$_undefinedMap[this.id]&&(this.vscrollParent.$_undefinedSizes--,this.vscrollParent.$_undefinedMap[this.id]=void 0),this.$set(this.vscrollData.sizes,this.id,i),this.$set(this.vscrollData.validSizes,this.id,!0),this.emitResize&&this.$emit("resize",this.id))},observeSize(){this.vscrollResizeObserver&&this.$el.parentNode&&(this.vscrollResizeObserver.observe(this.$el.parentNode),this.$el.parentNode.addEventListener("resize",this.onResize))},unobserveSize(){this.vscrollResizeObserver&&(this.vscrollResizeObserver.unobserve(this.$el.parentNode),this.$el.parentNode.removeEventListener("resize",this.onResize))},onResize(e){const{width:t,height:i}=e.detail.contentRect;this.applySize(t,i)}},render(e){return e(this.tag,this.$slots.default)}},void 0,void 0,void 0,!1,void 0,void 0,void 0);const P={version:"1.1.2",install(e,t){const i=Object.assign({},{installComponents:!0,componentsPrefix:""},t);for(const e in i)void 0!==i[e]&&(r[e]=i[e]);i.installComponents&&function(e,t){e.component(t+"recycle-scroller",V),e.component(t+"RecycleScroller",V),e.component(t+"dynamic-scroller",k),e.component(t+"DynamicScroller",k),e.component(t+"dynamic-scroller-item",A),e.component(t+"DynamicScrollerItem",A)}(e,i.componentsPrefix)}};let M=null;return"undefined"!=typeof window?M=window.Vue:"undefined"!=typeof global&&(M=global.Vue),M&&M.use(P),e.DynamicScroller=k,e.DynamicScrollerItem=A,e.IdState=function({idProp:e=(e=>e.item.id)}={}){const t={},i=new s.default({data:()=>({store:t})});return{data:()=>({idState:null}),created(){this.$_id=null,this.$_getId="function"==typeof e?()=>e.call(this,this):()=>this[e],this.$watch(this.$_getId,{handler(e){this.$nextTick(()=>{this.$_id=e})},immediate:!0}),this.$_updateIdState()},beforeUpdate(){this.$_updateIdState()},methods:{$_idStateInit(e){const s=this.$options.idState;if("function"==typeof s){const r=s.call(this,this);return i.$set(t,e,r),this.$_id=e,r}throw new Error("[mixin IdState] Missing `idState` function on component definition.")},$_updateIdState(){const i=this.$_getId();null==i&&console.warn(`No id found for IdState with idProp: '${e}'.`),i!==this.$_id&&(t[i]||this.$_idStateInit(i),this.idState=t[i])}}}},e.RecycleScroller=V,e.default=P,Object.defineProperty(e,"__esModule",{value:!0}),e}({},Vue);
//# sourceMappingURL=vue-virtual-scroller.min.js.map
