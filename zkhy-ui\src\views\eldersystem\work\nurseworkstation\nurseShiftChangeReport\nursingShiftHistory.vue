
<template>
    <div class="app-container">  
      <!-- 表格 -->
      <el-row :gutter="24">
         <el-col :span="4">
          <div class="treeStyle">
            <div class="panel-header">
              <span class="title">楼层信息</span>
           </div>
            <el-tree
                :data="buildingData"
                :props="treeProps"
                node-key="id"
                highlight-current
                default-expand-all
                :expand-on-click-node="false"
                :current-node-key="currentBedId"
                @node-click="handleNodeClick"
                >
                <template #default="{ node, data }">
                    <template v-if="data.type === 'building'">
                    <el-icon style="color: #409EFF; margin-right: 4px"><OfficeBuilding /></el-icon>
                    <span>{{ data.label }}</span>
                    </template>
                    <template v-else-if="data.type === 'floor'">
                    <el-icon style="color: #67C23A; margin-right: 4px"><List /></el-icon>
                    <span>{{ data.label }}</span>
                    </template>
                    <!-- <template v-else-if="data.type === 'room'">
                    <el-icon style="color: #909399; margin-right: 4px"><MonitorIcon /></el-icon>
                    <span>{{ data.label }}</span>
                    </template> -->
                    <template v-else-if="data.type === 'bed'">
                    <el-icon style="color: #1890ff; margin-right: 4px"><ConnectionIcon /></el-icon>
                    <span>{{ data.label }}</span>
                    </template>
                </template>
                </el-tree>
          </div>
         </el-col>
          <el-col :span="20">
            <div class="rightTable">
                <!-- 搜索栏 -->
            <el-form :inline="true" :model="queryParams" class="search-form" label-width="100px" ref="queryForm">
              <el-form-item label="交接班日期" prop="handoverDate">
                <el-date-picker v-model="queryParams.handoverDate" type="date" placeholder="选择日期"  value-format="YYYY-MM-DD" style="width: 160px" clearable></el-date-picker>
              </el-form-item>
              <el-form-item label="白班交接人" prop="dayNurse">
                <el-input v-model="queryParams.dayNurse" placeholder="请输入交接人" style="width: 160px" clearable></el-input>
              </el-form-item>
              <el-form-item label="夜班交接人" prop="nightNurse">
                <el-input v-model="queryParams.nightNurse" placeholder="请输入交接人" style="width: 160px" clearable></el-input>
              </el-form-item>
              <el-form-item label="楼栋信息" prop="buildingName">
                <el-select v-model="queryParams.buildingName" style="width: 160px" placeholder="全部" clearable @change="handleBuildingChange">
                  <el-option v-for="item in buildingList" :key="item.value" :label="item.buildingName" :value="item.buildingName" />
                </el-select>
              </el-form-item>
              <el-form-item label="楼栋层数" prop="floorNumber">
                <el-select v-model="queryParams.floorNumber" style="width: 160px" placeholder="全部" clearable :disabled="!queryParams.buildingName">
                  <el-option v-for="item in floorList" :key="item.value" :label="item.floorName" :value="item.floorName" />
                </el-select>
              </el-form-item>
              <el-form-item label="状 态" prop="status">
                <el-select v-model="queryParams.status" style="width: 160px" placeholder="全部" clearable>
                  <el-option v-for='dict in handover_status' :key='dict.value' :label='dict.label' :value='dict.value'></el-option>
                </el-select>
              </el-form-item>
              <div class="btn-group">
                  <el-button type="primary" icon="Search" @click="handleQuery" style="margin-left: 60px;">查询</el-button>
                  <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </div>
            </el-form>
            <el-table :data="tableData" style="width: 100%"  border stripe v-loading="loading">
                <el-table-column prop="index" label="序号" width="60" align="center">
                  <template #default="scope">
                    <span>{{ scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="handoverDate" label="交接日期" width="100" align="center"></el-table-column>
                <el-table-column prop="floorNumber" label="楼栋层数" width="80" align="center"></el-table-column>
                <el-table-column prop="buildingName" label="楼栋信息" width="120" align="center"></el-table-column>
                <el-table-column prop="dayNurse" label="白班护士" width="100" align="center"></el-table-column>
                <el-table-column prop="dayHandoverTime" label="白班班次" min-width="180" align="center"></el-table-column>
                <el-table-column prop="nightNurse" label="夜班护士" width="100" align="center"></el-table-column>
                <el-table-column prop="nightHandoverTime" label="夜班班次" min-width="180" align="center"></el-table-column>
                <el-table-column prop="status" label="状态" min-width="180" align="center">
                <template #default="scope">
                    <span v-if="scope.row.status === 'complete'">
                    <i class="el-icon-check" style="color: green;"></i> 已完成
                    </span>
                    <span v-else>
                    <i class="el-icon-loading" style="color: orange;"></i> 未完成
                    </span>
                </template>
                </el-table-column>
                <el-table-column label="操作" width="180" fixed="right" align="center">
                <template #default="scope">
                    <el-button type="primary"  @click="handleView(scope.row)" link>详情</el-button>
                    <el-button type="primary" @click="handleDelete(scope.row)" link>删除</el-button>
                </template>
                </el-table-column>
            </el-table>   
          <!-- 分页 -->
          <div class="pagination-container">
          <el-pagination
                layout="prev, pager, next, sizes, jumper"
                :total="total"
                background
                :page-size="queryParams.pageSize"
                :current-page="queryParams.pageNum"
                @size-change="handleSizeChange"
                @current-change="handlePageChange"
                :page-sizes="[10, 20, 50]"
              />
          </div>
            </div>
          </el-col>
      </el-row>
      
    <!-- <el-dialog title="交接详情" v-model="dialogVisible" width="70%">
       <DetailNurse ref="detailNurseRef"/>
    </el-dialog> -->
    </div>
  </template>
  
  <script setup>
  import { ref, reactive } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
//   import DetailNurse from './detailNurse.vue';
  import { Connection as ConnectionIcon, Monitor as MonitorIcon } from '@element-plus/icons-vue'
  import { getRoomTreeList} from '@/api/live/buildmanage'
  import {getNurseHandoverList} from '@/api/nurse/index'
  import { getBuildingList, getFloorList, getRoomCardList } from '@/api/live/roommanage'
  const {
      proxy
  } = getCurrentInstance()
  const loading = ref(false)
  const {  handover_status } = proxy.useDict(
    "handover_status",
  );
  const router = useRouter()
  const dialogVisible = ref(false);
  const title = ref("");
  const open = ref(false)
  const tableData = ref([]);
  const buildingList = ref([])//楼栋下拉列表
  const floorList = ref([])//楼层下拉列表
  const treeProps = { children: 'children', label: 'label' }
  const currentBedId = ref('')
  const total = ref(0);  
  const buildingData = ref([])
  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      status:undefined,
      handoverDate:undefined,
      dayNurse:undefined,
      nightNurse:undefined,
      buildingName:undefined,
      floorNumber:undefined
    },
    // 查询参数
  
    // 表单参数
    form: {},
    // 表单校验
    rules: {},
  });
  
  const { queryParams, form, rules } = toRefs(data);
  function handleNodeClick(data) {
    if(data.type == "building"){
      queryParams.value.buildingName = data.label
      queryParams.value.buildingId = data.id
      handleBuildingChange(data.label)
    }else if(data.type == "floor"){
      queryParams.value.floorNumber = data.floorNumber
      queryParams.value.floorId = data.id
    }
    handleQuery()
  }
  const handlePageChange = (val) => {
    // 分页逻辑
    queryParams.value.pageNum = val
    getList()
  };
  const handleAdd = () => {
    router.push({
      path: '/nursecheckin/nurseCheckinAdd/add/0/add'
    })
  }
  const handleView = (row) => {  
    // dialogVisible.value = true
    // nextTick(() => {    
    //   proxy.$refs.detailNurseRef.sendParams(row);
    // })
    router.push({
      path: '/nurseShiftChangeReport/nurseShiftDetail/add/0/view',
    })
  };
  const handleDelete = (row) => { 
    ElMessageBox.confirm('确定删除该交接班吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      deleteNurseCheckin(row.id).then(res => {
        if (res.code === 200) {
          ElMessage({
            type: 'success',
            message: '删除成功!'
          });
          handleQuery()
        } else {
          ElMessage({
            type: 'error',
            message: res.msg
          });
        }
      })
    })
  };
  const fetchRoomTreeData = async () => {
      try {
        const res = await getRoomTreeList()
        if (res.code === 200) {
          buildingData.value = res.data.map(building => ({
            ...building,
            children: building.children?.map(floor => ({
              ...floor,
              children: [] // 清空楼层下的房间数据
            }))
          }))
        }
      } catch (error) {
        console.error('获取房间树形数据失败:', error)
        ElMessage.error('获取楼栋信息失败')
      }
  }
  const getList = () => {
    loading.value = true;
    getNurseHandoverList({...queryParams.value}).then((response) => {
      tableData.value = response.rows;
      total.value = response.total;
    }).finally(() => {
      loading.value = false;
    })
    ;
  };
  function handleQuery() {
    tableData.value = [];
    queryParams.pageNum = 1;
    getList();
  }
  const handleBuildingChange =async (val) => {
    floorList.value = [];
    queryParams.value.floorNumber = '';
    const filterInfo = buildingList.value.filter(item=>item.buildingName == val)
    const res = await getFloorList(filterInfo[0].id);
    floorList.value = res.rows;
  }
  //重置
  function resetQuery() {
    proxy.$refs.queryForm.resetFields()
    queryParams.value = {
      pageNum: 1,
      pageSize: 10,
      status:undefined,
      handoverDate:undefined,
      dayNurse:undefined,
      nightNurse:undefined,
      buildingName:undefined,
      buildingId:undefined,
      floorNumber:undefined,
      floorId:undefined,
    }
    handleQuery();
  }
  // 分页处理
  const handleSizeChange = (val) => {
      queryParams.value.pageSize = val
      getList()
    }
  function initList(){
    fetchRoomTreeData()  
    getBuildingListData();
    getList();
  }
  const getBuildingListData = async () => {    
      const res = await getBuildingList()
      buildingList.value = res.rows || []
  }
  onMounted(()=>{
    initList();
  })
  </script>
  
  <style scoped>
  .search-form {
    margin-bottom: 20px;
  }
  
  .pagination-container {
    margin: 20px 0 0 0;
    text-align: right;
  }
  .treeStyle{
    border: 1px solid #e5e6eb;
    min-height:calc(100vh - 40px);
  }
  .rightTable{
    min-height:calc(100vh - 40px);
  }
  .btn-group{
    display: flex;
    justify-content: flex-end;
  }
  .panel-header {
    padding: 16px;
    border-bottom: 1px solid #e5e6eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
  
    .title {
      font-size: 16px;
      font-weight: 500;
      color: #1d2129;
    }
  }
  </style>