<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="项目名称" prop="itemName">
        <el-input
          v-model="queryParams.itemName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建人" prop="createByName">
        <el-input
          v-model="queryParams.createByName"
          placeholder="请输入创建人"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row justify="end" style="margin-bottom: 5px">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['roomnurserec:consumableChargeItem:add']"
          >新增</el-button
        >
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="consumableChargeItemList"
      border
      stripe
      @selection-change="handleSelectionChange"
    >
      <el-table-column label="序号" type="index" width="60" align="center" />
      <el-table-column label="易耗品项目名称" align="center" prop="itemName" />
      <el-table-column label="收费标准" align="center" prop="price" />
      <el-table-column
        label="状态(1:正常/0:停用)"
        align="center"
        prop="status"
        v-if="false"
      >
        <template #default="scope">
          <dict-tag :options="sys_notice_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="项目类别" align="center" prop="category" v-if="false" />
      <el-table-column label="计价单位" align="center" prop="unit" v-if="false" />
      <el-table-column label="项目描述" align="center" prop="description" v-if="false" />
      <el-table-column label="创建人" align="center" prop="createByName" />
      <el-table-column label="创建时间" align="center" prop="createTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改易耗品收费项目对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form
        ref="consumableChargeItemRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="易耗品项目名称" prop="itemName">
          <el-input v-model="form.itemName" placeholder="请输入易耗品项目名称" />
        </el-form-item>
        <el-form-item label="易耗品价格" prop="price">
          <el-input v-model="form.price" placeholder="请输入易耗品价格" min="0">
            <template #prepend>￥：</template>
          </el-input>
        </el-form-item>
        <el-form-item label="项目类别" prop="category" v-if="false">
          <el-input v-model="form.category" placeholder="请输入项目类别" />
        </el-form-item>
        <el-form-item label="计价单位" prop="unit" v-if="false">
          <el-input v-model="form.unit" placeholder="请输入计价单位" />
        </el-form-item>
        <el-form-item label="项目描述" prop="description" v-if="false">
          <el-input v-model="form.description" placeholder="请输入项目描述" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <div style="margin-left: 20px; color: #999; font-size: 14px">
            记录人：{{ currentUser }}
          </div>
          <div>
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ConsumableChargeItem">
import {
  listConsumableChargeItem,
  getConsumableChargeItem,
  delConsumableChargeItem,
  addConsumableChargeItem,
  updateConsumableChargeItem,
} from "@/api/work/tConsumableChargeItem";
import { getUserProfile } from "@/api/system/user";
const { proxy } = getCurrentInstance();
const currentUser = ref(JSON.parse(localStorage.getItem("userInfo")).nickName);

const consumableChargeItemList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    itemName: null,
    price: null,
    status: 1,
    category: null,
    unit: null,
    description: null,
  },
  rules: {
    itemName: [{ required: true, message: "项目名称不能为空", trigger: "blur" }],
    price: [{ required: true, message: "价格不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询易耗品收费项目列表 */
function getList() {
  loading.value = true;
  listConsumableChargeItem(queryParams.value).then((response) => {
    consumableChargeItemList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    itemName: null,
    price: null,
    status: null,
    category: null,
    unit: null,
    description: null,
    remark: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
  };
  proxy.resetForm("consumableChargeItemRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加易耗品收费项目";
  // currentUser.value = localStorage.getItem("userInfo").nickName();
  // console.log("currentUser", currentUser.value);
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getConsumableChargeItem(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改易耗品收费项目";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["consumableChargeItemRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateConsumableChargeItem(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addConsumableChargeItem(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除易耗品收费项目编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delConsumableChargeItem(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "roomnurserec/consumableChargeItem/export",
    {
      ...queryParams.value,
    },
    `consumableChargeItem_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
<style lang="css">
.dialog-footer {
  width: 100%;
  display: flex;
  justify-content: space-between;
  gap: 20px;
}
</style>
