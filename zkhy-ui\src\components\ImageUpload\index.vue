<template>
  <div class="component-upload-image">
    <el-upload
      ref="imageUpload"
      :accept="acceptFormats"
      :action="uploadImgUrl"
      :before-remove="handleDelete"
      :before-upload="handleBeforeUpload"
      :class="{ hide: fileList.length >= limit }"
      :data="fileData"
      :disabled="disabled"
      :file-list="fileList"
      :headers="headers"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-preview="handlePictureCardPreview"
      :on-success="handleUploadSuccess"
      :show-file-list="true"
      list-type="picture-card"
      multiple
    >
      <el-icon :disabled="disabled" class="avatar-uploader-icon">
        <plus />
      </el-icon>
      <el-button
        v-if="iconButton"
        :disabled="disabled"
        style="
          background-color: rgb(225, 225, 225);
          border-color: rgb(225, 225, 225);
          color: rgb(64, 158, 225);
        "
        type="info"
        >上传
      </el-button>
    </el-upload>
    <!-- 上传提示 -->
    <div v-if="showTip" class="el-upload__tip">
      请上传
      <template v-if="fileSize">
        大小不超过
        <b style="color: #f56c6c">{{ fileSize }}MB</b>
      </template>
      <template v-if="fileType">
        格式为
        <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
      </template>
      的文件
    </div>
    <el-dialog v-model="dialogVisible" append-to-body title="预览" width="800px">
      <img
        :src="dialogImageUrl"
        style="display: block; max-width: 100%; margin: 0 auto"
      />
    </el-dialog>
  </div>
</template>
<script setup>
import { getToken } from "@/utils/auth";
import { isExternal } from "@/utils/validate";
import { watch } from "vue";

const iconPlus = ref(false);
const iconuUploadFilled = ref(false);
const iconButton = ref(false);

const props = defineProps({
  modelValue: [String, Object, Array],
  // 图片数量限制
  limit: {
    type: Number,
    default: 5,
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 5,
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ["png", "jpg", "jpeg"],
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true,
  },
  fileData: [String, Object, Array],
  disabled: {
    type: Boolean,
    default: false,
  },
});

const { proxy } = getCurrentInstance();
const emit = defineEmits(["submitParentValue", "removeAtt"]);
const number = ref(0);
const uploadList = ref([]);
const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const baseUrl = import.meta.env.VITE_APP_BASE_API;
// const uploadImgUrl = ref(import.meta.env.VITE_APP_BASE_API + "/common/upload"); // 上传的图片服务器地址
const uploadImgUrl = ref(
  import.meta.env.VITE_APP_BASE_API + "/eldersystem/fileinfo/upload"
); // 上传的图片服务器地址
const headers = ref({ Authorization: "Bearer " + getToken() });
const fileList = ref([]);
const showTip = computed(() => props.isShowTip && (props.fileType || props.fileSize));

watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      // console.log(val);
      // 首先将值转为数组
      const list = Array.isArray(val) ? val : props.modelValue.split(",");
      // 然后将数组转为对象数组
      fileList.value = list.map((item) => {
        if (typeof item === "string") {
          if (item.indexOf(baseUrl) === -1 && !isExternal(item)) {
            item = {
              name: baseUrl + item,
              url: baseUrl + item,
            };
          } else {
            item = {
              name: item,
              url: item,
            };
          }
        }
        return item;
      });
    } else {
      fileList.value = [];
      return [];
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
const acceptFormats = props.fileType.map((format) => `.${format}`).join(",");

// 上传前loading加载
function handleBeforeUpload(file) {
  let isImg = false;
  if (props.fileType.length) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    isImg = props.fileType.some((type) => {
      if (file.type.indexOf(type) > -1) return true;
      if (fileExtension && fileExtension.indexOf(type) > -1) return true;
      return false;
    });
  } else {
    isImg = file.type.indexOf("image") > -1;
  }
  if (!isImg) {
    proxy.$modal.msgError(
      `文件格式不正确，请上传${props.fileType.join("/")}图片格式文件!`
    );
    return false;
  }
  if (file.name.includes(",")) {
    proxy.$modal.msgError("文件名不正确，不能包含英文逗号!");
    return false;
  }
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      proxy.$modal.msgError(`上传头像图片大小不能超过 ${props.fileSize} MB!`);
      return false;
    }
  }
  proxy.$modal.loading("正在上传图片，请稍候...");
  number.value++;
}

// 文件个数超出
function handleExceed() {
  proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`);
}

// 上传成功回调
function handleUploadSuccess(res, file) {
  if (res.code === 200) {
    console.log(res, "res...");
    uploadList.value.push({
      id: res.data.id,
      ossId: res.data.ossId,
      name: res.data.fileName,
      url: res.data.filePath,
      type: res.data.attachmentType,
    });
    console.log(JSON.stringify(uploadList.value), "uploadList.value111");

    uploadedSuccessfully();
  } else {
    number.value--;
    proxy.$modal.closeLoading();
    proxy.$modal.msgError(res.msg);
    proxy.$refs.imageUpload.handleRemove(file);
    uploadedSuccessfully();
  }
}

// 删除图片
function handleDelete(file) {
  const findex = fileList.value.map((f) => f.name).indexOf(file.name);
  console.log(findex, "findex");
  console.log(fileList.value, "fileList.value");
  console.log(uploadList.value, "uploadList.value");
  // if (findex > -1 && uploadList.value.length === number.value) {
  if (findex > -1 && fileList.value[findex].uid) {
    console.log(JSON.stringify(fileList.value[findex]));
    let attId = fileList.value[findex].id;
    let type = fileList.value[findex].type;
    // fileList.value.splice(findex, 1);
    console.log(JSON.stringify(fileList.value));
    // emit("update:modelValue", listToString(fileList.value));
    emit("removeAtt", attId, type);
  }
}

// 上传结束处理
function uploadedSuccessfully() {
  // console.log(number.value, " number in uploadedSuccessfully... ");
  if (number.value > 0 && uploadList.value.length === number.value) {
    console.log(number.value, " number in uploadedSuccessfully... upload all... ");
    fileList.value = fileList.value
      .filter((f) => f.url !== undefined)
      .concat(uploadList.value);
    console.log(fileList.value, "fileList.value");
    console.log(uploadList.value, "uploadList.value");
    emit("submitParentValue", uploadList.value);

    console.log("listToString(fileList.value)", listToString(fileList.value));
    // emit("update:modelValue", listToString(fileList.value));
    emit("update:modelValue", fileList.value);

    uploadList.value = [];
    number.value = 0;
    proxy.$modal.closeLoading();
  }
}

// 上传失败
function handleUploadError() {
  proxy.$modal.msgError("上传图片失败");
  proxy.$modal.closeLoading();
}

// 预览
function handlePictureCardPreview(file) {
  dialogImageUrl.value = file.url;
  dialogVisible.value = true;
}

// 对象转成指定字符串分隔
function listToString(list, separator) {
  let strs = "";
  separator = separator || ",";
  for (let i in list) {
    if (undefined !== list[i].url && list[i].url.indexOf("blob:") !== 0) {
      strs += list[i].url.replace(baseUrl, "") + separator;
    }
  }
  return strs != "" ? strs.substr(0, strs.length - 1) : "";
}
</script>
<style lang="scss" scoped>
// .el-upload--picture-card 控制加号部分
:deep(.hide .el-upload--picture-card) {
  display: none;
}
</style>
