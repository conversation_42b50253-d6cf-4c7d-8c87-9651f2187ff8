{"name": "scrollparent", "version": "2.1.0", "description": "A function to get the scrolling parent of an html element.", "main": "scrollparent.js", "repository": {"type": "git", "url": "git+https://github.com/olahol/scrollparent.js.git"}, "keywords": ["browser", "scroll", "scrollparent", "dom"], "author": "<PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/olahol/scrollparent.js/issues"}, "homepage": "https://github.com/olahol/scrollparent.js#readme", "devDependencies": {"@playwright/test": "^1.27.1", "es5-validator": "^1.3.1"}, "scripts": {"test": "playwright test", "lint": "es5-validator scrollparent.js"}, "files": []}