<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24">
        <div class="stepList">
          <div
            :class="stepActive == 1 ? 'activeBackCss' : 'NoactiveBackCss'"
            @click="nextHandle(1)"
          >
            <span :class="stepActive == 1 ? 'steptitleSelect' : 'steptitleNoSelect'"
              >1.老人基本信息</span
            >
          </div>
          <div
            :class="stepActive == 2 ? 'activeBackCss' : 'NoactiveBackCss'"
            @click="nextHandle(2)"
          >
            <span :class="stepActive == 2 ? 'steptitleSelect' : 'steptitleNoSelect'"
              >2.评估及照护信息</span
            >
          </div>
          <div
            :class="stepActive == 3 ? 'activeBackCss' : 'NoactiveBackCss'"
            @click="nextHandle(3)"
          >
            <span :class="stepActive == 3 ? 'steptitleSelect' : 'steptitleNoSelect'"
              >3.合同及费用信息</span
            >
          </div>
          <div
            :class="stepActive == 4 ? 'activeBackCss' : 'NoactiveBackCss'"
            @click="nextHandle(4)"
          >
            <span :class="stepActive == 4 ? 'steptitleSelect' : 'steptitleNoSelect'"
              >4.风险告知及免责声明</span
            >
          </div>
        </div>
      </el-col>
      <el-col :span="24">
        <div style="margin-top: 10px"></div>
      </el-col>
    </el-row>
    <el-form ref="checkInRef" :model="form" :rules="rules" label-width="120px">
      <div class="formAll">
        <div class="formCss">
          <el-card v-if="stepActive == 1" shadow="hover">
            <div class="baseTitle">经办人信息</div>
            <el-row>
              <el-col :span="8">
                <el-form-item label="经办人" prop="handlerName" size="large">
                  <el-input
                    v-model="form.feeContract.handlerName"
                    :disabled="isShowOrEdit"
                    placeholder="请输入经办人"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="机构名称" prop="orgName" size="large">
                  <el-input
                    v-model="form.feeContract.orgName"
                    :disabled="isShowOrEdit"
                    placeholder="请输入机构名称"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <div class="baseTitle">基本信息</div>
            <el-row :gutter="15">
              <el-col :span="8">
                <el-form-item label="老人姓名" prop="elderName" size="large">
                  <el-input
                    v-model="form.elderInfo.elderName"
                    :disabled="isShowOrEdit"
                    placeholder="请输入老人姓名"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="老人编号" prop="elderCode" size="large">
                  <el-input
                    v-model="form.elderInfo.elderCode"
                    :disabled="isShowOrEdit || noEdit"
                    placeholder="请输入老人编号"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="入住时间" prop="checkInDate" size="large">
                  <el-date-picker
                    v-model="form.checkIn.checkInDate"
                    :disabled="isShowOrEdit"
                    clearable
                    placeholder="请选择入驻时间"
                    style="width: 100%"
                    type="date"
                    value-format="YYYY-MM-DD"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="身份证号" prop="idCard" size="large">
                  <el-input
                    v-model="form.elderInfo.idCard"
                    :disabled="isShowOrEdit"
                    placeholder="请输入身份证号"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="老人性别" prop="gender" size="large">
                  <el-select
                    v-model="form.elderInfo.gender"
                    :disabled="isShowOrEdit"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="dict in sys_user_sex"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="老人年龄" prop="age" size="large">
                  <el-input
                    v-model="form.elderInfo.age"
                    :disabled="isShowOrEdit"
                    placeholder="请输入老人年龄"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="老人生日" prop="birthDate" size="large">
                  <el-date-picker
                    v-model="form.elderInfo.birthDate"
                    :disabled="isShowOrEdit"
                    clearable
                    placeholder="请选择出生日期"
                    style="width: 100%"
                    type="date"
                    value-format="YYYY-MM-DD"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="老人电话" prop="phone" size="large">
                  <el-input
                    v-model="form.elderInfo.phone"
                    :disabled="isShowOrEdit"
                    placeholder="请输入老人电话"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="老人民族" prop="nation" size="large">
                  <el-input
                    v-model="form.elderInfo.nation"
                    :disabled="isShowOrEdit"
                    placeholder="请输入民族"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="能力等级" prop="abilityLevel" size="large">
                  <el-select
                    v-model="form.checkIn.abilityLevel"
                    :disabled="isShowOrEdit"
                    placeholder="请选择能力等级"
                  >
                    <el-option
                      v-for="dict in capability_level"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="自理能力" prop="selfCareAbility" size="large">
                  <el-select
                    v-model="form.checkIn.selfCareAbility"
                    :disabled="isShowOrEdit"
                    placeholder="请选择自理能力"
                  >
                    <el-option
                      v-for="dict in self_careability"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="照护等级" prop="careLevel" size="large">
                  <el-select
                    v-model="form.checkIn.careLevel"
                    :disabled="isShowOrEdit"
                    placeholder="请选择照护等级"
                  >
                    <el-option
                      v-for="dict in care_level"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="护理等级" prop="nursingLevel" size="large">
                  <el-select
                    v-model="form.checkIn.nursingLevel"
                    :disabled="isShowOrEdit"
                    placeholder="请选择护理等级"
                  >
                    <el-option
                      v-for="dict in nursing_grade"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="政治面貌" prop="politicalStatus" size="large">
                  <el-select
                    v-model="form.elderInfo.politicalStatus"
                    :disabled="isShowOrEdit"
                    placeholder="请选择政治面貌"
                  >
                    <el-option
                      v-for="dict in political_status"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="楼栋信息" prop="floorNumber">
                  <el-select
                    v-model="form.checkIn.buildingId"
                    style="width: 100%"
                    placeholder="全部"
                    clearable
                    :disabled="isShowOrEdit || noEdit"
                    @change="handleBuildingChange"
                  >
                    <el-option
                      v-for="item in buildingList"
                      :key="item.value"
                      :label="item.buildingName"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="楼栋层数" prop="buildingName">
                  <el-select
                    v-model="form.checkIn.floorId"
                    style="width: 100%"
                    placeholder="全部"
                    clearable
                    :disabled="isShowOrEdit || noEdit"
                    @change="handleFloorChange"
                  >
                    <el-option
                      v-for="item in floorList"
                      :key="item.value"
                      :label="item.floorName"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="房&nbsp;&nbsp;间&nbsp;&nbsp;号" prop="roomName">
                  <el-select
                    :disabled="isShowOrEdit || noEdit"
                    v-model="form.checkIn.roomId"
                    style="width: 100%"
                    placeholder="全部"
                    @change="handleRoomChange"
                    clearable
                  >
                    <el-option
                      v-for="item in roomList"
                      :key="item.id"
                      :label="item.roomNumber"
                      :value="item.id"
                    />
                  </el-select> </el-form-item
              ></el-col>

              <el-col :span="8">
                <el-form-item label="房间/床位" prop="bedId" size="large">
                  <el-select
                    v-model="form.checkIn.bedId"
                    :disabled="isShowOrEdit || noEdit"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in bedList"
                      :key="item.id"
                      :label="item.bedNumber"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="居住类型" prop="residenceType" size="large">
                  <el-select
                    v-model="form.checkIn.residenceType"
                    :disabled="isShowOrEdit"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="dict in residential_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="16">
                <el-form-item label="家庭住址" prop="homeAddress" size="large">
                  <el-input
                    v-model="form.elderInfo.homeAddress"
                    :disabled="isShowOrEdit"
                    placeholder="请输入家庭住址"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="20">
                <el-form-item
                  label="监护人信息"
                  prop="elderName1"
                  size="large"
                ></el-form-item>
                <el-table :data="jhrTable" style="width: 100%; margin-left: 10%">
                  <el-table-column v-if="false" label="序号" prop="id" width="80" />
                  <el-table-column
                    align="center"
                    label="与老人关系"
                    prop="relationship"
                    width="180"
                  >
                    <template #default="scope">
                      <dict-tag
                        :options="relationship_elderly"
                        :value="scope.row.relationship"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="姓名" prop="name" width="180" />
                  <el-table-column align="center" label="联系电话" prop="phone" />
                  <el-table-column
                    align="center"
                    label="是否是紧急联系人"
                    prop="isEmergencyContact"
                    width="200"
                  >
                    <template #default="scope">
                      <dict-tag
                        :options="emergency_contact"
                        :value="scope.row.isEmergencyContact"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="住址" prop="address" />
                  <el-table-column
                    align="center"
                    label="操作"
                    prop="careLevel"
                    width="150px"
                  >
                    <template #default="scope">
                      <el-button
                        :disabled="isShowOrEdit"
                        icon="Edit"
                        link
                        type="primary"
                        @click="jhrhandleUpdate(scope.row)"
                        >修改</el-button
                      >
                      <el-button
                        :disabled="isShowOrEdit"
                        icon="Delete"
                        link
                        type="primary"
                        @click="jhrhandleDelete(scope.row)"
                        >删除</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
              <el-col :span="4">
                <el-button :disabled="isShowOrEdit" type="primary" @click="addJHR"
                  >新增监护人</el-button
                >
              </el-col>
            </el-row>
            <div class="baseTitle">其他信息</div>
            <el-row style="margin-top: 20px">
              <el-col :span="8">
                <el-form-item label="工作单位" prop="workUnit" size="large">
                  <el-input
                    v-model="form.elderInfo.workUnit"
                    :disabled="isShowOrEdit"
                    placeholder="请输入工作单位"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="老人职业" prop="formerOccupation" size="large">
                  <el-input
                    v-model="form.elderInfo.formerOccupation"
                    :disabled="isShowOrEdit"
                    placeholder="请输入老人职业"
                  />
                  <!--                                    <el-select v-model='form.elderInfo.formerOccupation' :disabled='isShowOrEdit' placeholder='请选择老人职业'>-->
                  <!--                                        <el-option v-for='dict in occupation_type' :key='dict.value' :label='dict.label' :value='dict.value'></el-option>-->
                  <!--                                    </el-select>-->
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="籍贯" prop="hometown" size="large">
                  <el-input
                    v-model="form.elderInfo.hometown"
                    :disabled="isShowOrEdit"
                    placeholder="请输入籍贯"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="教育程度" prop="education" size="large">
                  <el-select
                    v-model="form.elderInfo.education"
                    :disabled="isShowOrEdit"
                    placeholder="请选择教育程度"
                  >
                    <el-option
                      v-for="dict in educational_level"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="婚姻状况" prop="maritalStatus" size="large">
                  <el-select
                    v-model="form.elderInfo.maritalStatus"
                    :disabled="isShowOrEdit"
                    placeholder="请选择婚姻状况"
                  >
                    <el-option
                      v-for="dict in marital_status"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="老人血型" prop="bloodType" size="large">
                  <el-select
                    v-model="form.elderInfo.bloodType"
                    :disabled="isShowOrEdit"
                    placeholder="请选择老人血型"
                  >
                    <el-option
                      v-for="dict in elderly_blood_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="社保号码" prop="socialSecurityCode" size="large">
                  <el-input
                    v-model="form.elderInfo.socialSecurityCode"
                    :disabled="isShowOrEdit"
                    placeholder="请输入社保号码"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="经济来源" prop="economicSource" size="large">
                  <el-input
                    v-model="form.elderInfo.economicSource"
                    :disabled="isShowOrEdit"
                    placeholder="请输入经济来源"
                  />
                  <!--                                    <el-select v-model='form.elderInfo.economicSource' :disabled='isShowOrEdit' placeholder='请选择经济来源'>-->
                  <!--                                        <el-option v-for='dict in financial_type' :key='dict.value' :label='dict.label' :value='dict.value'></el-option>-->
                  <!--                                    </el-select>-->
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  v-if="false"
                  label="工作单位"
                  prop="workUnit"
                  size="large"
                ></el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="老人标签" prop="elderTags">
                  <el-tag
                    v-for="tag in dynamicTags"
                    :key="tag"
                    :disable-transitions="false"
                    :disabled="isShowOrEdit"
                    closable
                    size="large"
                    style="margin-right: 4px"
                    @close="handleClose(tag)"
                    >{{ tag }}</el-tag
                  >
                  <el-input
                    v-if="inputVisible"
                    ref="InputRef"
                    v-model="inputValue"
                    class="w-20"
                    size="default"
                    style="width: 120px"
                    @blur="handleInputConfirm"
                    @keyup.enter="handleInputConfirm"
                  />
                  <el-button
                    v-else
                    :disabled="isShowOrEdit"
                    class="button-new-tag"
                    size="default"
                    @click="inputClick"
                    >+ 新增标签</el-button
                  >
                </el-form-item>
              </el-col>
              <el-col :span="20">
                <el-form-item label="证件照片" prop="idCardFrontPhoto" size="large">
                  <ImageUpload
                    v-model="form.elderInfo.id_card_front_photo"
                    :disabled="isShowOrEdit"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'id_card_front_photo',
                    }"
                    :fileType="['png', 'jpg', 'jpeg']"
                    :isShowTip="true"
                    :limit="1"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                  <ImageUpload
                    v-model="form.elderInfo.id_card_back_photo"
                    :disabled="isShowOrEdit"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'id_card_back_photo',
                    }"
                    :fileType="['png', 'jpg', 'jpeg']"
                    :isShowTip="true"
                    :limit="1"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
              <el-col :span="20">
                <el-form-item label="头像照片" prop="avatar" size="large">
                  <ImageUpload
                    v-model="form.elderInfo.avatar"
                    :disabled="isShowOrEdit"
                    :fileData="{ category: 'elder_profile', attachmentType: 'avatar' }"
                    :fileType="['png', 'jpg', 'jpeg']"
                    :isShowTip="true"
                    :limit="1"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
              <el-col :span="20">
                <el-form-item label="老人备注" prop="remark" size="large">
                  <el-input
                    v-model="form.checkIn.remark"
                    :disabled="isShowOrEdit"
                    placeholder="请输入备注内容"
                    rows="5"
                    type="textarea"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <div class="baseTitle">费用信息</div>
            <el-row>
              <el-col :span="8">
                <el-form-item label="合同编号" prop="contractNo" size="large">
                  <el-input
                    v-model="form.feeContract.contractNo"
                    :disabled="isShowOrEdit"
                    placeholder="请输入合同编号"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="签订日期" prop="signTime" size="large">
                  <el-date-picker
                    v-model="form.feeContract.signTime"
                    :disabled="isShowOrEdit"
                    :placeholder="isShowOrEdit ? '-' : '请选择签订日期'"
                    clearable
                    style="width: 100%"
                    type="date"
                    value-format="YYYY-MM-DD"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="合同开始时间" prop="contractStarttime">
                  <el-date-picker
                    v-model="form.feeContract.contractStarttime"
                    :disabled="isShowOrEdit"
                    :placeholder="isShowOrEdit ? '-' : '请选择合同开始时间'"
                    style="width: 100%"
                    type="date"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="合同结束时间" prop="contractEndtime">
                  <el-date-picker
                    v-model="form.feeContract.contractEndtime"
                    :disabled="isShowOrEdit"
                    :placeholder="isShowOrEdit ? '-' : '请选择合同结束时间'"
                    style="width: 100%"
                    type="date"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-row style="display: flex; justify-content: space-between">
                  <el-form-item
                    label="费用信息"
                    prop="elderName"
                    size="large"
                  ></el-form-item>
                  <el-button :disabled="isShowOrEdit" type="primary" @click="addfee"
                    >新增费用信息</el-button
                  >
                </el-row>
                <el-table v-loading="loadingfee" :data="feeTable" style="width: 100%">
                  <el-table-column
                    v-if="false"
                    align="center"
                    type="selection"
                    width="55"
                  />
                  <el-table-column v-if="false" align="center" label="序号" prop="id" />
                  <el-table-column align="center" label="费用项目" prop="feeItem" />
                  <el-table-column align="center" label="收费标准" prop="feeStandard" />
                  <el-table-column
                    align="center"
                    label="说明"
                    prop="description"
                    width="200px"
                  />
                  <el-table-column
                    align="center"
                    label="开始时间"
                    prop="startTime"
                    width="120px"
                  />
                  <el-table-column
                    align="center"
                    label="结束时间"
                    prop="endTime"
                    width="120px"
                  /><!--                                    <el-table-column align='center' label='合计金额' prop='amount'/>-->
                  <el-table-column
                    align="center"
                    label="折扣/优惠"
                    prop="discount"
                    width="150px"
                  />
                  <el-table-column align="center" label="实际缴纳" prop="actualAmount" />
                  <el-table-column align="center" label="备注" prop="careLevel" />
                  <el-table-column
                    align="center"
                    label="操作"
                    prop="careLevel"
                    width="150px"
                  >
                    <template #default="scope">
                      <el-button
                        :disabled="isShowOrEdit"
                        icon="Edit"
                        link
                        type="primary"
                        @click="feehandleUpdate(scope.row)"
                        >修改</el-button
                      >
                      <el-button
                        :disabled="isShowOrEdit"
                        icon="Delete"
                        link
                        type="primary"
                        @click="feehandleDelete(scope.row)"
                        >删除</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-row>
            <el-row style="margin-top: 10px">
              <el-col :span="8">
                <el-form-item label="缴费状态" prop="paymentStatus" size="large">
                  <el-select
                    v-model="form.feeContract.paymentStatus"
                    :disabled="isShowOrEdit"
                    placeholder="请选择缴费状态"
                  >
                    <el-option
                      v-for="dict in payment_status"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="收费人员" prop="collectorName" size="large">
                  <el-input
                    v-model="form.feeContract.collectorName"
                    :disabled="isShowOrEdit"
                    placeholder="请输入收费人员"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="缴费时间" prop="paymentTime" size="large">
                  <el-date-picker
                    v-model="form.feeContract.paymentTime"
                    :disabled="isShowOrEdit"
                    clearable
                    placeholder="请选择缴费时间"
                    style="width: 100%"
                    type="date"
                    value-format="YYYY-MM-DD"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="付款方式" prop="paymentMethod" size="large">
                  <el-select
                    v-model="form.feeContract.paymentMethod"
                    :disabled="isShowOrEdit"
                    placeholder="请选择付款方式"
                  >
                    <el-option
                      v-for="dict in payment_method"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="收费金额" prop="actualAmount" size="large">
                  <el-input
                    v-model="form.feeContract.actualAmount"
                    :disabled="isShowOrEdit"
                    placeholder="请输入收费金额"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注" prop="remark" size="large">
                  <el-input
                    v-model="form.feeContract.remark"
                    :disabled="isShowOrEdit"
                    placeholder="请输入备注"
                    type="textarea"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>
          <el-card v-if="stepActive == 2" shadow="hover">
            <el-row>
              <el-col :span="24">
                <el-form-item
                  label="能力评估表"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.assessment_form"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'assessment_form',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="true"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="和孚长者照护等级评估"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.elderl_care_level_assessment"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'elderl_care_level_assessment',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="false"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="个人照料计划表"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.personal_care_plan"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'personal_care_plan',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="false"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="入住老人健康体检表"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.health_checkup_form"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'health_checkup_form',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="false"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="压疮风险知青同意书"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.pressure_ulcer_risk"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'pressure_ulcer_risk',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="false"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="跌倒知青同意书"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.fall_risk_informed"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'fall_risk_informed',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="false"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>
          <el-card v-if="stepActive == 3" shadow="hover">
            <el-row>
              <el-col :span="24">
                <el-form-item
                  label="入住合同"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.accommodation_contract"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'accommodation_contract',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="false"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="入住协议书"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.accommodation_document"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'accommodation_document',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="false"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>
          <el-card v-if="stepActive == 4" shadow="hover">
            <el-row>
              <el-col :span="24">
                <el-form-item
                  label="入住老人及家属告知书"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.notification_to_elder"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'notification_to_elder',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="false"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="意外及风险告知书"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.notification_risks"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'notification_risks',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="false"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="自带口服药物委托书"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.oral_medication_authorization_form"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'oral_medication_authorization_form',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="false"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="长者用药安全告知书"
                  label-width="200px"
                  prop="elderName"
                  size="large"
                  style="border-bottom: 1px solid rgb(225, 225, 225)"
                >
                  <ImageUpload
                    v-model="form.notification_safety"
                    :fileData="{
                      category: 'elder_profile',
                      attachmentType: 'notification_safety',
                    }"
                    :fileType="[
                      'png',
                      'jpg',
                      'jpeg',
                      'doc',
                      'docx',
                      'xls',
                      'xlsx',
                      'ppt',
                      'pptx',
                      'txt',
                      'pdf',
                    ]"
                    :isShowTip="false"
                    :limit="uploadLimit"
                    @removeAtt="handleRemoveAtt"
                    @submitParentValue="handleGetFile"
                  ></ImageUpload>
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>
        </div>
        <!-- 固定右下角按钮浮动区域 -->
        <div class="fixed-bottom-actions">
          <el-button
            :disabled="stepActive <= 1"
            size="small"
            style="width: 64px; height: 28px; font-size: 13px; margin-right: 4px"
            type="primary"
            @click="nextHandle(9)"
            >上一步</el-button
          >
          <el-button
            v-if="stepActive == 1 && !isShowOrEdit"
            :disabled="stepActive != 1"
            size="small"
            style="width: 64px; height: 28px; font-size: 13px; margin-right: 4px"
            type="primary"
            @click="nextHandle('save')"
            >下一步</el-button
          >
          <el-button
            v-if="isShowOrEdit"
            :disabled="stepActive <= 0 || stepActive >= 4"
            size="small"
            style="width: 64px; height: 28px; font-size: 13px; margin-right: 4px"
            type="primary"
            @click="nextHandle(0)"
            >下一步</el-button
          >
          <el-button
            v-if="stepActive != 1 && !isShowOrEdit"
            :disabled="stepActive <= 0 || stepActive >= 4"
            size="small"
            style="width: 64px; height: 28px; font-size: 13px; margin-right: 4px"
            type="primary"
            @click="nextHandle(0)"
            >下一步</el-button
          >
          <el-button
            v-if="!isShowOrEdit"
            v-loading="saving"
            :disabled="stepActive != 4 || !form.elderInfo.elderName"
            size="small"
            style="width: 80px; height: 28px; font-size: 13px"
            type="primary"
            @click="submitFormSaveFile"
            >保存</el-button
          >
        </div>
      </div>
    </el-form>
    <el-drawer v-model="jhrDrawer" direction="rtl">
      <template #header><h4>添加监护人</h4></template>
      <template #default>
        <div>
          <el-form ref="jhrRef" :model="jhrform" :rules="jhrrules" label-width="120px">
            <el-row>
              <el-col :span="24">
                <el-form-item label="姓名" prop="name" size="large">
                  <el-input v-model="jhrform.name" placeholder="请输入姓名" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="联系电话" prop="phone" size="large">
                  <el-input v-model="jhrform.phone" placeholder="请输入联系电话" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="住址" prop="address" size="large">
                  <el-input
                    v-model="jhrform.address"
                    placeholder="请输入住址"
                    rows="4"
                    type="textarea"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="与老人关系" prop="relationship" size="large">
                  <el-radio-group
                    v-model="jhrform.relationship"
                    placeholder="请选择与老人关系"
                    size="large"
                    style="width: 100%"
                  >
                    <el-radio-button
                      v-for="item in relationship_elderly"
                      :key="item.value"
                      :label="item.value"
                      >{{ item.label }}</el-radio-button
                    >
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="是否紧急联系人"
                  prop="isEmergencyContact"
                  size="large"
                >
                  <el-radio-group
                    v-model="jhrform.isEmergencyContact"
                    placeholder="请选择是否紧急联系人"
                    size="large"
                    style="width: 100%"
                  >
                    <el-radio-button
                      v-for="item in emergency_contact"
                      :key="item.value"
                      :label="item.value"
                      >{{ item.label }}</el-radio-button
                    >
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="false">
              <el-form-item label="id" prop="isEmergencyContact" size="large">
                <el-input v-model="jhrform.id" />
              </el-form-item>
            </el-row>
          </el-form>
        </div>
      </template>
      <template #footer>
        <div style="flex: auto">
          <el-button v-if="jhrAddNoEdit" type="primary" @click="confirmClickAdd"
            >连续添加</el-button
          >
          <el-button v-if="jhrAddNoEdit" type="primary" @click="confirmClick"
            >添加</el-button
          >
          <el-button v-if="!jhrAddNoEdit" type="primary" @click="confirmClickEdit"
            >修改</el-button
          >
          <el-button @click="cancelClickjhr">取消</el-button>
        </div>
      </template>
    </el-drawer>
    <el-drawer v-model="feeDrawer" direction="rtl">
      <template #header><h4>费用详情</h4></template>
      <template #default>
        <div>
          <el-form ref="feeRef" :model="feeForm" :rules="feerules" label-width="120px">
            <el-row>
              <el-col :span="24">
                <el-form-item label="费用项目" prop="feeItem" size="large">
                  <el-select
                    v-model="feeForm.feeItem"
                    :disabled="isShowOrEdit"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="dict in feeItems"
                      :key="dict"
                      :label="dict"
                      :value="dict"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="收费标准" prop="feeStandard" size="large">
                  <el-input v-model="feeForm.feeStandard" placeholder="请输入收费标准" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="说明" prop="description" size="large">
                  <el-input
                    v-model="feeForm.description"
                    placeholder="请输入说明"
                    rows="4"
                    type="textarea"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="开始时间" prop="startTime" size="large">
                  <el-date-picker
                    v-model="feeForm.startTime"
                    clearable
                    placeholder="请选择开始时间"
                    type="date"
                    value-format="YYYY-MM-DD"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="结束时间" prop="endTime" size="large">
                  <el-date-picker
                    v-model="feeForm.endTime"
                    clearable
                    placeholder="请选择结束时间"
                    type="date"
                    value-format="YYYY-MM-DD"
                  ></el-date-picker>
                </el-form-item> </el-col
              ><!--                            <el-col :span='24'>--><!--                                <el-form-item label='合计金额' prop='discount' size='large'>--><!--                                    <el-input v-model='feeForm.discount' placeholder='请输入合计金额'/>--><!--                                </el-form-item>--><!--                            </el-col>-->
              <el-col :span="24">
                <el-form-item label="实际缴纳" prop="actualAmount" size="large">
                  <el-input v-model="feeForm.actualAmount" placeholder="请输入实际缴纳" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注" prop="remark" size="large">
                  <el-input
                    v-model="feeForm.remark"
                    placeholder="请输入备注"
                    rows="4"
                    type="textarea"
                  />
                </el-form-item>
                <el-input v-model="feeForm.id" type="hidden" />
                <el-input v-model="feeForm.elderId" type="hidden" />
                <el-input v-model="feeForm.contractId" type="hidden" />
                <el-input v-model="feeForm.amount" type="hidden" />
              </el-col>
            </el-row>
          </el-form>
        </div>
      </template>
      <template #footer>
        <div style="flex: auto">
          <el-button v-if="!feeAddNoEdit" type="primary" @click="confirmClickAddFee"
            >连续添加</el-button
          >
          <el-button v-if="!feeAddNoEdit" type="primary" @click="confirmClickFee"
            >添加</el-button
          >
          <el-button v-if="feeAddNoEdit" type="primary" @click="confirmClickEditFee"
            >修改</el-button
          >
          <el-button @click="cancelClickFee">取消</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>
<script name="addElder" setup>
import {
  addCheckInSave,
  CheckInUpdate,
  getAggregateInfo,
} from "@/api/ReceptionManagement/tcheckin";
import {
  listFileinfo,
  removeFileinfoById,
  updateElderIdAttachment,
} from "@/api/ReceptionManagement/telderAttachement";
import router from "@/router";
import { getTemId } from "@/utils/paramUtil.js";
import { ref } from "vue";
import { listRoom } from "@/api/roominfo/tLiveRoom";
import { getBuildingList, getFloorList, getRoomCardList } from "@/api/live/roommanage";
import { getRoleInfo, getOlderInfo } from "@/api/nurse/index";
import { listBed } from "@/api/roominfo/tLiveBed";
const { proxy } = getCurrentInstance();
const route = useRoute();
const inputValue = ref("");
const dynamicTags = ref([]);
const inputVisible = ref(false);
const elderInputRef = ref();
const elderId = ref("");
const fileOssIdList = ref([]);
const isShowOrEdit = ref(false);
const isShow = ref(false);
const eideFileList = ref([]);
const feeAddNoEdit = ref(false);
const jhrAddNoEdit = ref(false);
const formAddNoEdit = ref("add");
const noEdit = ref(false);
const feeItems = [
  "押金",
  "床位费",
  "取暖/空调费",
  "护理费",
  "照护费",
  "特殊护理费",
  "伙食费",
  "杂费",
];
const {
  sys_normal_disable,
  sys_user_sex,
  self_careability,
  capability_level,
  care_level,
  nursing_grade,
  political_status,
  residential_type,
  occupation_type,
  educational_level,
  marital_status,
  elderly_blood_type,
  financial_type,
  elderly_label,
  relationship_elderly,
  emergency_contact,
  payment_status,
  payment_method,
} = proxy.useDict(
  "sys_normal_disable",
  "sys_user_sex",
  "self_careability",
  "capability_level",
  "care_level",
  "nursing_grade",
  "political_status",
  "residential_type",
  "occupation_type",
  "educational_level",
  "marital_status",
  "elderly_blood_type",
  "financial_type",
  "elderly_label",
  "relationship_elderly",
  "emergency_contact",
  "payment_status",
  "payment_method"
);
const roomBed = [
  {
    id: 1,
    value: "201-01",
  },
  {
    id: 2,
    value: "201-02",
  },
  {
    id: 3,
    value: "201-03",
  },
  {
    id: 4,
    value: "201-04",
  },
];
const data = reactive({
  form: {
    /*入园信息保存*/ elderInfo: {} /*入院基本信息*/,
    checkIn: {
      roomId: null,
      bedId: null,
    } /*监护人信息列表*/,
    guardians: [] /*费用合同信息*/,
    feeContract: {} /*费用明细列表*/,
    feeDetails: [] /* 新增：保证 checkIns 初始化，防止未定义赋值报错 */,
    checkIns: {},
  },
  jhrform: {},
  jhrrules: {},
  feeForm: {},
  feerules: [],
  rules: {
    elderName: [
      {
        required: true,
        message: "请输入老人姓名",
        trigger: "blur",
        validator: (rule, value, callback) => {
          console.log("form.value.elderInfo.elderName", form.value.elderInfo);
          if (!form.value.elderInfo.elderName) {
            callback(new Error("老人姓名不能为空"));
          } else {
            callback();
          }
        },
      },
      { min: 0, max: 50, message: "老人姓名长度50个字符以内", trigger: "blur" },
    ],
    elderCode: [
      {
        required: true,
        message: "请输入老人编号",
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (!form.value.elderInfo.elderCode) {
            callback(new Error("老人编号不能为空"));
          } else {
            callback();
          }
        },
      },
      { min: 0, max: 20, message: "老人编号长度20个字符以内", trigger: "blur" },
    ],
  },

  queryParamsFiles: {
    pageNum: 1,
    pageSize: 2000,
    elderId: null,
  },
  queryParamsfee: {
    pageNum: 1,
    pageSize: 40,
    elderId: null,
  },
  queryParams: {
    pageNum: 1,
    pageSize: 100,
  },
});
const {
  form,
  jhrform,
  jhrrules,
  rules,
  feeForm,
  queryParamsFiles,
  queryParamsfee,
  queryParams,
} = toRefs(data);
const loadingfee = ref(false);
const saving = ref(false);
const buildingList = ref([]); //楼栋下拉列表
const floorList = ref([]); //楼层下拉列表
const roomList = ref([]); //楼层下拉列表
const bedList = ref([]); //床位下拉列表
// 限制上传数量
const uploadLimit = 100;
const uploadFileList = ref([]);

const getFiles = function (param) {
  listFileinfo(param).then((resFile) => {
    if (!form.value.assessment_form) {
      form.value.assessment_form = [];
    }
    eideFileList.value = resFile.rows.map((item) => {
      // console.log(item, "item....");
      if (item.attachmentType == "id_card_front_photo") {
        form.value.elderInfo.id_card_front_photo = item.filePath;
      } else if (item.attachmentType == "id_card_back_photo") {
        form.value.elderInfo.id_card_back_photo = item.filePath;
      } else if (item.attachmentType == "avatar") {
        form.value.elderInfo.avatar = item.filePath;
      } else if (
        item.attachmentType == "assessment_form" ||
        item.attachmentType == "elderl_care_level_assessment" ||
        item.attachmentType == "personal_care_plan" ||
        item.attachmentType == "health_checkup_form" ||
        item.attachmentType == "pressure_ulcer_risk" ||
        item.attachmentType == "fall_risk_informed" ||
        item.attachmentType == "accommodation_contract" ||
        item.attachmentType == "accommodation_document" ||
        item.attachmentType == "notification_to_elder" ||
        item.attachmentType == "notification_risks" ||
        item.attachmentType == "oral_medication_authorization_form" ||
        item.attachmentType == "notification_safety"
      ) {
        if (!form.value[item.attachmentType]) {
          form.value[item.attachmentType] = [];
        }
        let file = {};
        file.id = item.id;
        file.name = item.fileName;
        file.url = item.filePath;
        file.type = item.attachmentType;
        form.value[item.attachmentType].push(file);
      }
    });
  });
};

function getList() {
  var type = route.params.type;
  var id = route.params.id;
  if (type == "show") {
    isShowOrEdit.value = true;
    showbuildFloorData();
    getAggregate();
  } else if (type == "edit") {
    isShowOrEdit.value = false;
    formAddNoEdit.value = "edit";
    noEdit.value = true;
    showbuildFloorData();
    getAggregate();
  } else if (type == "add") {
    formAddNoEdit.value = "add";
    getBuildingList().then((res) => {
      buildingList.value = res.rows;
    });
    console.log(id, type, "init----");
  }
}

function getAggregate() {
  if (route.params.id) {
    var id = route.params.id;
    getAggregateInfo(id).then((res) => {
      console.log(res, "getAggregateInfo");
      form.value.elderInfo = res.data.elderInfo || {};
      form.value.checkIn = res.data.checkIn || {};
      form.value.guardians = jhrTable.value =
        res.data.guardians || []; /* 兼容经办人信息和费用信息赋值 */
      form.value.feeContract = res.data.feeContract
        ? { ...res.data.feeContract }
        : {}; /* 合同编号、签订日期回显赋值 */
      if (res.data.feeContract) {
        form.value.feeContract.contractNo =
          res.data.feeContract.contractNo || res.data.contractNo || "";
        form.value.feeContract.signTime =
          res.data.feeContract.signTime || res.data.signTime || "";
      }
      feeForm.value = res.data.feeContract ? { ...res.data.feeContract } : {};
      form.value.feeDetails = feeTable.value = Array.isArray(res.data.feeDetails)
        ? [...res.data.feeDetails]
        : [];
      dynamicTags.value = res.data.elderInfo?.elderTags
        ? res.data.elderInfo?.elderTags.split(",")
        : [];
      form.value.checkIn.roomId = parseInt(res.data.checkIn.roomId) || null;
      form.value.checkIn.bedId = parseInt(res.data.checkIn.bedId) || null;
      feeTable.value = Array.isArray(res.data.feeDetails) ? [...res.data.feeDetails] : [];
      form.value.checkIns.roomdIdbedId =
        (res.data.elderInfo?.roomId || "") +
        "-" +
        (res.data.elderInfo?.bedId || ""); /*form.value.checkIn.roomdIdbedId=res.data.*/
      queryParamsFiles.value.elderId = res.data.elderInfo.id;
      getFiles(queryParamsFiles.value);
      loadingfee.value = false;
    });
  }
}

function showbuildFloorData() {
  getBuildingList().then((res) => {
    buildingList.value = res.rows;
  });
  getFloorList().then((res) => {
    floorList.value = res.rows || [];
  });
  listRoom(queryParams.value).then((res) => {
    console.log(res, "room");
    roomList.value = res.rows || [];
  });
  listBed(queryParams.value).then((res) => {
    console.log(res, "bed");
    bedList.value = res.rows || [];
  });
}

function handleBuildingChange(val) {
  form.value.elderInfo.buildingName = val;
  console.log(val, "val");
  const filterInfo = buildingList.value.filter((item) => item.id == val);
  getFloorList(filterInfo[0].id).then((res) => {
    console.log(res, "getFloorListByBuild");
    floorList.value = res.rows;
  });
}

function handleFloorChange(val) {
  console.log(val, "111");
  form.value.elderInfo.floorNumber = val;
  console.log(floorList.value, "floorList");
  const floorId = floorList.value.filter((item) => item.floorNumber == val);
  listRoom({ floorId: floorId[0].id }).then((res) => {
    console.log(res, "getRoomListByBuild");
    roomList.value = res.rows;
  });
}
function handleRoomChange(val) {
  //form.elderInfo.roomNumber = val;
  listBed({ roomId: val, checkUsed: true }).then((res) => {
    console.log(res, "getUserByRoomId");
    bedList.value = res.rows;
  });
}
//roomBed

function handleBedChange(val) {
  form.value.checkIn.bedId = val;
  bedList.value.map((item) => {
    console.log(item, "roomitem");
    if (item.id == val) {
      form.value.checkIn.roomBed = item.bedNumber;
    }
    console.log(form.value.checkIn.roomBed, "form.value.roomName");
  });
}

/** 表单重置 */ function resetjhr() {
  jhrform.value = {
    name: null,
    phone: null,
    address: null,
    relationship: "父子",
    isEmergencyContact: "否",
  };
  proxy.resetForm("jhrRef");
}

function resetFee() {
  feeForm.value = {
    id: null,
    feeItem: null,
    feeStandard: null,
    description: null,
    startTime: null,
    endTime: null,
    discount: null,
    actualAmount: null,
  };
  proxy.resetForm("feeRef");
}

const jhrDrawer = ref(false);
const feeDrawer = ref(false);
const jhrTable = ref([]);
const feeTable = ref([]);
const stepActive = ref("01");

function nextHandle(step) {
  if (step == 1) {
    stepActive.value = 1;
  } else if (step == 2) {
    if (stepActive.value == 1) {
      submitFormAddBaseElder();
      proxy.$refs["checkInRef"].validate((valid) => {
        if (valid) {
          stepActive.value = 2;
        }
      });
    }
  } else if (step == 3) {
    if (stepActive.value == 1) {
      submitFormAddBaseElder();
      proxy.$refs["checkInRef"].validate((valid) => {
        if (valid) {
          stepActive.value = 3;
        }
      });
    }
    //stepActive.value = 3;
  } else if (step == 4) {
    if (stepActive.value == 1) {
      submitFormAddBaseElder();
    }
    proxy.$refs["checkInRef"].validate((valid) => {
      if (valid) {
        stepActive.value = 4;
      }
    });
    //stepActive.value = 4;
  } else if (step == 0) {
    stepActive.value++;
  } else if (step == 9 && stepActive.value > 0) {
    stepActive.value--;
  } else if (step == "save") {
    //第一步提交后提交文本信息
    submitFormAddBaseElder();
    proxy.$refs["checkInRef"].validate((valid) => {
      if (valid) {
        stepActive.value = 2;
      }
    });
    //stepActive.value = 2;
  }
}

/*第一个保存，提交老人的文本信息*/
function submitFormAddBaseElder() {
  proxy.$refs["checkInRef"].validate((valid) => {
    if (valid) {
      jhrTable.value.map((item) => {
        console.log(item.id, "item.id...");
        item.id = !item.id || String(item.id).startsWith("tmp-") ? "" : item.id;
      });

      form.value.guardians = jhrTable.value;
      form.value.elderInfo.elderTags = dynamicTags.value.join(",");
      // 合同编号、签订日期赋值

      form.value.contractNo = form.value.feeContract.contractNo || "";
      form.value.signTime = form.value.feeContract.signTime || "";

      form.value.feeDetails = feeTable.value;
      uploadFileList.value.map((item) => {
        if (item.type == "id_card_front_photo") {
          form.value.elderInfo.idCardFrontPhoto = item.url;
        } else if (item.type == "id_card_back_photo") {
          form.value.elderInfo.idCardBackPhoto = item.url;
        } else if (item.type == "avatar") {
          form.value.elderInfo.avatar = item.url;
        }
      });
      if (formAddNoEdit.value == "add") {
        addCheckInSave(form.value).then((res) => {
          elderId.value = res.data.elderId;
          console.log(res, "add");

          //proxy.$modal.msgSuccess("添加成功");
        });
      } else if (formAddNoEdit.value == "edit") {
        CheckInUpdate(form.value).then((res) => {
          console.log(res, "修改数据成功");
        });
      }
    } else {
      console.log("老人信息表单 校验不通过.");
    }
  });
}
//const obj = { path: "/elderInfo/checkin" };
//proxy.$tab.closeOpenPage(obj);

/*最后一步保存提交附件信息,更新老人的id到附件表*/
function submitFormSaveFile() {
  saving.value = true; /* 兜底赋值，防止 elderId.value 为空 */
  const obj = { path: "/elderInfo/checkin" };
  if (!elderId.value) elderId.value = queryParamsFiles.value.elderId;
  console.log(fileOssIdList.value?.length > 0, "是否有附件更新ID?");
  if (fileOssIdList.value?.length > 0) {
    updateElderIdAttachment(fileOssIdList.value, elderId.value).then((res) => {
      /* setTimeout(() => { */
      saving.value = false; /* }, 5000); */
      console.log(`最后一步保存成功: ${fileOssIdList.value.length}个附件`);

      proxy.$tab.closeOpenPage(obj);
    });
  } else {
    setTimeout(() => {
      saving.value = false;
      console.log("最后一步保存成功: 无附件");
      //router.replace("/elderInfo/checkin");
      proxy.$tab.closeOpenPage(obj);
    }, 100);
  }
  proxy.$modal.msgSuccess("保存成功");
}

/*添加监护人显示*/
function addJHR() {
  jhrDrawer.value = true;
  jhrAddNoEdit.value = true;
  resetjhr();
}

function jhrhandleUpdate(row) {
  jhrDrawer.value = true;
  jhrform.value = row;
  jhrAddNoEdit.value = false;
}

function confirmClickEdit() {
  jhrTable.value.map((item) => {
    if (item.id == jhrform.value.id) {
      item.relationship = jhrform.value.relationship;
      item.name = jhrform.value.name;
      item.phone = jhrform.value.phone;
      item.isEmergencyContact = jhrform.value.isEmergencyContact;
      item.address = jhrform.value.address;
    }
  });
  jhrDrawer.value = false;
}

/*添加监护人*/
function confirmClick() {
  proxy.$refs["jhrRef"].validate((valid) => {
    if (valid) {
      jhrform.value.id = getTemId();
      console.log(jhrform.value, "jhrform");
      let resobj = Object.assign({}, jhrform.value);
      jhrTable.value.push(resobj);
      jhrDrawer.value = false;
    }
  });
} /*连续添加监护人*/
function confirmClickAdd() {
  proxy.$refs["jhrRef"].validate((valid) => {
    if (valid) {
      jhrform.value.id = getTemId();
      let resobj = Object.assign({}, jhrform.value);
      jhrTable.value.push(resobj); /* jhrDrawer.value = false; */
      resetjhr();
      console.log(jhrform.value, "jhrform1111111");
    }
  });
}

function cancelClickjhr() {
  jhrDrawer.value = false;
  resetjhr();
}

function jhrhandleDelete(row) {
  jhrTable.value = jhrTable.value.filter((item) => item.id !== row.id);
}

function handleClose(tagname) {
  dynamicTags.value.splice(dynamicTags.value.indexOf(tagname), 1);
}

function inputClick() {
  inputVisible.value = true;
  nextTick(() => {
    elderInputRef.value.input.focus();
  });
}

function handleInputConfirm() {
  if (inputValue.value) dynamicTags.value.push(inputValue.value);
  inputVisible.value = false;
  inputValue.value = "";
}

function addfee() {
  feeDrawer.value = true; /*Date.now().toString(36).substring(0, 10),*/
}

/*连续添加费用信息*/
function confirmClickAddFee() {
  feeForm.value.id = Math.random() * 1000;
  proxy.$refs["feeRef"].validate((valid) => {
    if (valid) {
      var resobj = Object.assign({}, feeForm.value);
      feeTable.value.push(resobj);
      console.log(feeTable.value, "feeRef1111111");
      resetFee();
    }
  });
}

/*添加费用信息*/
function confirmClickFee() {
  feeForm.value.id = Math.random() * 1000;
  proxy.$refs["feeRef"].validate((valid) => {
    if (valid) {
      var resobj = Object.assign({}, feeForm.value);
      feeTable.value.push(resobj);
      console.log(feeTable.value, "feeRef22222222");
      feeDrawer.value = false;
      resetFee();
    }
  });
}

function cancelClickFee() {
  feeDrawer.value = false;
  resetFee();
}

/* 删除附件信息 */
const handleRemoveAtt = (uid, type) => {
  console.log(uid, "handleRemoveAtt", type);
  removeFileinfoById(uid).then((res) => {
    console.log(res, "删除附件信息成功");
    listFileinfo(queryParamsFiles.value).then((res) => {
      const findex = form.value[type].map((f) => f.id).indexOf(uid);
      form.value[type].splice(findex, 1);
    });
  });
};

/*上传完成后获取ssoid信息*/
function handleGetFile(value) {
  console.log(value, "handleGetFile---------");
  if (value) {
    if (Array.isArray(value)) {
      fileOssIdList.value = fileOssIdList.value.concat(value.map((it) => it.ossId));
    } else {
      fileOssIdList.value.push(value);
    }
  }
  uploadFileList.value.push(value[0]);
} /*打开费用修改*/
function feehandleUpdate(row) {
  feeDrawer.value = true;
  feeForm.value = row;
  feeAddNoEdit.value = true;
  console.log(row, "feehandleUpdate");
} /*多行修改费用*/
function confirmClickEditFee() {
  feeTable.value.map((item) => {
    if (item.id == feeForm.value.id) {
      item.feeItem = feeForm.value.feeItem;
      item.feeStandard = feeForm.value.feeStandard;
      item.description = feeForm.value.description;
      item.startTime = feeForm.value.startTime;
      item.endTime = feeForm.value.endTime;
      item.discount = feeForm.value.discount;
      item.actualAmount = feeForm.value.actualAmount;
      item.remark = feeForm.value.remark;
    }
  });
  feeDrawer.value = false;
}

function feehandleDelete(row) {
  console.log(row.id, "rowid");
  console.log(feeTable.value, "feeTable.value");
  feeTable.value = feeTable.value.filter((item) => {
    console.log(item, "item");
    return item.id != row.id;
  });
  console.log(feeTable.value, "feehandleDelete");
}

getList();
</script>
<style scoped>
.fixed-bottom-actions {
  position: fixed;
  right: 24px;
  bottom: 20px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-radius: 6px;
  padding: 6px 12px 6px 12px;
  display: flex;
  flex-direction: row;
  align-items: center;
  min-width: unset;
}

.stepList {
  display: flex;
  flex-direction: row;
  width: 100%;
}

.activeBackCss {
  width: 25%;
  height: 40px;
  padding-top: 6px;
  background-image: url("../../../assets/images/olders/stepSelect.png");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}

.NoactiveBackCss {
  width: 25%;
  height: 40px;
  color: #999;
  padding-top: 6px;
  background-image: url("../../../assets/images/olders/stepNoSelect.png");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}

.steptitleSelect {
  color: white;
  padding-left: 80px;
  font-size: 16px;
  width: 100%;
  text-align: center;
}

.steptitleNoSelect {
  color: rgb(60, 62, 66);
  padding-left: 80px;
  font-size: 16px;
  width: 100%;
  text-align: center;
}

.formCss {
  width: 70%;
  margin-right: 0px;
  margin-top: 10px;
  margin-left: 10%;
}

.baseTitle {
  font-size: 16px;
  color: rgb(64, 158, 225);
  font-weight: 600;
  margin-top: 20px;
}

.formAll {
  display: flex;
  justify-content: left;
}

.color {
  color: #409eff;
}

/* 自定义 Loading 样式 */
:deep(.el-button--small.is-loading) {
  ::before {
    width: 28px; /* 按钮宽度 */
    height: 28px; /* 按钮高度 */
    background-color: rgba(255, 255, 255, 0.9); /* 背景颜色 */
    border-radius: 6px; /* 圆角 */
    top: 0; /* 顶部对齐 */
    left: 0; /* 左侧对齐 */
    right: 0; /* 右侧对齐 */
    bottom: 0; /* 底部对齐 */
    margin: auto; /* 居中对齐 */
  }

  .el-loading-spinner {
    top: 50%; /* 垂直居中 */
    left: 50%; /* 水平居中 */
    transform: translate(-50%, -50%); /* 移动到中心 */
  }

  .el-loading-spinner .circular {
    width: 10px; /* Loading 图标大小 */
    height: 10px; /* Loading 图标大小 */
  }
}
</style>
