<template>
<div class="replace-consumables">
    <el-button type="primary" @click="goBack">返回工作台</el-button>
    <div class="headerTitle">
        <h2>更换易耗品记录表</h2>
    </div>
    <div style="text-align: right;">
        <el-button type="primary" @click="addNewElder">+ 新增老人</el-button>
    </div>
    <el-table :data="tableData" border style="width: 100%" :span-method="mergeTableRows">
        <!-- 老人信息列 -->
        <el-table-column label="老人信息" width="200" align="center" prop="avatar">
            <template #default="scope">
                <div class="elder-info">
                    <img :src="scope.row.avatar" alt="老人头像" class="avatar">
                    <div class="info">
                        <p class="leaderName">{{ scope.row.name }}</p>
                        <p>{{ scope.row.room }}</p>
                        <span class="processIndex">{{ scope.row.processIndex }}</span>
                    </div>
                </div>
            </template>
        </el-table-column>
        <el-table-column prop="roundCount" width="80" align="center">
            <template #default="scope">
                <div class="roundCount">{{ scope.row._serviceRecords.roundCount }}</div>
            </template>
        </el-table-column>
        <!-- 服务日期列 -->
        <el-table-column prop="serviceDate" label="服务日期" width="230" align="center">
            <template #default="scope">
                <el-date-picker v-model="scope.row._serviceRecords.serviceDate" type="date" placeholder="选择日期" style="width: 200px;"></el-date-picker>
            </template>
        </el-table-column>
        <!-- 服务项目列 -->
        <el-table-column prop="serviceItem" label="服务项目" width="180" align="center">
            <template #default="scope">
                <el-select v-model="scope.row._serviceRecords.serviceItem" placeholder="请选择服务项目">
                    <el-option label="胃管" value="胃管"></el-option>
                    <!-- 更多服务项目选项 -->
                </el-select>
            </template>
        </el-table-column>
        <!-- 数量列 -->
        <el-table-column prop="quantity" label="数量" width="130" align="center">
            <template #default="scope">
                <el-input-number v-model="scope.row._serviceRecords.quantity" :min="0" style="width: 100px;"></el-input-number>
            </template>
        </el-table-column>
        <!-- 价格列 -->
        <el-table-column prop="price" label="价格" width="130" align="center">
            <template #default="scope">
                <!-- <el-input-number v-model="scope.row._serviceRecords.price" :min="0" style="width: 100px;" placeholder="请输入价格"></el-input-number> -->
                 {{ scope.row.price }}
            </template>
        </el-table-column>
        <!-- 备注列 -->
        <el-table-column prop="remark" label="备注" min-width="140" align="center">
            <template #default="scope">
                <el-input v-model="scope.row._serviceRecords.remark" placeholder="请输入备注" type="textarea" :rows="2"></el-input>
            </template>
        </el-table-column>
        <!-- 操作列 -->
        <el-table-column label="操作" width="120" align="center">
            <template #default="scope">
                <el-button type="primary" :icon="Plus" circle @click.stop="handleAddRow(scope.row)" />
                <el-button type="danger" :icon="Delete" circle @click.stop="deleteServiceRecord(scope.$index)" :disabled="scope.row._serviceRecords.roundCount === 1 && originalData.find(elder => elder.id === scope.row.elderId).serviceRecords.length === 1" />
            </template>
        </el-table-column>
    </el-table>
    <div style="margin-top: 20px;text-align: center;">
        <el-button type="primary" @click="submit">提交</el-button>
        <el-button @click="cancel">取消</el-button>
    </div>
    <el-dialog v-model="visible" title="新增老人" width="70%" :close-on-click-modal="false" append-to-body>
        <el-form ref="formRef" :model="formRoom" :rules="rules" label-width="120px" label-position="left">
            <div class="room_info_top">
                <div class="title_room">
                    <h3>房间信息</h3>
                </div>
                <div class="room_form">
                    <el-row :gutter="24">
                        <el-col :span="8">
                            <el-form-item label="查房日期" prop="handoverDate">
                                <el-date-picker v-model="formRoom.handoverDate" type="date" placeholder="选择日期" style="width: 200px" value-format="YYYY-MM-DD"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="楼栋信息" prop="buildingId">
                                <el-select v-model="formRoom.buildingId" style="width: 200px" @change="getFloorListData">
                                    <el-option v-for="item in buildingList" :key="item.value" :label="item.buildingName" :value="item.id" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="楼栋层数" prop="floorId">
                                <el-select v-model="formRoom.floorId" :disabled="!formRoom.buildingId" style="width: 200px" @change="getRoomListData">
                                    <el-option v-for="item in floorList" :key="item.value" :label="item.floorName" :value="item.id" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="24">
                        <div class="roomList">
                            <el-check-tag v-for="item in roomOptions" :key="item.id" :checked="selectedRooms.some(r => r.roomId === item.id)" @change="handleRoomChange(item)">
                                {{ item.roomName }}
                            </el-check-tag>
                        </div>
                    </el-row>
                </div>
            </div>
        </el-form>
        <!-- 底部按钮 -->
        <template #footer>
            <el-button type="primary" @click="handleSubmit()">提交</el-button>
            <el-button @click="visible = false">取消</el-button>
        </template>

    </el-dialog>
</div>
</template>

  
<script setup>
import {
    getBuildingList,
    getFloorList
} from '@/api/live/roommanage'
import {
    listRoom
} from "@/api/roominfo/tLiveRoom";
import {
    Delete,
    Plus,
} from '@element-plus/icons-vue'
const router = useRouter()
import moment from 'moment'
const originalData = ref([{
        avatar: 'https://placehold.co/64x64',
        name: '王药师',
        room: '301 301-01',
        id: "1",
        serviceRecords: [{
                serviceDate: moment().format('YYYY-MM-DD'),
                serviceItem: '',
                quantity: 1,
                price: 0,
                remark: ''
            },
            {
                serviceDate: moment().format('YYYY-MM-DD'),
                serviceItem: '',
                quantity: 1,
                price: 0,
                remark: ''
            },
            // 更多服务记录...
        ]
    },
    {
        avatar: 'https://placehold.co/64x64',
        name: '王药师',
        room: '301 301-02',
        id: "2",
        serviceRecords: [{
                serviceDate: moment().format('YYYY-MM-DD'),
                serviceItem: '',
                quantity: 1,
                price: 0,
                remark: ''
            },
            // 更多服务记录...
        ]
    },
    // 更多默认数据项...
]);
const visible = ref(false)
const formRoom = ref({})
const buildingList = ref([]) //楼栋下拉列表
const floorList = ref([]) //楼层下拉列表
const roomOptions = ref([]) //房间
const selectedRooms = ref([])
const rules = ref({
    handoverDate: [{
        required: true,
        message: '请选择日期',
        trigger: 'change'
    }],
    buildingId: [{
        required: true,
        message: '请选择楼栋',
        trigger: 'change'
    }],
    floorId: [{
        required: true,
        message: '请选择楼层',
        trigger: 'change'
    }]
})
const tableData = ref([]);

const goBack = () => {
    router.push('/work/nurseworkstation')
}
// 增加服务记录
const handleAddRow = (row) => {
    const elderIndex = originalData.value.findIndex(elder => elder.id === row.elderId);
    if (elderIndex !== -1) {
        const newRecord = {
            serviceDate: moment().format('YYYY-MM-DD'),
            serviceItem: '',
            quantity: 1,
            price: 0,
            remark: ''
        };
        originalData.value[elderIndex].serviceRecords.push(newRecord);
        // 重新处理数据以更新表格
        tableData.value = preprocessData(originalData.value);
    }
};

// 删除服务记录
const deleteServiceRecord = (index) => {
    const rowToDelete = tableData.value[index];
    const elderIndex = originalData.value.findIndex(elder => elder.id === rowToDelete.elderId);

    if (elderIndex !== -1) {
        // 找到要删除的服务记录在原数据中的位置
        const recordIndex = originalData.value[elderIndex].serviceRecords.findIndex(
            record => record.serviceDate === rowToDelete._serviceRecords.serviceDate
        );

        if (recordIndex !== -1) {
            // 如果这是老人的最后一条记录，则删除整个老人条目
            if (originalData.value[elderIndex].serviceRecords.length === 1) {
                originalData.value.splice(elderIndex, 1);
            } else {
                // 否则只删除特定的服务记录
                originalData.value[elderIndex].serviceRecords.splice(recordIndex, 1);
            }
            // 重新处理数据以更新表格
            tableData.value = preprocessData(originalData.value);
        }
    }
};
const handleEdit = (index, row) => {
    console.log('编辑:', index, row);
};

const handleDelete = (index, row) => {
    originalData.value.splice(index, 1);
};

const save = () => {
    console.log('保存数据:', originalData.value);
};

const submit = () => {
    console.log('提交数据:', originalData.value);
};

const cancel = () => {
    console.log('取消操作');
};
// 合并单元格
const mergeTableRows = ({
    row,
    column,
    rowIndex,
    columnIndex
}) => {
    const fields = ['avatar'];
    const cellValue = row[column.property];
    if (fields.includes(column.property) || fields.includes(column.label) || fields.includes(column.type)) {
        const prevRow = tableData.value[rowIndex - 1];
        const nextRow = tableData.value[rowIndex + 1];
        // 修改比较逻辑，使得空值也能进行合并
        const currentValueIsNull = cellValue == null; // 检查cellValue是否为null或undefined
        const prevValueMatches = prevRow && prevRow.elderId === row.elderId && (prevRow[column.property] === cellValue || currentValueIsNull);
        const nextValueMatches = nextRow && nextRow.elderId === row.elderId && (nextRow[column.property] === cellValue || currentValueIsNull);
        if (prevValueMatches) {
            return {
                rowspan: 0,
                colspan: 0
            }; // 隐藏当前行的单元格
        } else if (nextValueMatches) {
            let countRowspan = 1;
            let tempRowIndex = rowIndex + 1;
            while (tempRowIndex < tableData.value.length &&
                tableData.value[tempRowIndex].elderId === row.elderId &&
                (tableData.value[tempRowIndex][column.property] === cellValue || currentValueIsNull)) {
                countRowspan++;
                tempRowIndex++;
            }
            if (countRowspan > 1) {
                return {
                    rowspan: countRowspan,
                    colspan: 1
                };
            }
        }
    }

    return {
        rowspan: 1,
        colspan: 1
    };
};
function preprocessData(datas) {
    let processIdToIndexMap = {}; // 用来存储processId与其对应索引的映射关系
    let currentIndex = 1; // 从1开始计数

    return datas.flatMap((elder) => {
        // 确保至少有一条查房记录
        if (!elder.serviceRecords || elder.serviceRecords.length === 0) {
            elder.serviceRecords = [createEmptyVisit(0)]; // 初始化为1次查房
        }

        // 为每个老人分配唯一的processIndex
        if (!processIdToIndexMap[elder.id]) {
            processIdToIndexMap[elder.id] = currentIndex++;
        }

        const processIndex = processIdToIndexMap[elder.id];

        // 更新每条记录的 roundCount
        elder.serviceRecords.forEach((serviceRecords, index) => {
            serviceRecords.roundCount = index + 1;
        });

        return elder.serviceRecords.map((serviceRecords, index) => ({
            ...elder,
            processIndex, // 添加processIndex
            ...serviceRecords,
            elderId: elder.id,
            visible: index === 0, // 默认只显示第一条记录            
            _serviceRecords: serviceRecords
        }));
    });
}

function createEmptyVisit(totalVisits) {
    return {
        roundCount: totalVisits + 1,
        serviceDate: moment().format('YYYY-MM-DD'),
        serviceItem: '',
        quantity: 1,
        price: 0,
        remark: ''
    };
}
// 添加新老人
const addNewElder = () => {
    visible.value = true;
};
const getBuildingListData = async () => {
    const res = await getBuildingList()
    buildingList.value = res.rows || []
}
const getFloorListData = async (val) => {
    floorList.value = []
    selectedRooms.value = []
    roomOptions.value = []
    formRoom.value.floorId = ""
    const res = await getFloorList(val)
    floorList.value = res.rows;
}
const getRoomListData = async (val) => {
    roomOptions.value = []
    selectedRooms.value = []
    const roomsRes = await listRoom({
        floorId: val
    })
    roomOptions.value = roomsRes.rows;
}
const handleRoomChange = (room) => {
    // 判断是否已经存在
    const index = selectedRooms.value.findIndex(r => r.roomId === room.id)

    if (index > -1) {
        // 如果已存在则移除
        selectedRooms.value.splice(index, 1)
    } else {
        // 如果不存在则添加
        selectedRooms.value.push({
            roomId: room.id,
            roomName: room.roomName
        })
    }
}
const initRequest = () => {
    // 初始化请求数据
    getBuildingListData()
    tableData.value = preprocessData(originalData.value);
}
onMounted(() => {
    initRequest()
})
</script>

  
<style scoped>
.headerTitle {
    text-align: center;
    color: #D9001B;
}

.replace-consumables {
    padding: 20px;
}

.elder-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    .processIndex {
        position: absolute;
        left: 5px;
        top: 5px;
        color: var(--el-color-primary);
        font-weight: bold;
    }
}

.avatar {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    margin-right: 10px;
}

.info p {
    margin: 0;
}

.leaderName {
    color: var(--el-color-primary);
    margin: 10px 0;
}

.roundCount {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: var(--el-color-primary);
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    margin-left: 10px;
}

.service-item {
    display: flex;
}

.roomList {
    display: flex;

    span {
        margin-left: 15px;
        width: 85px;
        height: 33px;
        line-height: inherit;
        text-align: center;
    }
}
</style>
