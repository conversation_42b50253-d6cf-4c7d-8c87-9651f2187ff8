{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./src/App.vue?c15c", "webpack:///src/App.vue", "webpack:///./src/App.vue?1160", "webpack:///./src/App.vue", "webpack:///./src/main.js", "webpack:///./src/App.vue?2c4a", "webpack:///../dist/vue-resize.esm.js"], "names": ["webpackJsonpCallback", "data", "moduleId", "chunkId", "chunkIds", "moreModules", "executeModules", "i", "resolves", "length", "installedChunks", "push", "Object", "prototype", "hasOwnProperty", "call", "modules", "parentJsonpFunction", "shift", "deferredModules", "apply", "checkDeferredModules", "result", "deferredModule", "fulfilled", "j", "depId", "splice", "__webpack_require__", "s", "installedModules", "app", "exports", "module", "l", "m", "c", "d", "name", "getter", "o", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "p", "jsonpArray", "window", "oldJsonpFunction", "slice", "Appvue_type_template_id_f6ea4904_render", "_vm", "this", "_h", "$createElement", "_c", "_self", "attrs", "id", "staticClass", "_v", "_s", "count", "on", "click", "$event", "show", "changeSize", "style", "width", "notify", "handleResize", "_e", "staticRenderFns", "Appvue_type_script_lang_js_", "methods", "console", "log", "Math", "round", "random", "src_Appvue_type_script_lang_js_", "component", "componentNormalizer", "options", "__file", "App", "<PERSON><PERSON>", "use", "Resize", "install", "ResizeObserver", "el", "render", "h", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_11_oneOf_1_0_node_modules_css_loader_index_js_ref_11_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_11_oneOf_1_2_node_modules_stylus_loader_index_js_ref_11_oneOf_1_3_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_App_vue_vue_type_style_index_0_lang_stylus___WEBPACK_IMPORTED_MODULE_0__", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_11_oneOf_1_0_node_modules_css_loader_index_js_ref_11_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_11_oneOf_1_2_node_modules_stylus_loader_index_js_ref_11_oneOf_1_3_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_App_vue_vue_type_style_index_0_lang_stylus___WEBPACK_IMPORTED_MODULE_0___default", "global", "getInternetExplorerVersion", "ua", "navigator", "userAgent", "msie", "indexOf", "parseInt", "substring", "trident", "rv", "edge", "__webpack_exports__", "isIE", "initCompat", "init", "tabindex", "_scopeId", "compareAndNotify", "_w", "$el", "offsetWidth", "offsetHeight", "$emit", "addResizeHandlers", "_resizeObject", "contentDocument", "defaultView", "addEventListener", "removeResizeHandlers", "onload", "removeEventListener", "mounted", "_this", "$nextTick", "document", "createElement", "setAttribute", "type", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "plugin", "version", "GlobalVue"], "mappings": "aACA,SAAAA,EAAAC,GAQA,IAPA,IAMAC,EAAAC,EANAC,EAAAH,EAAA,GACAI,EAAAJ,EAAA,GACAK,EAAAL,EAAA,GAIAM,EAAA,EAAAC,EAAA,GACQD,EAAAH,EAAAK,OAAoBF,IAC5BJ,EAAAC,EAAAG,GACAG,EAAAP,IACAK,EAAAG,KAAAD,EAAAP,GAAA,IAEAO,EAAAP,GAAA,EAEA,IAAAD,KAAAG,EACAO,OAAAC,UAAAC,eAAAC,KAAAV,EAAAH,KACAc,EAAAd,GAAAG,EAAAH,IAGAe,KAAAhB,GAEA,MAAAO,EAAAC,OACAD,EAAAU,OAAAV,GAOA,OAHAW,EAAAR,KAAAS,MAAAD,EAAAb,GAAA,IAGAe,IAEA,SAAAA,IAEA,IADA,IAAAC,EACAf,EAAA,EAAiBA,EAAAY,EAAAV,OAA4BF,IAAA,CAG7C,IAFA,IAAAgB,EAAAJ,EAAAZ,GACAiB,GAAA,EACAC,EAAA,EAAkBA,EAAAF,EAAAd,OAA2BgB,IAAA,CAC7C,IAAAC,EAAAH,EAAAE,GACA,IAAAf,EAAAgB,KAAAF,GAAA,GAEAA,IACAL,EAAAQ,OAAApB,IAAA,GACAe,EAAAM,IAAAC,EAAAN,EAAA,KAGA,OAAAD,EAIA,IAAAQ,EAAA,GAKApB,EAAA,CACAqB,IAAA,GAGAZ,EAAA,GAGA,SAAAS,EAAA1B,GAGA,GAAA4B,EAAA5B,GACA,OAAA4B,EAAA5B,GAAA8B,QAGA,IAAAC,EAAAH,EAAA5B,GAAA,CACAK,EAAAL,EACAgC,GAAA,EACAF,QAAA,IAUA,OANAhB,EAAAd,GAAAa,KAAAkB,EAAAD,QAAAC,IAAAD,QAAAJ,GAGAK,EAAAC,GAAA,EAGAD,EAAAD,QAKAJ,EAAAO,EAAAnB,EAGAY,EAAAQ,EAAAN,EAGAF,EAAAS,EAAA,SAAAL,EAAAM,EAAAC,GACAX,EAAAY,EAAAR,EAAAM,IACA1B,OAAA6B,eAAAT,EAAAM,EAAA,CAA0CI,YAAA,EAAAC,IAAAJ,KAK1CX,EAAAgB,EAAA,SAAAZ,GACA,qBAAAa,eAAAC,aACAlC,OAAA6B,eAAAT,EAAAa,OAAAC,YAAA,CAAwDC,MAAA,WAExDnC,OAAA6B,eAAAT,EAAA,cAAiDe,OAAA,KAQjDnB,EAAAoB,EAAA,SAAAD,EAAAE,GAEA,GADA,EAAAA,IAAAF,EAAAnB,EAAAmB,IACA,EAAAE,EAAA,OAAAF,EACA,KAAAE,GAAA,kBAAAF,QAAAG,WAAA,OAAAH,EACA,IAAAI,EAAAvC,OAAAwC,OAAA,MAGA,GAFAxB,EAAAgB,EAAAO,GACAvC,OAAA6B,eAAAU,EAAA,WAAyCT,YAAA,EAAAK,UACzC,EAAAE,GAAA,iBAAAF,EAAA,QAAAM,KAAAN,EAAAnB,EAAAS,EAAAc,EAAAE,EAAA,SAAAA,GAAgH,OAAAN,EAAAM,IAAqBC,KAAA,KAAAD,IACrI,OAAAF,GAIAvB,EAAA2B,EAAA,SAAAtB,GACA,IAAAM,EAAAN,KAAAiB,WACA,WAA2B,OAAAjB,EAAA,YAC3B,WAAiC,OAAAA,GAEjC,OADAL,EAAAS,EAAAE,EAAA,IAAAA,GACAA,GAIAX,EAAAY,EAAA,SAAAgB,EAAAC,GAAsD,OAAA7C,OAAAC,UAAAC,eAAAC,KAAAyC,EAAAC,IAGtD7B,EAAA8B,EAAA,eAEA,IAAAC,EAAAC,OAAA,gBAAAA,OAAA,oBACAC,EAAAF,EAAAhD,KAAA2C,KAAAK,GACAA,EAAAhD,KAAAX,EACA2D,IAAAG,QACA,QAAAvD,EAAA,EAAgBA,EAAAoD,EAAAlD,OAAuBF,IAAAP,EAAA2D,EAAApD,IACvC,IAAAU,EAAA4C,EAIA1C,EAAAR,KAAA,qBAEAU,6JCtJI0C,EAAM,WAAgB,IAAAC,EAAAC,KAAaC,EAAAF,EAAAG,eAA0BC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,MAAA,CAAOC,GAAA,QAAY,CAAAH,EAAA,OAAYI,YAAA,WAAsB,CAAAR,EAAAS,GAAA,WAAAT,EAAAU,GAAAV,EAAAW,UAAAP,EAAA,OAAAA,EAAA,UAAgEI,YAAA,SAAAI,GAAA,CAAyBC,MAAA,SAAAC,GAAyBd,EAAAe,MAAAf,EAAAe,QAAuB,CAAAf,EAAAS,GAAA,YAAAL,EAAA,UAAkCI,YAAA,SAAAI,GAAA,CAAyBC,MAAAb,EAAAgB,aAAwB,CAAAhB,EAAAS,GAAA,uBAAAT,EAAA,KAAAI,EAAA,OAAqDI,YAAA,WAAsB,CAAAJ,EAAA,YAAiBa,MAAA,CAAQC,MAAAlB,EAAAkB,MAAA,QAA8Bd,EAAA,mBAAwBQ,GAAA,CAAIO,OAAAnB,EAAAoB,iBAA2B,GAAApB,EAAAqB,QACpjBC,EAAA,GCcAC,EAAA,CACAjD,KAAA,MAEArC,KAHA,WAIA,OACA0E,MAAA,EACAI,MAAA,EACAG,MAAA,MAIAM,QAAA,CACAJ,aADA,WAEAnB,KAAAU,QACAc,QAAAC,IAAA,WAGAV,WANA,WAOAf,KAAAiB,MAAAS,KAAAC,MAAA,IAAAD,KAAAE,UAAA,OCjC8TC,EAAA,0BCQ9TC,EAAgBnF,OAAAoF,EAAA,KAAApF,CACdkF,EACA/B,EACAuB,GACF,EACA,KACA,KACA,MAIAS,EAAAE,QAAAC,OAAA,UACe,IAAAC,EAAAJ,UCffK,OAAIC,IAAIC,QAERb,QAAQC,IAAIY,OAAQC,OAASC,QAG7B,IAAIJ,OAAI,CACNK,GAAI,OACJC,OAAQ,SAAAC,GAAC,OAAIA,EAAER,2CCZjB,IAAAS,EAAAhF,EAAA,QAAAiF,EAAAjF,EAAA2B,EAAAqD,GAAqfC,EAAG,sFCAxf,SAAAC,GAAA,SAASC,IACR,IAAIC,EAAKpD,OAAOqD,UAAUC,UAEtBC,EAAOH,EAAGI,QAAQ,SACtB,GAAID,EAAO,EAEV,OAAOE,SAASL,EAAGM,UAAUH,EAAO,EAAGH,EAAGI,QAAQ,IAAKD,IAAQ,IAGhE,IAAII,EAAUP,EAAGI,QAAQ,YACzB,GAAIG,EAAU,EAAG,CAEhB,IAAIC,EAAKR,EAAGI,QAAQ,OACpB,OAAOC,SAASL,EAAGM,UAAUE,EAAK,EAAGR,EAAGI,QAAQ,IAAKI,IAAM,IAG5D,IAAIC,EAAOT,EAAGI,QAAQ,SACtB,OAAIK,EAAO,EAEHJ,SAASL,EAAGM,UAAUG,EAAO,EAAGT,EAAGI,QAAQ,IAAKK,IAAQ,KAIxD,EAvBT7F,EAAAS,EAAAqF,EAAA,sBAAAnB,IAAA3E,EAAAS,EAAAqF,EAAA,sBAAAlB,IA0BA,IAAImB,OAAO,EAEX,SAASC,IACHA,EAAWC,OACfD,EAAWC,MAAO,EAClBF,GAAyC,IAAlCZ,KAIT,IAAIP,EAAiB,CAAEE,OAAQ,WAC7B,IAAI1C,EAAMC,KAASC,EAAKF,EAAIG,eAAmBC,EAAKJ,EAAIK,MAAMD,IAAMF,EAAG,OAAOE,EAAG,MAAO,CAAEI,YAAa,kBAAmBF,MAAO,CAAEwD,SAAY,SAC7IxC,gBAAiB,GAAIyC,SAAU,kBAClCzF,KAAM,kBAENkD,QAAS,CACRwC,iBAAkB,WACb/D,KAAKgE,KAAOhE,KAAKiE,IAAIC,aAAelE,KAAKC,KAAOD,KAAKiE,IAAIE,eAC5DnE,KAAKgE,GAAKhE,KAAKiE,IAAIC,YACnBlE,KAAKC,GAAKD,KAAKiE,IAAIE,aACnBnE,KAAKoE,MAAM,YAGbC,kBAAmB,WAClBrE,KAAKsE,cAAcC,gBAAgBC,YAAYC,iBAAiB,SAAUzE,KAAK+D,kBAC/E/D,KAAK+D,oBAENW,qBAAsB,WACjB1E,KAAKsE,eAAiBtE,KAAKsE,cAAcK,UACvCjB,GAAQ1D,KAAKsE,cAAcC,iBAC/BvE,KAAKsE,cAAcC,gBAAgBC,YAAYI,oBAAoB,SAAU5E,KAAK+D,yBAE5E/D,KAAKsE,cAAcK,UAK7BE,QAAS,WACR,IAAIC,EAAQ9E,KAEZ2D,IACA3D,KAAK+E,UAAU,WACdD,EAAMd,GAAKc,EAAMb,IAAIC,YACrBY,EAAM7E,GAAK6E,EAAMb,IAAIE,eAEtB,IAAI5E,EAASyF,SAASC,cAAc,UACpCjF,KAAKsE,cAAgB/E,EACrBA,EAAO2F,aAAa,cAAe,QACnC3F,EAAO2F,aAAa,YAAa,GACjC3F,EAAOoF,OAAS3E,KAAKqE,kBACrB9E,EAAO4F,KAAO,YACVzB,GACH1D,KAAKiE,IAAImB,YAAY7F,GAEtBA,EAAOvD,KAAO,cACT0H,GACJ1D,KAAKiE,IAAImB,YAAY7F,IAGvB8F,cAAe,WACdrF,KAAK0E,yBAKP,SAASpC,EAAQH,GAChBA,EAAIL,UAAU,kBAAmBS,GACjCJ,EAAIL,UAAU,iBAAkBS,GAIjC,IAAI+C,EAAS,CAEZC,QAAS,QACTjD,QAASA,GAINkD,EAAY,KACM,qBAAX7F,OACV6F,EAAY7F,OAAOwC,IACS,qBAAXU,IACjB2C,EAAY3C,EAAOV,KAEhBqD,GACHA,EAAUpD,IAAIkD,GAIAA", "file": "js/app.afb5acee.js", "sourcesContent": [" \t// install a JSONP callback for chunk loading\n \tfunction webpackJsonpCallback(data) {\n \t\tvar chunkIds = data[0];\n \t\tvar moreModules = data[1];\n \t\tvar executeModules = data[2];\n\n \t\t// add \"moreModules\" to the modules object,\n \t\t// then flag all \"chunkIds\" as loaded and fire callback\n \t\tvar moduleId, chunkId, i = 0, resolves = [];\n \t\tfor(;i < chunkIds.length; i++) {\n \t\t\tchunkId = chunkIds[i];\n \t\t\tif(installedChunks[chunkId]) {\n \t\t\t\tresolves.push(installedChunks[chunkId][0]);\n \t\t\t}\n \t\t\tinstalledChunks[chunkId] = 0;\n \t\t}\n \t\tfor(moduleId in moreModules) {\n \t\t\tif(Object.prototype.hasOwnProperty.call(moreModules, moduleId)) {\n \t\t\t\tmodules[moduleId] = moreModules[moduleId];\n \t\t\t}\n \t\t}\n \t\tif(parentJsonpFunction) parentJsonpFunction(data);\n\n \t\twhile(resolves.length) {\n \t\t\tresolves.shift()();\n \t\t}\n\n \t\t// add entry modules from loaded chunk to deferred list\n \t\tdeferredModules.push.apply(deferredModules, executeModules || []);\n\n \t\t// run deferred modules when all chunks ready\n \t\treturn checkDeferredModules();\n \t};\n \tfunction checkDeferredModules() {\n \t\tvar result;\n \t\tfor(var i = 0; i < deferredModules.length; i++) {\n \t\t\tvar deferredModule = deferredModules[i];\n \t\t\tvar fulfilled = true;\n \t\t\tfor(var j = 1; j < deferredModule.length; j++) {\n \t\t\t\tvar depId = deferredModule[j];\n \t\t\t\tif(installedChunks[depId] !== 0) fulfilled = false;\n \t\t\t}\n \t\t\tif(fulfilled) {\n \t\t\t\tdeferredModules.splice(i--, 1);\n \t\t\t\tresult = __webpack_require__(__webpack_require__.s = deferredModule[0]);\n \t\t\t}\n \t\t}\n \t\treturn result;\n \t}\n\n \t// The module cache\n \tvar installedModules = {};\n\n \t// object to store loaded and loading chunks\n \t// undefined = chunk not loaded, null = chunk preloaded/prefetched\n \t// Promise = chunk loading, 0 = chunk loaded\n \tvar installedChunks = {\n \t\t\"app\": 0\n \t};\n\n \tvar deferredModules = [];\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/vue-resize/\";\n\n \tvar jsonpArray = window[\"webpackJsonp\"] = window[\"webpackJsonp\"] || [];\n \tvar oldJsonpFunction = jsonpArray.push.bind(jsonpArray);\n \tjsonpArray.push = webpackJsonpCallback;\n \tjsonpArray = jsonpArray.slice();\n \tfor(var i = 0; i < jsonpArray.length; i++) webpackJsonpCallback(jsonpArray[i]);\n \tvar parentJsonpFunction = oldJsonpFunction;\n\n\n \t// add entry module to deferred list\n \tdeferredModules.push([0,\"chunk-vendors\"]);\n \t// run deferred modules when ready\n \treturn checkDeferredModules();\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{attrs:{\"id\":\"app\"}},[_c('div',{staticClass:\"counter\"},[_vm._v(\"Resize: \"+_vm._s(_vm.count))]),_c('div',[_c('button',{staticClass:\"toggle\",on:{\"click\":function($event){_vm.show = !_vm.show}}},[_vm._v(\"Toggle\")]),_c('button',{staticClass:\"change\",on:{\"click\":_vm.changeSize}},[_vm._v(\"Change CSS size\")])]),(_vm.show)?_c('div',{staticClass:\"resized\"},[_c('textarea',{style:({ width: (_vm.width + \"px\") })}),_c('resize-observer',{on:{\"notify\":_vm.handleResize}})],1):_vm._e()])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div id=\"app\">\n    <div class=\"counter\">Resize: {{ count }}</div>\n    <div>\n      <button class=\"toggle\" @click=\"show = !show\">Toggle</button>\n      <button class=\"change\" @click=\"changeSize\">Change CSS size</button>\n    </div>\n    <div class=\"resized\" v-if=\"show\">\n      <textarea :style=\"{ width: `${width}px` }\"></textarea>\n      <resize-observer @notify=\"handleResize\"/>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'app',\n\n  data () {\n    return {\n      count: 0,\n      show: true,\n      width: 300,\n    }\n  },\n\n  methods: {\n    handleResize () {\n      this.count++\n      console.log('handle')\n    },\n\n    changeSize () {\n      this.width = Math.round(Math.random() * 400) + 100\n    },\n  },\n}\n</script>\n\n<style lang=\"stylus\">\n$color = #42b983\n\n#app\n  font-family 'Avenir', Helvetica, Arial, sans-serif\n  text-align center\n  color #2c3e50\n  margin-top 60px\n\n.counter\n  font-size 42px\n  color $color\n\n.resized\n  position relative\n  border solid 1px $color\n  margin 12px\n  padding @margin\n  display inline-block\n</style>\n", "import mod from \"-!../node_modules/cache-loader/dist/cjs.js??ref--12-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/cache-loader/dist/cjs.js??ref--12-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./App.vue?vue&type=template&id=f6ea4904&\"\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=stylus&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import '../../dist/vue-resize.css'\nimport Vue from 'vue'\nimport Resize, { install, ResizeObserver } from '../../'\nimport App from './App.vue'\n\nVue.use(Resize)\n\nconsole.log(Resize, install, ResizeObserver)\n\n/* eslint-disable no-new */\nnew Vue({\n  el: '#app',\n  render: h => h(App),\n})\n", "import mod from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--11-oneOf-1-0!../node_modules/css-loader/index.js??ref--11-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--11-oneOf-1-2!../node_modules/stylus-loader/index.js??ref--11-oneOf-1-3!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=0&lang=stylus&\"; export default mod; export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--11-oneOf-1-0!../node_modules/css-loader/index.js??ref--11-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--11-oneOf-1-2!../node_modules/stylus-loader/index.js??ref--11-oneOf-1-3!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=0&lang=stylus&\"", "function getInternetExplorerVersion() {\n\tvar ua = window.navigator.userAgent;\n\n\tvar msie = ua.indexOf('MSIE ');\n\tif (msie > 0) {\n\t\t// IE 10 or older => return version number\n\t\treturn parseInt(ua.substring(msie + 5, ua.indexOf('.', msie)), 10);\n\t}\n\n\tvar trident = ua.indexOf('Trident/');\n\tif (trident > 0) {\n\t\t// IE 11 => return version number\n\t\tvar rv = ua.indexOf('rv:');\n\t\treturn parseInt(ua.substring(rv + 3, ua.indexOf('.', rv)), 10);\n\t}\n\n\tvar edge = ua.indexOf('Edge/');\n\tif (edge > 0) {\n\t\t// Edge (IE 12+) => return version number\n\t\treturn parseInt(ua.substring(edge + 5, ua.indexOf('.', edge)), 10);\n\t}\n\n\t// other browser\n\treturn -1;\n}\n\nvar isIE = void 0;\n\nfunction initCompat() {\n\tif (!initCompat.init) {\n\t\tinitCompat.init = true;\n\t\tisIE = getInternetExplorerVersion() !== -1;\n\t}\n}\n\nvar ResizeObserver = { render: function render() {\n\t\tvar _vm = this;var _h = _vm.$createElement;var _c = _vm._self._c || _h;return _c('div', { staticClass: \"resize-observer\", attrs: { \"tabindex\": \"-1\" } });\n\t}, staticRenderFns: [], _scopeId: 'data-v-b329ee4c',\n\tname: 'resize-observer',\n\n\tmethods: {\n\t\tcompareAndNotify: function compareAndNotify() {\n\t\t\tif (this._w !== this.$el.offsetWidth || this._h !== this.$el.offsetHeight) {\n\t\t\t\tthis._w = this.$el.offsetWidth;\n\t\t\t\tthis._h = this.$el.offsetHeight;\n\t\t\t\tthis.$emit('notify');\n\t\t\t}\n\t\t},\n\t\taddResizeHandlers: function addResizeHandlers() {\n\t\t\tthis._resizeObject.contentDocument.defaultView.addEventListener('resize', this.compareAndNotify);\n\t\t\tthis.compareAndNotify();\n\t\t},\n\t\tremoveResizeHandlers: function removeResizeHandlers() {\n\t\t\tif (this._resizeObject && this._resizeObject.onload) {\n\t\t\t\tif (!isIE && this._resizeObject.contentDocument) {\n\t\t\t\t\tthis._resizeObject.contentDocument.defaultView.removeEventListener('resize', this.compareAndNotify);\n\t\t\t\t}\n\t\t\t\tdelete this._resizeObject.onload;\n\t\t\t}\n\t\t}\n\t},\n\n\tmounted: function mounted() {\n\t\tvar _this = this;\n\n\t\tinitCompat();\n\t\tthis.$nextTick(function () {\n\t\t\t_this._w = _this.$el.offsetWidth;\n\t\t\t_this._h = _this.$el.offsetHeight;\n\t\t});\n\t\tvar object = document.createElement('object');\n\t\tthis._resizeObject = object;\n\t\tobject.setAttribute('aria-hidden', 'true');\n\t\tobject.setAttribute('tabindex', -1);\n\t\tobject.onload = this.addResizeHandlers;\n\t\tobject.type = 'text/html';\n\t\tif (isIE) {\n\t\t\tthis.$el.appendChild(object);\n\t\t}\n\t\tobject.data = 'about:blank';\n\t\tif (!isIE) {\n\t\t\tthis.$el.appendChild(object);\n\t\t}\n\t},\n\tbeforeDestroy: function beforeDestroy() {\n\t\tthis.removeResizeHandlers();\n\t}\n};\n\n// Install the components\nfunction install(Vue) {\n\tVue.component('resize-observer', ResizeObserver);\n\tVue.component('ResizeObserver', ResizeObserver);\n}\n\n// Plugin\nvar plugin = {\n\t// eslint-disable-next-line no-undef\n\tversion: \"0.4.4\",\n\tinstall: install\n};\n\n// Auto-install\nvar GlobalVue = null;\nif (typeof window !== 'undefined') {\n\tGlobalVue = window.Vue;\n} else if (typeof global !== 'undefined') {\n\tGlobalVue = global.Vue;\n}\nif (GlobalVue) {\n\tGlobalVue.use(plugin);\n}\n\nexport { install, ResizeObserver };\nexport default plugin;\n"], "sourceRoot": ""}