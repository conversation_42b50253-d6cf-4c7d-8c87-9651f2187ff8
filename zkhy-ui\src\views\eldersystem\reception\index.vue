<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryRef"
      :inline="true"
      :model="queryParams"
      label-width="68px"
    >
      <el-form-item label="老人姓名" prop="elderName">
        <el-input
          v-model="queryParams.elderName"
          clearable
          placeholder="请输入老人姓名"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="接待人" prop="receptionPerson">
        <el-input
          v-model="queryParams.receptionPerson"
          clearable
          placeholder="请输入接待人"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="咨询时间" style="width: 308px">
        <el-date-picker
          v-model="dateRange"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          range-separator="至"
          start-placeholder="开始日期"
          type="daterange"
          value-format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          clearable
          placeholder="状态"
          style="width: 120px"
        >
          <el-option :value="''" label="全部" />
          <el-option
            v-for="dict in follow_results"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="渠道" prop="channel">
        <el-select
          v-model="queryParams.channel"
          clearable
          placeholder="请选择渠道"
          style="width: 120px"
        >
          <el-option :value="''" label="全部" />
          <el-option
            v-for="dict in channel_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <el-row justify="end">
      <el-form-item>
        <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        <el-button icon="Plus" plain type="primary" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-row>
    <el-table v-loading="loading" :data="ReceptionList" border stripe>
      <el-table-column align="center" label="咨询时间" min-width="180" prop="consultTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.consultTime, "{y}-{m}-{d} {h}:{m}") }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="咨询类型" prop="consultType">
        <!--                <template #default='scope'>-->
        <!--                    <dict-tag :options='consultation_type' :value='scope.row.consultType'/>-->
        <!--                </template>-->
      </el-table-column>
      <el-table-column
        align="center"
        label="老人姓名"
        min-width="110px"
        prop="elderName"
      />
      <el-table-column
        align="center"
        label="老人性别"
        min-width="80px"
        prop="elderGender"
      >
        <template #default="scope">
          <dict-tag :options="sys_user_sex" :value="scope.row.elderGender" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="老人年龄" prop="elderAge" />
      <!--            <el-table-column v-if='false' align='center' label='老人电话' prop='elderPhone'/>-->
      <el-table-column
        align="center"
        label="咨询人姓名"
        min-width="110px"
        prop="consultPersonName"
      />
      <el-table-column
        align="center"
        label="咨询人电话"
        min-width="110px"
        prop="consultPersonPhone"
      />
      <el-table-column
        align="center"
        label="与老人关系"
        min-width="100px"
        prop="relation"
      >
      </el-table-column>
      <el-table-column align="center" label="接待人" prop="receptionPerson" />
      <el-table-column align="center" label="状态" prop="status">
        <template #default="scope">
          <dict-tag :options="follow_results" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="回访次数" prop="visitCount" />
      <el-table-column align="center" label="渠道" prop="channel">
        <template #default="scope">
          <dict-tag :options="channel_type" :value="scope.row.channel" />
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        align="center"
        label="咨询内容"
        prop="consultContent"
      />
      <el-table-column v-if="false" align="center" label="备注" prop="remark" />
      <el-table-column v-if="false" align="center" label="系统内置" prop="ReceptionType">
        <template #default="scope">
          <dict-tag :options="sys_yes_no" :value="scope.row.ReceptionType" />
        </template>
      </el-table-column>
      <el-table-column
        v-if="false"
        :show-overflow-tooltip="true"
        align="center"
        label="备注"
        prop="remark"
      />
      <el-table-column
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        label="操作"
        min-width="200"
      >
        <template #default="scope">
          <el-button icon="Edit" link type="primary" @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button icon="Edit" link type="primary" @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button icon="Delete" link type="primary" @click="handleDelete(scope.row)"
            >删除</el-button
          >
          <el-button icon="ChatLineSquare" link type="primary">
            <router-link
              :to="'/ReceptionManagement/receptionVisit/detail/' + scope.row.id"
            >
              <span>回访记录</span>
            </router-link>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNum"
      :total="total"
      @pagination="getList"
    />
    <!-- 添加或修改参数配置对话框 -->
    <el-dialog v-model="open" :title="title" append-to-body width="50%">
      <template #header>
        <h2 class="dc-dialog-header-16">{{ title }}咨询信息</h2>
        <div style="width: 100px; height: 3px; background-color: rgb(50, 109, 234)"></div>
      </template>
      <el-row :gutter="20">
        <!--                <el-col :span='2'></el-col>-->
        <el-col :span="24">
          <el-form ref="ReceptionRef" :model="form" :rules="rules" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="咨询时间" prop="consultTime">
                  <el-date-picker
                    v-model="form.consultTime"
                    :readonly="openVisit"
                    clearable
                    format="YYYY-MM-DD HH:mm"
                    placeholder="请选择咨询时间"
                    size="large"
                    style="width: 100%"
                    type="datetime"
                    value-format="YYYY-MM-DD HH:mm:ss"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="咨询类型" prop="consultType">
                  <!--                                    <el-select v-model='form.consultType' :disabled='openVisit' placeholder='请选择咨询类型' size='large' style='width: 100%'>
                                                                            <el-option v-for='item in consultation_type' :key='item.value' :label='item.label' :value='item.value'/>
                                                                        </el-select>-->
                  <el-input
                    v-model="form.consultType"
                    :readonly="openVisit"
                    placeholder="请输入咨询类型"
                    size="large"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="老人姓名" prop="elderName">
                  <el-input
                    v-model="form.elderName"
                    :readonly="openVisit"
                    placeholder="请输入老人姓名"
                    size="large"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="老人性别" prop="elderGender">
                  <el-radio-group
                    v-model="form.elderGender"
                    :disabled="openVisit"
                    placeholder="请输入老人性别"
                    size="large"
                  >
                    <el-radio-button
                      v-for="dict in sys_user_sex"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-radio-button>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="老人年龄" prop="elderAge">
                  <el-input
                    v-model="form.elderAge"
                    :readonly="openVisit"
                    placeholder="请输入老人年龄"
                    size="large"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="老人电话" prop="elderPhone">
                  <el-input
                    v-model="form.elderPhone"
                    :readonly="openVisit"
                    placeholder="请输入老人电话"
                    size="large"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="咨询人姓名" prop="consultPersonName">
                  <el-input
                    v-model="form.consultPersonName"
                    :readonly="openVisit"
                    placeholder="请输入咨询人姓名"
                    size="large"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="与老人关系" prop="relation">
                  <el-input
                    v-model="form.relation"
                    :readonly="openVisit"
                    placeholder="请输入老人姓名"
                    size="large"
                  />
                  <el-select
                    v-model="form.relation"
                    :disabled="openVisit"
                    placeholder="请选择咨询类型"
                    size="large"
                    style="width: 100%"
                    v-if="false"
                  >
                    <el-option
                      v-for="item in relationship_elderly"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="咨询人电话" prop="consultPersonPhone">
                  <el-input
                    v-model="form.consultPersonPhone"
                    :readonly="openVisit"
                    placeholder="请输入咨询人电话"
                    size="large"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="渠道" prop="channel">
                  <el-select
                    v-model="form.channel"
                    :disabled="openVisit"
                    placeholder="请选择渠道"
                    size="large"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in channel_type"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                  <!--                                    <el-input v-model='form.channel' :readonly='openVisit' placeholder='请输入渠道' size='large'/>-->
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="渠道内容" prop="channelContent">
                  <el-input
                    v-model="form.channelContent"
                    :readonly="openVisit"
                    placeholder="请输入渠道内容"
                    rows="3"
                    size="large"
                    type="textarea"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="咨询内容">
                  <el-input
                    v-model="form.consultContent"
                    :readonly="openVisit"
                    placeholder="请输入咨询内容"
                    rows="3"
                    size="large"
                    type="textarea"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="接待人" prop="receptionPerson">
                  <el-input
                    v-model="form.receptionPerson"
                    :readonly="openVisit"
                    placeholder="请输入接待人"
                    size="large"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="记录时间" prop="createTime">
                  <el-date-picker
                    v-model="form.createTime"
                    :readonly="openVisit"
                    clearable
                    format="YYYY-MM-DD HH:mm"
                    placeholder="请选择记录时间"
                    size="large"
                    style="width: 100%"
                    type="datetime"
                    value-format="YYYY-MM-DD HH:mm:ss"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                  <el-input
                    v-model="form.remark"
                    :readonly="openVisit"
                    V-IF="false"
                    placeholder="请输入内容"
                    size="large"
                    type="textarea"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-col>
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="!openVisit" size="large" type="primary" @click="submitForm"
            >确 定</el-button
          >
          <el-button size="large" @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script name="reception" setup>
import {
  addReception,
  delReception,
  getReception,
  listReception,
  updateReception,
} from "@/api/ReceptionManagement/tReception";
import moment from "moment";

const router = useRouter();
const { proxy } = getCurrentInstance();
const {
  sys_yes_no,
  sys_user_sex,
  consultation_type,
  relationship_elderly,
  channel_type,
  follow_results,
} = proxy.useDict(
  "sys_yes_no",
  "sys_user_sex",
  "consultation_type",
  "relationship_elderly",
  "channel_type",
  "follow_results"
);
const { reception_review } = proxy.useDict("reception_review");
const ReceptionList = ref([]);
const open = ref(false);
const openVisit = ref(false);
const titleVisit = ref("");
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    elderName: undefined,
    channel: "",
    status: "",
    ReceptionType: undefined,
  },
  rules: {
    elderName: [
      {
        required: true,
        message: "老人姓名不能为空",
        trigger: "blur",
      },
    ],
    elderAge: [
      {
        required: true,
        message: "老人年龄不能为空",
        trigger: "blur",
      },
    ],
    consultPersonName: [
      {
        required: true,
        message: "咨询人姓名不能为空",
        trigger: "blur",
      },
    ],
    consultPersonPhone: [
      {
        required: true,
        message: "咨询人电话不能为空",
        trigger: "blur",
      },
    ],
    // channel: [
    //   {
    //     required: true,
    //     message: "渠道不能为空",
    //     trigger: "blur",
    //   },
    // ],
    // elderAge: [
    //   // {
    //   //     required: true,
    //   //     message : "年龄不能为空",
    //   //     trigger : "blur",
    //   // },
    //   {
    //     validator: (rule, value, callback) => {
    //       // console.log(value,"age value");
    //       if (value == null || value === "") {
    //         callback();
    //       }
    //       const age = parseInt(value);
    //       if (!/^\d+$/.test(value) || age < 18 || age > 200) {
    //         form.value.elderAge = "";
    //         callback(new Error("年龄应该是合理的年龄范围,如18到200岁之间."));
    //       } else {
    //         callback();
    //       }
    //     },
    //     trigger: "blur",
    //   },
    //],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询参数列表 */
function getList() {
  loading.value = true;
  listReception(proxy.addDateRange(queryParams.value, dateRange.value)).then(
    (response) => {
      ReceptionList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    }
  );
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    consultTime: null,
    consultType: null,
    elderName: null,
    elderGender: 0,
    elderAge: null,
    elderPhone: null,
    consultPersonName: null,
    consultPersonPhone: null,
    relation: null,
    receptionPerson: null,
    status: "01",
    visitCount: null,
    channel: null,
    channelContent: null,
    consultContent: null,
    createBy: null,
    createTime: moment().format("YYYY-MM-DD HH:mm:ss"),
    updateBy: null,
    updateTime: null,
    remark: null,
  };
  proxy.resetForm("ReceptionRef");
  openVisit.value = false;
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.ReceptionId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "新增";
}

//回访记录
// function handleShowVisit(row) {
//     router.push("/ReceptionManagement/receptionVisit/detail/" + row.id);
// }

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const ReceptionId = row.id || ids.value;
  getReception(ReceptionId).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改";
  });
}

/** 修改按钮操作 */
function handleView(row) {
  reset();
  const ReceptionId = row.id || ids.value;
  getReception(ReceptionId).then((response) => {
    form.value = response.data;
    openVisit.value = true;
    open.value = true;
    title.value = titleVisit.value;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["ReceptionRef"].validate((valid) => {
    // console.log(valid,"-----valid result");
    if (valid) {
      if (form.value.id != undefined) {
        updateReception(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addReception(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const ReceptionIds = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除参数编号为"' + ReceptionIds + '"的数据项？')
    .then(function () {
      return delReception(ReceptionIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

getList();
</script>
<style scoped>
@import "@/assets/styles/zkhy.scss";

.el-descriptions__cell {
  width: 25% !important;
}

.itemcss {
  width: 25% !important;
}
</style>
