<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" @tab-change="handleTabChange">
      <el-tab-pane label="药品收取记录" name="drugReceiveRecord">
        <DrugReceiveRecord v-if="activeTab === 'drugReceiveRecord'" />
      </el-tab-pane>
      <el-tab-pane label="药品预备记录" name="drugPrepareRecord">
        <DrugPrepareRecord v-if="activeTab === 'drugPrepareRecord'" />
      </el-tab-pane>
      <el-tab-pane label="药品领用记录" name="drugUseRecord">
        <DrugUseRecord v-if="activeTab === 'drugUseRecord'" />
      </el-tab-pane>
      <el-tab-pane label="药品处理记录" name="drugProcessRecord">
        <DrugProcessRecord v-if="activeTab === 'drugProcessRecord'" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, defineAsyncComponent } from 'vue';

const activeTab = ref('drugReceiveRecord');

const DrugReceiveRecord = defineAsyncComponent(() =>
  import('./drugReceiveRecord/index.vue')
);
const DrugPrepareRecord = defineAsyncComponent(() =>
  import('./drugPrepareRecord/index.vue')
);
const DrugUseRecord = defineAsyncComponent(() =>
  import('./drugUseRecord/index.vue')
);
const DrugProcessRecord = defineAsyncComponent(() =>
  import('./drugProcessRecord/index.vue')
);

const handleTabChange = (tab) => {
  activeTab.value = tab;
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>