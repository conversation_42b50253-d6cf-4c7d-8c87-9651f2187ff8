<template>
    <div class="log-review-container">
        <!-- 返回工作台按钮 -->
        <el-button type="primary" @click="goBack">
            返回工作台
        </el-button>
    
        <!-- 查询表单 -->
        <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form" label-width="90px">
            <el-form-item label="消毒日期" prop="logDate">
                <el-date-picker style="width: 200px;" v-model="queryParams.logDate" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
            </el-form-item>
    
            <el-form-item label="楼栋信息" prop="department">
                <el-select v-model="queryParams.department" placeholder="全部" clearable style="width: 200px;">
                    <el-option label="全部" value="" />
                    <el-option label="护士部" value="护士部" />
                    <el-option label="医生部" value="医生部" />
                    <el-option label="行政部" value="行政部" />
                </el-select>
            </el-form-item>
    
            <el-form-item label="楼层层数" prop="reviewStatus">
                <el-select v-model="queryParams.reviewStatus" placeholder="全部" clearable style="width: 200px;">
                    <el-option label="全部" value="" />
                    <el-option label="已审阅" value="已审阅" />
                    <el-option label="未审阅" value="未审阅" />
                </el-select>
            </el-form-item>
    
            <el-form-item label="消毒人员" prop="nurseName">
                <el-input style="width: 200px;" v-model="queryParams.nurseName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="房间号" prop="roomNumber">
                <el-input style="width: 200px;" v-model="queryParams.roomNumber" placeholder="请输入" clearable />
            </el-form-item>
    
            <el-form-item>
                <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
                <el-button @click="resetQuery" icon="Refresh">重置</el-button>
            </el-form-item>
        </el-form>
    
        <!-- 表格 -->
        <!-- 增加全选按钮和打印按钮 -->
        <div class="table-header-btns">
            <el-button type="primary" @click="toggleAllSelection">全选</el-button>
            <el-button type="primary" @click="handlePrint" icon="Printer">打印选中数据</el-button>
        </div>
        <el-table 
            ref="multipleTableRef"
            :data="tableData" 
            border 
            style="width: 100%"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column prop="id" label="序号" width="80" align="center" />
            <el-table-column prop="logDate" label="消毒日期" min-width="120" align="center" />
            <el-table-column prop="nurseName" label="房间号" min-width="120" align="center" />
            <el-table-column prop="department" label="楼栋层数" min-width="120" align="center" />
            <el-table-column prop="department" label="楼栋信息" min-width="120" align="center" />
            <el-table-column prop="department" label="紫外线灯编号" min-width="120" align="center" />
            <el-table-column prop="department" label="消毒区域" min-width="120" align="center" />
            <el-table-column prop="department" label="消毒时间" min-width="120" align="center" />
            <el-table-column prop="department" label="消毒时长" min-width="120" align="center" />
            <el-table-column prop="department" label="辐照强度结果" min-width="120" align="center" />
            <el-table-column prop="department" label="消毒人员" min-width="120" align="center" />
            <el-table-column prop="department" label="监督人员" min-width="120" align="center" />
            <el-table-column prop="department" label="记录人" min-width="120" align="center" />
            <el-table-column label="操作" width="180" align="center" fixed="right">
                <template #default="{ row }">
                    <el-button type="primary" link @click="showDetail(row)">详情</el-button>
                    <el-button type="primary" link @click="handleDelete(row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
    
        <!-- 分页 -->
        <div class="paginationBox">
            <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="queryParams.pageNum" :page-sizes="[10, 20, 30, 50]" :page-size="queryParams.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" />
        </div>
        
        <!-- 详情对话框 -->
        <el-dialog title="详情" v-model="detailVisible" width="50%">
            <div class="disinfection-detail">
                <div class="title_room">
                    房间信息
                </div>
                <div class="detail-content">
                    <div class="room-info">
                        <div class="info-item">
                            <span class="label">房间号：</span>
                            <span class="value">{{ recordData.roomNumber || '-'}}</span>
                        </div>
                        <div class="info-items">
                            <div class="info-item">
                                <span class="label">楼栋信息：</span>
                                <span class="value">{{ recordData.buildingName || '-' }}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">楼层信息：</span>
                                <span class="value">{{ recordData.floorNumber || '-' }}层</span>
                            </div>
                        </div>
                    </div>
                    <div class="title_room">
                        消毒信息
                    </div>
                    <div class="detail-contents">
                        <div class="info-item">
                            <span class="label">消毒日期：</span>
                            <span class="value">{{ recordData.logDate || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">紫外线灯编号：</span>
                            <span class="value">{{ recordData.logDate || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">消毒时间：</span>
                            <span class="value">{{ recordData.logDate || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">消毒时长：</span>
                            <span class="value">{{ recordData.logDate || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">辐照强度结果：</span>
                            <span class="value">{{ recordData.logDate || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">消毒人员：</span>
                            <span class="value">{{ recordData.logDate || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">监督人员：</span>
                            <span class="value">{{ recordData.logDate || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">记录人：</span>
                            <span class="value">{{ recordData.logDate || '-' }}</span>
                        </div>
                        <div class="info-items">
                            <span class="label">消毒区域：</span>
                            <span class="value">{{ recordData.logDate || '阿首都阿事实上收拾收拾是撒是撒是撒是撒是撒是撒是撒是撒是撒是撒是撒是撒是撒是撒是撒是撒是撒是撒是撒是撒是撒是撒是撒是撒是撒' }}</span>
                        </div>
                        <div class="info-items">
                            <span class="label">备注：</span>
                            <span class="value">{{ recordData.logDate || '-' }}</span>
                        </div>
                    </div>
                </div>
            </div>
    
            <template #footer>
                <el-button type="primary" @click="detailVisible = false" plain>返回</el-button>
            </template>
        </el-dialog>
    </div>
    </template>
    
    <script setup>
    import { ref, reactive, onMounted } from 'vue'
    import { ElMessage, ElMessageBox } from 'element-plus'
    import { useRouter } from 'vue-router'
    
    // 查询参数
    const queryParams = reactive({
        logDate: '',
        department: '',
        reviewStatus: '',
        nurseName: '',
        pageNum: 1,
        pageSize: 10
    })
    const recordData = ref({})
    const router = useRouter()
    // 表格数据
    const tableData = ref([])
    const total = ref(0)
    const detailVisible = ref(false)
    const currentDetail = ref(null)
    const multipleTableRef = ref() // 表格引用
    const selectedRows = ref([]) // 选中的行数据
    
    // 模拟数据
    const mockData = [
        {
            id: 1,
            logDate: '2025-6-26',
            nurseName: '王护士',
            department: '护士部',
            reviewStatus: '未审阅',
            roomNumber: '101',
            buildingName: 'A栋',
            floorNumber: '1'
        },
        {
            id: 2,
            logDate: '2025-6-26',
            nurseName: '王护士',
            department: '护士部',
            reviewStatus: '未审阅',
            roomNumber: '102',
            buildingName: 'A栋',
            floorNumber: '1'
        },
        {
            id: 3,
            logDate: '2025-6-25',
            nurseName: '王护士',
            department: '护士部',
            reviewStatus: '未审阅',
            roomNumber: '201',
            buildingName: 'A栋',
            floorNumber: '2'
        },
        {
            id: 4,
            logDate: '2025-6-24',
            nurseName: '王护士',
            department: '护士部',
            reviewStatus: '未审阅',
            roomNumber: '202',
            buildingName: 'A栋',
            floorNumber: '2'
        },
        {
            id: 5,
            logDate: '2025-6-23',
            nurseName: '王护士',
            department: '护士部',
            reviewStatus: '已审阅',
            roomNumber: '301',
            buildingName: 'B栋',
            floorNumber: '3'
        },
        {
            id: 6,
            logDate: '2025-6-22',
            nurseName: '王护士',
            department: '护士部',
            reviewStatus: '已审阅',
            roomNumber: '302',
            buildingName: 'B栋',
            floorNumber: '3'
        },
        {
            id: 7,
            logDate: '2025-6-21',
            nurseName: '李护士',
            department: '护士部',
            reviewStatus: '已审阅',
            roomNumber: '401',
            buildingName: 'B栋',
            floorNumber: '4'
        },
        {
            id: 8,
            logDate: '2025-6-20',
            nurseName: '李护士',
            department: '护士部',
            reviewStatus: '已审阅',
            roomNumber: '402',
            buildingName: 'B栋',
            floorNumber: '4'
        },
        {
            id: 9,
            logDate: '2025-6-19',
            nurseName: '李护士',
            department: '护士部',
            reviewStatus: '已审阅',
            roomNumber: '501',
            buildingName: 'C栋',
            floorNumber: '5'
        },
        {
            id: 10,
            logDate: '2025-6-18',
            nurseName: '李护士',
            department: '护士部',
            reviewStatus: '已审阅',
            roomNumber: '502',
            buildingName: 'C栋',
            floorNumber: '5'
        },
    ]
    
    // 获取表格数据
    const getList = () => {
        // 模拟API请求
        return new Promise(resolve => {
            setTimeout(() => {
                // 过滤数据
                let filteredData = [...mockData]
    
                if (queryParams.logDate) {
                    filteredData = filteredData.filter(item => item.logDate === queryParams.logDate)
                }
    
                if (queryParams.department) {
                    filteredData = filteredData.filter(item => item.department === queryParams.department)
                }
    
                if (queryParams.reviewStatus) {
                    filteredData = filteredData.filter(item => item.reviewStatus === queryParams.reviewStatus)
                }
    
                if (queryParams.nurseName) {
                    filteredData = filteredData.filter(item =>
                        item.nurseName.includes(queryParams.nurseName)
                    )
                }
    
                // 分页
                const start = (queryParams.pageNum - 1) * queryParams.pageSize
                const end = start + queryParams.pageSize
                const pageData = filteredData.slice(start, end)
    
                tableData.value = pageData
                total.value = filteredData.length
                resolve()
            }, 300)
        })
    }
    
    // 查询
    const handleQuery = () => {
        queryParams.pageNum = 1
        getList()
    }
    
    // 重置
    const resetQuery = () => {
        queryParams.logDate = ''
        queryParams.department = ''
        queryParams.reviewStatus = ''
        queryParams.nurseName = ''
        queryParams.pageNum = 1
        getList()
    }
    
    // 分页
    const handleSizeChange = (val) => {
        queryParams.pageSize = val
        getList()
    }
    
    const handleCurrentChange = (val) => {
        queryParams.pageNum = val
        getList()
    }
    
    // 详情
    const showDetail = (row) => {
        recordData.value = row
        detailVisible.value = true
    }
    
    // 删除
    const handleDelete = (row) => {
        ElMessageBox.confirm('确定删除该紫外线记录表吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            // 这里应该是调用删除API
            ElMessage.success('删除成功')
            getList()
        }).catch(() => {
            ElMessage.info('已取消删除')
        })
    }
    
    // 返回工作台
    const goBack = () => {
        router.push('/work/nurseworkstation')
    }
    
    // 全选/取消全选
    const toggleAllSelection = () => {
        if (selectedRows.value.length === tableData.value.length) {
            multipleTableRef.value.clearSelection()
        } else {
            tableData.value.forEach(row => {
                multipleTableRef.value.toggleRowSelection(row, true)
            })
        }
    }
    
    // 处理选中行变化
    const handleSelectionChange = (val) => {
        selectedRows.value = val
    }
    
    // 打印选中数据
    const handlePrint = () => {
        if (selectedRows.value.length === 0) {
            ElMessage.warning('请先选择要打印的数据')
            return
        }
        
        // 创建一个新窗口用于打印
        const printWindow = window.open('', '_blank')
        printWindow.document.write(`
            <html>
                <head>
                    <title>消毒记录打印</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        h1 { text-align: center; margin-bottom: 20px; }
                        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: center; color: #666; }
                        th { background-color: #f2f2f2; }
                        .print-date { text-align: right; margin-bottom: 20px; }
                    </style>
                </head>
                <body>
                    <h1>紫外线消毒记录表</h1>
                    <div class="print-date">打印日期: ${new Date().toLocaleDateString()}</div>
                    <table>
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>消毒日期</th>
                                <th>房间号</th>
                                <th>楼栋层数</th>
                                <th>楼栋信息</th>
                                <th>紫外线灯编号</th>
                                <th>消毒区域</th>
                                <th>消毒时间</th>
                                <th>消毒时长</th>
                                <th>辐照强度结果</th>
                                <th>消毒人员</th>
                                <th>监督人员</th>
                                <th>记录人</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${selectedRows.value.map(row => `
                                <tr>
                                    <td>${row.id}</td>
                                    <td>${row.logDate}</td>
                                    <td>${row.roomNumber}</td>
                                    <td>${row.floorNumber}层</td>
                                    <td>${row.buildingName}</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>${row.nurseName}</td>
                                    <td>-</td>
                                    <td>-</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                    <script>
                        window.onload = function() {
                            window.print();
                            window.close();
                        }
                    <\/script>
                </body>
            </html>
        `)
        printWindow.document.close()
    }
    
    // 初始化
    onMounted(() => {
        getList()
    })
    </script>
    
    <style scoped>
    .log-review-container {
        padding: 20px;
    }
    
    .back-btn {
        margin-bottom: 20px;
        padding-left: 0;
    }
    
    .search-form {
        margin-bottom: 20px;
    }
    
    .paginationBox {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
    }
    
    .nurse-log {
        .titleLog {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #D9001B;
            text-align: center;
        }
    }
    
    .table-style {
        border: 1px solid #ebeef5;
        border-collapse: collapse;
        width: 100%;
    
        td {
            border: 1px solid #ebeef5;
            padding: 8px;
            font-size: 14px;
        }
    }
    
    .tdColor {
        color: #D9001B
    }
    
    .table-header-btns {
        display: flex;
        margin-bottom: 10px;
    }
    
    .room-info {}
    
    .title_room {
        font-weight: bold;
        font-size: 16px;
        margin-bottom: 16px;
        color: #2c3e50;
        border-bottom: 1px solid #e0e7ef;
        padding-bottom: 8px;
        padding-top: 10px;
    }
    
    .info-items {
        display: flex;
    
        .info-item {
            flex-basis: 30%;
            margin-top: 10px;
        }
    }
    
    .detail-contents {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
    
        .info-item {
            flex-basis: 50%;
            margin-bottom: 10px;
        }
    
        .info-items {
            display: flex;
            flex-basis: 100%;
            margin-bottom: 10px;
    
            .label {
                width: 120px;
                display: inline-block;
            }
        }
    }
    </style>