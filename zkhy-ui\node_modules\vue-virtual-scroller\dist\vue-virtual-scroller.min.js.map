{"version": 3, "file": "vue-virtual-scroller.min.js", "sources": ["../src/config.js", "../../../node_modules/vue-resize/dist/vue-resize.esm.js", "../../../node_modules/vue-observe-visibility/dist/vue-observe-visibility.esm.js", "../../../node_modules/scrollparent/scrollparent.js", "../src/components/common.js", "../src/utils.js", "../src/components/RecycleScroller.vue", "../src/components/DynamicScroller.vue", "../src/components/DynamicScrollerItem.vue", "../src/index.js", "../src/mixins/IdState.js"], "sourcesContent": ["export default {\n  itemsLimit: 1000,\n}\n", "function getInternetExplorerVersion() {\n\tvar ua = window.navigator.userAgent;\n\n\tvar msie = ua.indexOf('MSIE ');\n\tif (msie > 0) {\n\t\t// IE 10 or older => return version number\n\t\treturn parseInt(ua.substring(msie + 5, ua.indexOf('.', msie)), 10);\n\t}\n\n\tvar trident = ua.indexOf('Trident/');\n\tif (trident > 0) {\n\t\t// IE 11 => return version number\n\t\tvar rv = ua.indexOf('rv:');\n\t\treturn parseInt(ua.substring(rv + 3, ua.indexOf('.', rv)), 10);\n\t}\n\n\tvar edge = ua.indexOf('Edge/');\n\tif (edge > 0) {\n\t\t// Edge (IE 12+) => return version number\n\t\treturn parseInt(ua.substring(edge + 5, ua.indexOf('.', edge)), 10);\n\t}\n\n\t// other browser\n\treturn -1;\n}\n\nvar isIE = void 0;\n\nfunction initCompat() {\n\tif (!initCompat.init) {\n\t\tinitCompat.init = true;\n\t\tisIE = getInternetExplorerVersion() !== -1;\n\t}\n}\n\nvar ResizeObserver = { render: function render() {\n\t\tvar _vm = this;var _h = _vm.$createElement;var _c = _vm._self._c || _h;return _c('div', { staticClass: \"resize-observer\", attrs: { \"tabindex\": \"-1\" } });\n\t}, staticRenderFns: [], _scopeId: 'data-v-b329ee4c',\n\tname: 'resize-observer',\n\n\tmethods: {\n\t\tcompareAndNotify: function compareAndNotify() {\n\t\t\tif (this._w !== this.$el.offsetWidth || this._h !== this.$el.offsetHeight) {\n\t\t\t\tthis._w = this.$el.offsetWidth;\n\t\t\t\tthis._h = this.$el.offsetHeight;\n\t\t\t\tthis.$emit('notify');\n\t\t\t}\n\t\t},\n\t\taddResizeHandlers: function addResizeHandlers() {\n\t\t\tthis._resizeObject.contentDocument.defaultView.addEventListener('resize', this.compareAndNotify);\n\t\t\tthis.compareAndNotify();\n\t\t},\n\t\tremoveResizeHandlers: function removeResizeHandlers() {\n\t\t\tif (this._resizeObject && this._resizeObject.onload) {\n\t\t\t\tif (!isIE && this._resizeObject.contentDocument) {\n\t\t\t\t\tthis._resizeObject.contentDocument.defaultView.removeEventListener('resize', this.compareAndNotify);\n\t\t\t\t}\n\t\t\t\tdelete this._resizeObject.onload;\n\t\t\t}\n\t\t}\n\t},\n\n\tmounted: function mounted() {\n\t\tvar _this = this;\n\n\t\tinitCompat();\n\t\tthis.$nextTick(function () {\n\t\t\t_this._w = _this.$el.offsetWidth;\n\t\t\t_this._h = _this.$el.offsetHeight;\n\t\t});\n\t\tvar object = document.createElement('object');\n\t\tthis._resizeObject = object;\n\t\tobject.setAttribute('aria-hidden', 'true');\n\t\tobject.setAttribute('tabindex', -1);\n\t\tobject.onload = this.addResizeHandlers;\n\t\tobject.type = 'text/html';\n\t\tif (isIE) {\n\t\t\tthis.$el.appendChild(object);\n\t\t}\n\t\tobject.data = 'about:blank';\n\t\tif (!isIE) {\n\t\t\tthis.$el.appendChild(object);\n\t\t}\n\t},\n\tbeforeDestroy: function beforeDestroy() {\n\t\tthis.removeResizeHandlers();\n\t}\n};\n\n// Install the components\nfunction install(Vue) {\n\tVue.component('resize-observer', ResizeObserver);\n\tVue.component('ResizeObserver', ResizeObserver);\n}\n\n// Plugin\nvar plugin = {\n\t// eslint-disable-next-line no-undef\n\tversion: \"0.4.5\",\n\tinstall: install\n};\n\n// Auto-install\nvar GlobalVue = null;\nif (typeof window !== 'undefined') {\n\tGlobalVue = window.Vue;\n} else if (typeof global !== 'undefined') {\n\tGlobalVue = global.Vue;\n}\nif (GlobalVue) {\n\tGlobalVue.use(plugin);\n}\n\nexport { install, ResizeObserver };\nexport default plugin;\n", "function _typeof(obj) {\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) {\n    for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) arr2[i] = arr[i];\n\n    return arr2;\n  }\n}\n\nfunction _iterableToArray(iter) {\n  if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === \"[object Arguments]\") return Array.from(iter);\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance\");\n}\n\nfunction processOptions(value) {\n  var options;\n\n  if (typeof value === 'function') {\n    // Simple options (callback-only)\n    options = {\n      callback: value\n    };\n  } else {\n    // Options object\n    options = value;\n  }\n\n  return options;\n}\nfunction throttle(callback, delay) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var timeout;\n  var lastState;\n  var currentArgs;\n\n  var throttled = function throttled(state) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    currentArgs = args;\n    if (timeout && state === lastState) return;\n    var leading = options.leading;\n\n    if (typeof leading === 'function') {\n      leading = leading(state, lastState);\n    }\n\n    if ((!timeout || state !== lastState) && leading) {\n      callback.apply(void 0, [state].concat(_toConsumableArray(currentArgs)));\n    }\n\n    lastState = state;\n    clearTimeout(timeout);\n    timeout = setTimeout(function () {\n      callback.apply(void 0, [state].concat(_toConsumableArray(currentArgs)));\n      timeout = 0;\n    }, delay);\n  };\n\n  throttled._clear = function () {\n    clearTimeout(timeout);\n    timeout = null;\n  };\n\n  return throttled;\n}\nfunction deepEqual(val1, val2) {\n  if (val1 === val2) return true;\n\n  if (_typeof(val1) === 'object') {\n    for (var key in val1) {\n      if (!deepEqual(val1[key], val2[key])) {\n        return false;\n      }\n    }\n\n    return true;\n  }\n\n  return false;\n}\n\nvar VisibilityState =\n/*#__PURE__*/\nfunction () {\n  function VisibilityState(el, options, vnode) {\n    _classCallCheck(this, VisibilityState);\n\n    this.el = el;\n    this.observer = null;\n    this.frozen = false;\n    this.createObserver(options, vnode);\n  }\n\n  _createClass(VisibilityState, [{\n    key: \"createObserver\",\n    value: function createObserver(options, vnode) {\n      var _this = this;\n\n      if (this.observer) {\n        this.destroyObserver();\n      }\n\n      if (this.frozen) return;\n      this.options = processOptions(options);\n\n      this.callback = function (result, entry) {\n        _this.options.callback(result, entry);\n\n        if (result && _this.options.once) {\n          _this.frozen = true;\n\n          _this.destroyObserver();\n        }\n      }; // Throttle\n\n\n      if (this.callback && this.options.throttle) {\n        var _ref = this.options.throttleOptions || {},\n            _leading = _ref.leading;\n\n        this.callback = throttle(this.callback, this.options.throttle, {\n          leading: function leading(state) {\n            return _leading === 'both' || _leading === 'visible' && state || _leading === 'hidden' && !state;\n          }\n        });\n      }\n\n      this.oldResult = undefined;\n      this.observer = new IntersectionObserver(function (entries) {\n        var entry = entries[0];\n\n        if (entries.length > 1) {\n          var intersectingEntry = entries.find(function (e) {\n            return e.isIntersecting;\n          });\n\n          if (intersectingEntry) {\n            entry = intersectingEntry;\n          }\n        }\n\n        if (_this.callback) {\n          // Use isIntersecting if possible because browsers can report isIntersecting as true, but intersectionRatio as 0, when something very slowly enters the viewport.\n          var result = entry.isIntersecting && entry.intersectionRatio >= _this.threshold;\n          if (result === _this.oldResult) return;\n          _this.oldResult = result;\n\n          _this.callback(result, entry);\n        }\n      }, this.options.intersection); // Wait for the element to be in document\n\n      vnode.context.$nextTick(function () {\n        if (_this.observer) {\n          _this.observer.observe(_this.el);\n        }\n      });\n    }\n  }, {\n    key: \"destroyObserver\",\n    value: function destroyObserver() {\n      if (this.observer) {\n        this.observer.disconnect();\n        this.observer = null;\n      } // Cancel throttled call\n\n\n      if (this.callback && this.callback._clear) {\n        this.callback._clear();\n\n        this.callback = null;\n      }\n    }\n  }, {\n    key: \"threshold\",\n    get: function get() {\n      return this.options.intersection && this.options.intersection.threshold || 0;\n    }\n  }]);\n\n  return VisibilityState;\n}();\n\nfunction bind(el, _ref2, vnode) {\n  var value = _ref2.value;\n  if (!value) return;\n\n  if (typeof IntersectionObserver === 'undefined') {\n    console.warn('[vue-observe-visibility] IntersectionObserver API is not available in your browser. Please install this polyfill: https://github.com/w3c/IntersectionObserver/tree/master/polyfill');\n  } else {\n    var state = new VisibilityState(el, value, vnode);\n    el._vue_visibilityState = state;\n  }\n}\n\nfunction update(el, _ref3, vnode) {\n  var value = _ref3.value,\n      oldValue = _ref3.oldValue;\n  if (deepEqual(value, oldValue)) return;\n  var state = el._vue_visibilityState;\n\n  if (!value) {\n    unbind(el);\n    return;\n  }\n\n  if (state) {\n    state.createObserver(value, vnode);\n  } else {\n    bind(el, {\n      value: value\n    }, vnode);\n  }\n}\n\nfunction unbind(el) {\n  var state = el._vue_visibilityState;\n\n  if (state) {\n    state.destroyObserver();\n    delete el._vue_visibilityState;\n  }\n}\n\nvar ObserveVisibility = {\n  bind: bind,\n  update: update,\n  unbind: unbind\n};\n\nfunction install(Vue) {\n  Vue.directive('observe-visibility', ObserveVisibility);\n  /* -- Add more components here -- */\n}\n/* -- Plugin definition & Auto-install -- */\n\n/* You shouldn't have to modify the code below */\n// Plugin\n\nvar plugin = {\n  // eslint-disable-next-line no-undef\n  version: \"0.4.6\",\n  install: install\n};\n\nvar GlobalVue = null;\n\nif (typeof window !== 'undefined') {\n  GlobalVue = window.Vue;\n} else if (typeof global !== 'undefined') {\n  GlobalVue = global.Vue;\n}\n\nif (GlobalVue) {\n  GlobalVue.use(plugin);\n}\n\nexport default plugin;\nexport { ObserveVisibility, install };\n", "(function (root, factory) {\n  if (typeof define === \"function\" && define.amd) {\n    define([], factory);\n  } else if (typeof module === \"object\" && module.exports) {\n    module.exports = factory();\n  } else {\n    root.Scrollparent = factory();\n  }\n}(this, function () {\n  var regex = /(auto|scroll)/;\n\n  var parents = function (node, ps) {\n    if (node.parentNode === null) { return ps; }\n\n    return parents(node.parentNode, ps.concat([node]));\n  };\n\n  var style = function (node, prop) {\n    return getComputedStyle(node, null).getPropertyValue(prop);\n  };\n\n  var overflow = function (node) {\n    return style(node, \"overflow\") + style(node, \"overflow-y\") + style(node, \"overflow-x\");\n  };\n\n  var scroll = function (node) {\n   return regex.test(overflow(node));\n  };\n\n  var scrollParent = function (node) {\n    if (!(node instanceof HTMLElement || node instanceof SVGElement)) {\n      return ;\n    }\n\n    var ps = parents(node.parentNode, []);\n\n    for (var i = 0; i < ps.length; i += 1) {\n      if (scroll(ps[i])) {\n        return ps[i];\n      }\n    }\n\n    return document.scrollingElement || document.documentElement;\n  };\n\n  return scrollParent;\n}));\n", "export const props = {\n  items: {\n    type: Array,\n    required: true,\n  },\n\n  keyField: {\n    type: String,\n    default: 'id',\n  },\n\n  direction: {\n    type: String,\n    default: 'vertical',\n    validator: (value) => ['vertical', 'horizontal'].includes(value),\n  },\n\n  listTag: {\n    type: String,\n    default: 'div',\n  },\n\n  itemTag: {\n    type: String,\n    default: 'div',\n  },\n}\n\nexport function simpleArray () {\n  return this.items.length && typeof this.items[0] !== 'object'\n}\n", "export let supportsPassive = false\n\nif (typeof window !== 'undefined') {\n  supportsPassive = false\n  try {\n    var opts = Object.defineProperty({}, 'passive', {\n      get () {\n        supportsPassive = true\n      },\n    })\n    window.addEventListener('test', null, opts)\n  } catch (e) {}\n}\n", "<template>\n  <div\n    v-observe-visibility=\"handleVisibilityChange\"\n    class=\"vue-recycle-scroller\"\n    :class=\"{\n      ready,\n      'page-mode': pageMode,\n      [`direction-${direction}`]: true,\n    }\"\n    @scroll.passive=\"handleScroll\"\n  >\n    <div\n      v-if=\"$slots.before\"\n      ref=\"before\"\n      class=\"vue-recycle-scroller__slot\"\n    >\n      <slot\n        name=\"before\"\n      />\n    </div>\n\n    <component\n      :is=\"listTag\"\n      ref=\"wrapper\"\n      :style=\"{ [direction === 'vertical' ? 'minHeight' : 'minWidth']: totalSize + 'px' }\"\n      class=\"vue-recycle-scroller__item-wrapper\"\n      :class=\"listClass\"\n    >\n      <component\n        :is=\"itemTag\"\n        v-for=\"view of pool\"\n        :key=\"view.nr.id\"\n        :style=\"ready ? {\n          transform: `translate${direction === 'vertical' ? 'Y' : 'X'}(${view.position}px) translate${direction === 'vertical' ? 'X' : 'Y'}(${view.offset}px)`,\n          width: gridItems ? `${direction === 'vertical' ? itemSecondarySize || itemSize : itemSize}px` : undefined,\n          height: gridItems ? `${direction === 'horizontal' ? itemSecondarySize || itemSize : itemSize}px` : undefined,\n        } : null\"\n        class=\"vue-recycle-scroller__item-view\"\n        :class=\"[\n          itemClass,\n          {\n            hover: !skipHover && hoverKey === view.nr.key\n          },\n        ]\"\n        v-on=\"skipHover ? {} : {\n          mouseenter: () => { hoverKey = view.nr.key },\n          mouseleave: () => { hoverKey = null },\n        }\"\n      >\n        <slot\n          :item=\"view.item\"\n          :index=\"view.nr.index\"\n          :active=\"view.nr.used\"\n        />\n      </component>\n\n      <slot\n        name=\"empty\"\n      />\n    </component>\n\n    <div\n      v-if=\"$slots.after\"\n      ref=\"after\"\n      class=\"vue-recycle-scroller__slot\"\n    >\n      <slot\n        name=\"after\"\n      />\n    </div>\n\n    <ResizeObserver @notify=\"handleResize\" />\n  </div>\n</template>\n\n<script>\nimport { ResizeObserver } from 'vue-resize'\nimport { ObserveVisibility } from 'vue-observe-visibility'\nimport ScrollParent from 'scrollparent'\nimport config from '../config'\nimport { props, simpleArray } from './common'\nimport { supportsPassive } from '../utils'\n\nlet uid = 0\n\nexport default {\n  name: 'RecycleScroller',\n\n  components: {\n    ResizeObserver,\n  },\n\n  directives: {\n    ObserveVisibility,\n  },\n\n  props: {\n    ...props,\n\n    itemSize: {\n      type: Number,\n      default: null,\n    },\n\n    gridItems: {\n      type: Number,\n      default: undefined,\n    },\n\n    itemSecondarySize: {\n      type: Number,\n      default: undefined,\n    },\n\n    minItemSize: {\n      type: [Number, String],\n      default: null,\n    },\n\n    sizeField: {\n      type: String,\n      default: 'size',\n    },\n\n    typeField: {\n      type: String,\n      default: 'type',\n    },\n\n    buffer: {\n      type: Number,\n      default: 200,\n    },\n\n    pageMode: {\n      type: Boolean,\n      default: false,\n    },\n\n    prerender: {\n      type: Number,\n      default: 0,\n    },\n\n    emitUpdate: {\n      type: Boolean,\n      default: false,\n    },\n\n    skipHover: {\n      type: Boolean,\n      default: false,\n    },\n\n    listTag: {\n      type: String,\n      default: 'div',\n    },\n\n    itemTag: {\n      type: String,\n      default: 'div',\n    },\n\n    listClass: {\n      type: [String, Object, Array],\n      default: '',\n    },\n\n    itemClass: {\n      type: [String, Object, Array],\n      default: '',\n    },\n  },\n\n  data () {\n    return {\n      pool: [],\n      totalSize: 0,\n      ready: false,\n      hoverKey: null,\n    }\n  },\n\n  computed: {\n    sizes () {\n      if (this.itemSize === null) {\n        const sizes = {\n          '-1': { accumulator: 0 },\n        }\n        const items = this.items\n        const field = this.sizeField\n        const minItemSize = this.minItemSize\n        let computedMinSize = 10000\n        let accumulator = 0\n        let current\n        for (let i = 0, l = items.length; i < l; i++) {\n          current = items[i][field] || minItemSize\n          if (current < computedMinSize) {\n            computedMinSize = current\n          }\n          accumulator += current\n          sizes[i] = { accumulator, size: current }\n        }\n        // eslint-disable-next-line\n        this.$_computedMinItemSize = computedMinSize\n        return sizes\n      }\n      return []\n    },\n\n    simpleArray,\n  },\n\n  watch: {\n    items () {\n      this.updateVisibleItems(true)\n    },\n\n    pageMode () {\n      this.applyPageMode()\n      this.updateVisibleItems(false)\n    },\n\n    sizes: {\n      handler () {\n        this.updateVisibleItems(false)\n      },\n      deep: true,\n    },\n\n    gridItems () {\n      this.updateVisibleItems(true)\n    },\n\n    itemSecondarySize () {\n      this.updateVisibleItems(true)\n    },\n  },\n\n  created () {\n    this.$_startIndex = 0\n    this.$_endIndex = 0\n    this.$_views = new Map()\n    this.$_unusedViews = new Map()\n    this.$_scrollDirty = false\n    this.$_lastUpdateScrollPosition = 0\n\n    // In SSR mode, we also prerender the same number of item for the first render\n    // to avoir mismatch between server and client templates\n    if (this.prerender) {\n      this.$_prerender = true\n      this.updateVisibleItems(false)\n    }\n\n    if (this.gridItems && !this.itemSize) {\n      console.error('[vue-recycle-scroller] You must provide an itemSize when using gridItems')\n    }\n  },\n\n  mounted () {\n    this.applyPageMode()\n    this.$nextTick(() => {\n      // In SSR mode, render the real number of visible items\n      this.$_prerender = false\n      this.updateVisibleItems(true)\n      this.ready = true\n    })\n  },\n\n  activated () {\n    const lastPosition = this.$_lastUpdateScrollPosition\n    if (typeof lastPosition === 'number') {\n      this.$nextTick(() => {\n        this.scrollToPosition(lastPosition)\n      })\n    }\n  },\n\n  beforeDestroy () {\n    this.removeListeners()\n  },\n\n  methods: {\n    addView (pool, index, item, key, type) {\n      const view = {\n        item,\n        position: 0,\n      }\n      const nonReactive = {\n        id: uid++,\n        index,\n        used: true,\n        key,\n        type,\n      }\n      Object.defineProperty(view, 'nr', {\n        configurable: false,\n        value: nonReactive,\n      })\n      pool.push(view)\n      return view\n    },\n\n    unuseView (view, fake = false) {\n      const unusedViews = this.$_unusedViews\n      const type = view.nr.type\n      let unusedPool = unusedViews.get(type)\n      if (!unusedPool) {\n        unusedPool = []\n        unusedViews.set(type, unusedPool)\n      }\n      unusedPool.push(view)\n      if (!fake) {\n        view.nr.used = false\n        view.position = -9999\n        this.$_views.delete(view.nr.key)\n      }\n    },\n\n    handleResize () {\n      this.$emit('resize')\n      if (this.ready) this.updateVisibleItems(false)\n    },\n\n    handleScroll (event) {\n      if (!this.$_scrollDirty) {\n        this.$_scrollDirty = true\n        requestAnimationFrame(() => {\n          this.$_scrollDirty = false\n          const { continuous } = this.updateVisibleItems(false, true)\n\n          // It seems sometimes chrome doesn't fire scroll event :/\n          // When non continous scrolling is ending, we force a refresh\n          if (!continuous) {\n            clearTimeout(this.$_refreshTimout)\n            this.$_refreshTimout = setTimeout(this.handleScroll, 100)\n          }\n        })\n      }\n    },\n\n    handleVisibilityChange (isVisible, entry) {\n      if (this.ready) {\n        if (isVisible || entry.boundingClientRect.width !== 0 || entry.boundingClientRect.height !== 0) {\n          this.$emit('visible')\n          requestAnimationFrame(() => {\n            this.updateVisibleItems(false)\n          })\n        } else {\n          this.$emit('hidden')\n        }\n      }\n    },\n\n    updateVisibleItems (checkItem, checkPositionDiff = false) {\n      const itemSize = this.itemSize\n      const gridItems = this.gridItems || 1\n      const itemSecondarySize = this.itemSecondarySize || itemSize\n      const minItemSize = this.$_computedMinItemSize\n      const typeField = this.typeField\n      const keyField = this.simpleArray ? null : this.keyField\n      const items = this.items\n      const count = items.length\n      const sizes = this.sizes\n      const views = this.$_views\n      const unusedViews = this.$_unusedViews\n      const pool = this.pool\n      let startIndex, endIndex\n      let totalSize\n      let visibleStartIndex, visibleEndIndex\n\n      if (!count) {\n        startIndex = endIndex = visibleStartIndex = visibleEndIndex = totalSize = 0\n      } else if (this.$_prerender) {\n        startIndex = visibleStartIndex = 0\n        endIndex = visibleEndIndex = Math.min(this.prerender, items.length)\n        totalSize = null\n      } else {\n        const scroll = this.getScroll()\n\n        // Skip update if use hasn't scrolled enough\n        if (checkPositionDiff) {\n          let positionDiff = scroll.start - this.$_lastUpdateScrollPosition\n          if (positionDiff < 0) positionDiff = -positionDiff\n          if ((itemSize === null && positionDiff < minItemSize) || positionDiff < itemSize) {\n            return {\n              continuous: true,\n            }\n          }\n        }\n        this.$_lastUpdateScrollPosition = scroll.start\n\n        const buffer = this.buffer\n        scroll.start -= buffer\n        scroll.end += buffer\n\n        // account for leading slot\n        let beforeSize = 0\n        if (this.$refs.before) {\n          beforeSize = this.$refs.before.scrollHeight\n          scroll.start -= beforeSize\n        }\n\n        // account for trailing slot\n        if (this.$refs.after) {\n          const afterSize = this.$refs.after.scrollHeight\n          scroll.end += afterSize\n        }\n\n        // Variable size mode\n        if (itemSize === null) {\n          let h\n          let a = 0\n          let b = count - 1\n          let i = ~~(count / 2)\n          let oldI\n\n          // Searching for startIndex\n          do {\n            oldI = i\n            h = sizes[i].accumulator\n            if (h < scroll.start) {\n              a = i\n            } else if (i < count - 1 && sizes[i + 1].accumulator > scroll.start) {\n              b = i\n            }\n            i = ~~((a + b) / 2)\n          } while (i !== oldI)\n          i < 0 && (i = 0)\n          startIndex = i\n\n          // For container style\n          totalSize = sizes[count - 1].accumulator\n\n          // Searching for endIndex\n          for (endIndex = i; endIndex < count && sizes[endIndex].accumulator < scroll.end; endIndex++);\n          if (endIndex === -1) {\n            endIndex = items.length - 1\n          } else {\n            endIndex++\n            // Bounds\n            endIndex > count && (endIndex = count)\n          }\n\n          // search visible startIndex\n          for (visibleStartIndex = startIndex; visibleStartIndex < count && (beforeSize + sizes[visibleStartIndex].accumulator) < scroll.start; visibleStartIndex++);\n\n          // search visible endIndex\n          for (visibleEndIndex = visibleStartIndex; visibleEndIndex < count && (beforeSize + sizes[visibleEndIndex].accumulator) < scroll.end; visibleEndIndex++);\n        } else {\n          // Fixed size mode\n          startIndex = ~~(scroll.start / itemSize * gridItems)\n          const remainer = startIndex % gridItems\n          startIndex -= remainer\n          endIndex = Math.ceil(scroll.end / itemSize * gridItems)\n          visibleStartIndex = Math.max(0, Math.floor((scroll.start - beforeSize) / itemSize * gridItems))\n          visibleEndIndex = Math.floor((scroll.end - beforeSize) / itemSize * gridItems)\n\n          // Bounds\n          startIndex < 0 && (startIndex = 0)\n          endIndex > count && (endIndex = count)\n          visibleStartIndex < 0 && (visibleStartIndex = 0)\n          visibleEndIndex > count && (visibleEndIndex = count)\n\n          totalSize = Math.ceil(count / gridItems) * itemSize\n        }\n      }\n\n      if (endIndex - startIndex > config.itemsLimit) {\n        this.itemsLimitError()\n      }\n\n      this.totalSize = totalSize\n\n      let view\n\n      const continuous = startIndex <= this.$_endIndex && endIndex >= this.$_startIndex\n\n      if (this.$_continuous !== continuous) {\n        if (continuous) {\n          views.clear()\n          unusedViews.clear()\n          for (let i = 0, l = pool.length; i < l; i++) {\n            view = pool[i]\n            this.unuseView(view)\n          }\n        }\n        this.$_continuous = continuous\n      } else if (continuous) {\n        for (let i = 0, l = pool.length; i < l; i++) {\n          view = pool[i]\n          if (view.nr.used) {\n            // Update view item index\n            if (checkItem) {\n              view.nr.index = items.indexOf(view.item)\n            }\n\n            // Check if index is still in visible range\n            if (\n              view.nr.index === -1 ||\n              view.nr.index < startIndex ||\n              view.nr.index >= endIndex\n            ) {\n              this.unuseView(view)\n            }\n          }\n        }\n      }\n\n      const unusedIndex = continuous ? null : new Map()\n\n      let item, type, unusedPool\n      let v\n      for (let i = startIndex; i < endIndex; i++) {\n        item = items[i]\n        const key = keyField ? item[keyField] : item\n        if (key == null) {\n          throw new Error(`Key is ${key} on item (keyField is '${keyField}')`)\n        }\n        view = views.get(key)\n\n        if (!itemSize && !sizes[i].size) {\n          if (view) this.unuseView(view)\n          continue\n        }\n\n        // No view assigned to item\n        if (!view) {\n          if (i === items.length - 1) this.$emit('scroll-end')\n          if (i === 0) this.$emit('scroll-start')\n\n          type = item[typeField]\n          unusedPool = unusedViews.get(type)\n\n          if (continuous) {\n            // Reuse existing view\n            if (unusedPool && unusedPool.length) {\n              view = unusedPool.pop()\n              view.item = item\n              view.nr.used = true\n              view.nr.index = i\n              view.nr.key = key\n              view.nr.type = type\n            } else {\n              view = this.addView(pool, i, item, key, type)\n            }\n          } else {\n            // Use existing view\n            // We don't care if they are already used\n            // because we are not in continous scrolling\n            v = unusedIndex.get(type) || 0\n\n            if (!unusedPool || v >= unusedPool.length) {\n              view = this.addView(pool, i, item, key, type)\n              this.unuseView(view, true)\n              unusedPool = unusedViews.get(type)\n            }\n\n            view = unusedPool[v]\n            view.item = item\n            view.nr.used = true\n            view.nr.index = i\n            view.nr.key = key\n            view.nr.type = type\n            unusedIndex.set(type, v + 1)\n            v++\n          }\n          views.set(key, view)\n        } else {\n          view.nr.used = true\n          view.item = item\n        }\n\n        // Update position\n        if (itemSize === null) {\n          view.position = sizes[i - 1].accumulator\n          view.offset = 0\n        } else {\n          view.position = Math.floor(i / gridItems) * itemSize\n          view.offset = (i % gridItems) * itemSecondarySize\n        }\n      }\n\n      this.$_startIndex = startIndex\n      this.$_endIndex = endIndex\n\n      if (this.emitUpdate) this.$emit('update', startIndex, endIndex, visibleStartIndex, visibleEndIndex)\n\n      // After the user has finished scrolling\n      // Sort views so text selection is correct\n      clearTimeout(this.$_sortTimer)\n      this.$_sortTimer = setTimeout(this.sortViews, 300)\n\n      return {\n        continuous,\n      }\n    },\n\n    getListenerTarget () {\n      let target = ScrollParent(this.$el)\n      // Fix global scroll target for Chrome and Safari\n      if (window.document && (target === window.document.documentElement || target === window.document.body)) {\n        target = window\n      }\n      return target\n    },\n\n    getScroll () {\n      const { $el: el, direction } = this\n      const isVertical = direction === 'vertical'\n      let scrollState\n\n      if (this.pageMode) {\n        const bounds = el.getBoundingClientRect()\n        const boundsSize = isVertical ? bounds.height : bounds.width\n        let start = -(isVertical ? bounds.top : bounds.left)\n        let size = isVertical ? window.innerHeight : window.innerWidth\n        if (start < 0) {\n          size += start\n          start = 0\n        }\n        if (start + size > boundsSize) {\n          size = boundsSize - start\n        }\n        scrollState = {\n          start,\n          end: start + size,\n        }\n      } else if (isVertical) {\n        scrollState = {\n          start: el.scrollTop,\n          end: el.scrollTop + el.clientHeight,\n        }\n      } else {\n        scrollState = {\n          start: el.scrollLeft,\n          end: el.scrollLeft + el.clientWidth,\n        }\n      }\n\n      return scrollState\n    },\n\n    applyPageMode () {\n      if (this.pageMode) {\n        this.addListeners()\n      } else {\n        this.removeListeners()\n      }\n    },\n\n    addListeners () {\n      this.listenerTarget = this.getListenerTarget()\n      this.listenerTarget.addEventListener('scroll', this.handleScroll, supportsPassive\n        ? {\n            passive: true,\n          }\n        : false)\n      this.listenerTarget.addEventListener('resize', this.handleResize)\n    },\n\n    removeListeners () {\n      if (!this.listenerTarget) {\n        return\n      }\n\n      this.listenerTarget.removeEventListener('scroll', this.handleScroll)\n      this.listenerTarget.removeEventListener('resize', this.handleResize)\n\n      this.listenerTarget = null\n    },\n\n    scrollToItem (index) {\n      let scroll\n      if (this.itemSize === null) {\n        scroll = index > 0 ? this.sizes[index - 1].accumulator : 0\n      } else {\n        scroll = Math.floor(index / this.gridItems) * this.itemSize\n      }\n      this.scrollToPosition(scroll)\n    },\n\n    scrollToPosition (position) {\n      const direction = this.direction === 'vertical'\n        ? { scroll: 'scrollTop', start: 'top' }\n        : { scroll: 'scrollLeft', start: 'left' }\n\n      let viewport\n      let scrollDirection\n      let scrollDistance\n\n      if (this.pageMode) {\n        const viewportEl = ScrollParent(this.$el)\n        // HTML doesn't overflow like other elements\n        const scrollTop = viewportEl.tagName === 'HTML' ? 0 : viewportEl[direction.scroll]\n        const bounds = viewportEl.getBoundingClientRect()\n\n        const scroller = this.$el.getBoundingClientRect()\n        const scrollerPosition = scroller[direction.start] - bounds[direction.start]\n\n        viewport = viewportEl\n        scrollDirection = direction.scroll\n        scrollDistance = position + scrollTop + scrollerPosition\n      } else {\n        viewport = this.$el\n        scrollDirection = direction.scroll\n        scrollDistance = position\n      }\n\n      viewport[scrollDirection] = scrollDistance\n    },\n\n    itemsLimitError () {\n      setTimeout(() => {\n        console.log('It seems the scroller element isn\\'t scrolling, so it tries to render all the items at once.', 'Scroller:', this.$el)\n        console.log('Make sure the scroller has a fixed height (or width) and \\'overflow-y\\' (or \\'overflow-x\\') set to \\'auto\\' so it can scroll correctly and only render the items visible in the scroll viewport.')\n      })\n      throw new Error('Rendered items limit reached')\n    },\n\n    sortViews () {\n      this.pool.sort((viewA, viewB) => viewA.nr.index - viewB.nr.index)\n    },\n  },\n}\n</script>\n\n<style>\n.vue-recycle-scroller {\n  position: relative;\n}\n\n.vue-recycle-scroller.direction-vertical:not(.page-mode) {\n  overflow-y: auto;\n}\n\n.vue-recycle-scroller.direction-horizontal:not(.page-mode) {\n  overflow-x: auto;\n}\n\n.vue-recycle-scroller.direction-horizontal {\n  display: flex;\n}\n\n.vue-recycle-scroller__slot {\n  flex: auto 0 0;\n}\n\n.vue-recycle-scroller__item-wrapper {\n  flex: 1;\n  box-sizing: border-box;\n  overflow: hidden;\n  position: relative;\n}\n\n.vue-recycle-scroller.ready .vue-recycle-scroller__item-view {\n  position: absolute;\n  top: 0;\n  left: 0;\n  will-change: transform;\n}\n\n.vue-recycle-scroller.direction-vertical .vue-recycle-scroller__item-wrapper {\n  width: 100%;\n}\n\n.vue-recycle-scroller.direction-horizontal .vue-recycle-scroller__item-wrapper {\n  height: 100%;\n}\n\n.vue-recycle-scroller.ready.direction-vertical .vue-recycle-scroller__item-view {\n  width: 100%;\n}\n\n.vue-recycle-scroller.ready.direction-horizontal .vue-recycle-scroller__item-view {\n  height: 100%;\n}\n</style>\n", "<template>\n  <RecycleScroller\n    ref=\"scroller\"\n    :items=\"itemsWithSize\"\n    :min-item-size=\"minItemSize\"\n    :direction=\"direction\"\n    key-field=\"id\"\n    :list-tag=\"listTag\"\n    :item-tag=\"itemTag\"\n    v-bind=\"$attrs\"\n    @resize=\"onScrollerResize\"\n    @visible=\"onScrollerVisible\"\n    v-on=\"listeners\"\n  >\n    <template slot-scope=\"{ item: itemWithSize, index, active }\">\n      <slot\n        v-bind=\"{\n          item: itemWithSize.item,\n          index,\n          active,\n          itemWithSize\n        }\"\n      />\n    </template>\n    <template slot=\"before\">\n      <slot name=\"before\" />\n    </template>\n    <template slot=\"after\">\n      <slot name=\"after\" />\n    </template>\n    <template slot=\"empty\">\n      <slot name=\"empty\" />\n    </template>\n  </RecycleScroller>\n</template>\n\n<script>\nimport RecycleScroller from './RecycleScroller.vue'\nimport { props, simpleArray } from './common'\n\nexport default {\n  name: 'DynamicScroller',\n\n  components: {\n    RecycleScroller,\n  },\n\n  provide () {\n    if (typeof ResizeObserver !== 'undefined') {\n      this.$_resizeObserver = new ResizeObserver(entries => {\n        requestAnimationFrame(() => {\n          if (!Array.isArray(entries)) {\n            return\n          }\n          for (const entry of entries) {\n            if (entry.target) {\n              const event = new CustomEvent(\n                'resize',\n                {\n                  detail: {\n                    contentRect: entry.contentRect,\n                  },\n                },\n              )\n              entry.target.dispatchEvent(event)\n            }\n          }\n        })\n      })\n    }\n\n    return {\n      vscrollData: this.vscrollData,\n      vscrollParent: this,\n      vscrollResizeObserver: this.$_resizeObserver,\n    }\n  },\n\n  inheritAttrs: false,\n\n  props: {\n    ...props,\n\n    minItemSize: {\n      type: [Number, String],\n      required: true,\n    },\n  },\n\n  data () {\n    return {\n      vscrollData: {\n        active: true,\n        sizes: {},\n        validSizes: {},\n        keyField: this.keyField,\n        simpleArray: false,\n      },\n    }\n  },\n\n  computed: {\n    simpleArray,\n\n    itemsWithSize () {\n      const result = []\n      const { items, keyField, simpleArray } = this\n      const sizes = this.vscrollData.sizes\n      const l = items.length\n      for (let i = 0; i < l; i++) {\n        const item = items[i]\n        const id = simpleArray ? i : item[keyField]\n        let size = sizes[id]\n        if (typeof size === 'undefined' && !this.$_undefinedMap[id]) {\n          size = 0\n        }\n        result.push({\n          item,\n          id,\n          size,\n        })\n      }\n      return result\n    },\n\n    listeners () {\n      const listeners = {}\n      for (const key in this.$listeners) {\n        if (key !== 'resize' && key !== 'visible') {\n          listeners[key] = this.$listeners[key]\n        }\n      }\n      return listeners\n    },\n  },\n\n  watch: {\n    items () {\n      this.forceUpdate(false)\n    },\n\n    simpleArray: {\n      handler (value) {\n        this.vscrollData.simpleArray = value\n      },\n      immediate: true,\n    },\n\n    direction (value) {\n      this.forceUpdate(true)\n    },\n\n    itemsWithSize (next, prev) {\n      const scrollTop = this.$el.scrollTop\n\n      // Calculate total diff between prev and next sizes\n      // over current scroll top. Then add it to scrollTop to\n      // avoid jumping the contents that the user is seeing.\n      let prevActiveTop = 0; let activeTop = 0\n      const length = Math.min(next.length, prev.length)\n      for (let i = 0; i < length; i++) {\n        if (prevActiveTop >= scrollTop) {\n          break\n        }\n        prevActiveTop += prev[i].size || this.minItemSize\n        activeTop += next[i].size || this.minItemSize\n      }\n      const offset = activeTop - prevActiveTop\n\n      if (offset === 0) {\n        return\n      }\n\n      this.$el.scrollTop += offset\n    },\n  },\n\n  beforeCreate () {\n    this.$_updates = []\n    this.$_undefinedSizes = 0\n    this.$_undefinedMap = {}\n  },\n\n  activated () {\n    this.vscrollData.active = true\n  },\n\n  deactivated () {\n    this.vscrollData.active = false\n  },\n\n  methods: {\n    onScrollerResize () {\n      const scroller = this.$refs.scroller\n      if (scroller) {\n        this.forceUpdate()\n      }\n      this.$emit('resize')\n    },\n\n    onScrollerVisible () {\n      this.$emit('vscroll:update', { force: false })\n      this.$emit('visible')\n    },\n\n    forceUpdate (clear = true) {\n      if (clear || this.simpleArray) {\n        this.vscrollData.validSizes = {}\n      }\n      this.$emit('vscroll:update', { force: true })\n    },\n\n    scrollToItem (index) {\n      const scroller = this.$refs.scroller\n      if (scroller) scroller.scrollToItem(index)\n    },\n\n    getItemSize (item, index = undefined) {\n      const id = this.simpleArray ? (index != null ? index : this.items.indexOf(item)) : item[this.keyField]\n      return this.vscrollData.sizes[id] || 0\n    },\n\n    scrollToBottom () {\n      if (this.$_scrollingToBottom) return\n      this.$_scrollingToBottom = true\n      const el = this.$el\n      // Item is inserted to the DOM\n      this.$nextTick(() => {\n        el.scrollTop = el.scrollHeight + 5000\n        // Item sizes are computed\n        const cb = () => {\n          el.scrollTop = el.scrollHeight + 5000\n          requestAnimationFrame(() => {\n            el.scrollTop = el.scrollHeight + 5000\n            if (this.$_undefinedSizes === 0) {\n              this.$_scrollingToBottom = false\n            } else {\n              requestAnimationFrame(cb)\n            }\n          })\n        }\n        requestAnimationFrame(cb)\n      })\n    },\n  },\n}\n</script>\n", "<script>\nexport default {\n  name: 'DynamicScrollerItem',\n\n  inject: [\n    'vscrollData',\n    'vscrollParent',\n    'vscrollResizeObserver',\n  ],\n\n  props: {\n    // eslint-disable-next-line vue/require-prop-types\n    item: {\n      required: true,\n    },\n\n    watchData: {\n      type: Boolean,\n      default: false,\n    },\n\n    /**\n     * Indicates if the view is actively used to display an item.\n     */\n    active: {\n      type: Boolean,\n      required: true,\n    },\n\n    index: {\n      type: Number,\n      default: undefined,\n    },\n\n    sizeDependencies: {\n      type: [Array, Object],\n      default: null,\n    },\n\n    emitResize: {\n      type: Boolean,\n      default: false,\n    },\n\n    tag: {\n      type: String,\n      default: 'div',\n    },\n  },\n\n  computed: {\n    id () {\n      if (this.vscrollData.simpleArray) return this.index\n      // eslint-disable-next-line no-prototype-builtins\n      if (this.item.hasOwnProperty(this.vscrollData.keyField)) return this.item[this.vscrollData.keyField]\n      throw new Error(`keyField '${this.vscrollData.keyField}' not found in your item. You should set a valid keyField prop on your Scroller`)\n    },\n\n    size () {\n      return (this.vscrollData.validSizes[this.id] && this.vscrollData.sizes[this.id]) || 0\n    },\n\n    finalActive () {\n      return this.active && this.vscrollData.active\n    },\n  },\n\n  watch: {\n    watchData: 'updateWatchData',\n\n    id () {\n      if (!this.size) {\n        this.onDataUpdate()\n      }\n    },\n\n    finalActive (value) {\n      if (!this.size) {\n        if (value) {\n          if (!this.vscrollParent.$_undefinedMap[this.id]) {\n            this.vscrollParent.$_undefinedSizes++\n            this.vscrollParent.$_undefinedMap[this.id] = true\n          }\n        } else {\n          if (this.vscrollParent.$_undefinedMap[this.id]) {\n            this.vscrollParent.$_undefinedSizes--\n            this.vscrollParent.$_undefinedMap[this.id] = false\n          }\n        }\n      }\n\n      if (this.vscrollResizeObserver) {\n        if (value) {\n          this.observeSize()\n        } else {\n          this.unobserveSize()\n        }\n      } else if (value && this.$_pendingVScrollUpdate === this.id) {\n        this.updateSize()\n      }\n    },\n  },\n\n  created () {\n    if (this.$isServer) return\n\n    this.$_forceNextVScrollUpdate = null\n    this.updateWatchData()\n\n    if (!this.vscrollResizeObserver) {\n      for (const k in this.sizeDependencies) {\n        this.$watch(() => this.sizeDependencies[k], this.onDataUpdate)\n      }\n\n      this.vscrollParent.$on('vscroll:update', this.onVscrollUpdate)\n      this.vscrollParent.$on('vscroll:update-size', this.onVscrollUpdateSize)\n    }\n  },\n\n  mounted () {\n    if (this.vscrollData.active) {\n      this.updateSize()\n      this.observeSize()\n    }\n  },\n\n  beforeDestroy () {\n    this.vscrollParent.$off('vscroll:update', this.onVscrollUpdate)\n    this.vscrollParent.$off('vscroll:update-size', this.onVscrollUpdateSize)\n    this.unobserveSize()\n  },\n\n  methods: {\n    updateSize () {\n      if (this.finalActive) {\n        if (this.$_pendingSizeUpdate !== this.id) {\n          this.$_pendingSizeUpdate = this.id\n          this.$_forceNextVScrollUpdate = null\n          this.$_pendingVScrollUpdate = null\n          this.computeSize(this.id)\n        }\n      } else {\n        this.$_forceNextVScrollUpdate = this.id\n      }\n    },\n\n    updateWatchData () {\n      if (this.watchData && !this.vscrollResizeObserver) {\n        this.$_watchData = this.$watch('item', () => {\n          this.onDataUpdate()\n        }, {\n          deep: true,\n        })\n      } else if (this.$_watchData) {\n        this.$_watchData()\n        this.$_watchData = null\n      }\n    },\n\n    onVscrollUpdate ({ force }) {\n      // If not active, sechedule a size update when it becomes active\n      if (!this.finalActive && force) {\n        this.$_pendingVScrollUpdate = this.id\n      }\n\n      if (this.$_forceNextVScrollUpdate === this.id || force || !this.size) {\n        this.updateSize()\n      }\n    },\n\n    onDataUpdate () {\n      this.updateSize()\n    },\n\n    computeSize (id) {\n      this.$nextTick(() => {\n        if (this.id === id) {\n          const width = this.$el.offsetWidth\n          const height = this.$el.offsetHeight\n          this.applySize(width, height)\n        }\n        this.$_pendingSizeUpdate = null\n      })\n    },\n\n    applySize (width, height) {\n      const size = ~~(this.vscrollParent.direction === 'vertical' ? height : width)\n      if (size && this.size !== size) {\n        if (this.vscrollParent.$_undefinedMap[this.id]) {\n          this.vscrollParent.$_undefinedSizes--\n          this.vscrollParent.$_undefinedMap[this.id] = undefined\n        }\n        this.$set(this.vscrollData.sizes, this.id, size)\n        this.$set(this.vscrollData.validSizes, this.id, true)\n        if (this.emitResize) this.$emit('resize', this.id)\n      }\n    },\n\n    observeSize () {\n      if (!this.vscrollResizeObserver || !this.$el.parentNode) return\n      this.vscrollResizeObserver.observe(this.$el.parentNode)\n      this.$el.parentNode.addEventListener('resize', this.onResize)\n    },\n\n    unobserveSize () {\n      if (!this.vscrollResizeObserver) return\n      this.vscrollResizeObserver.unobserve(this.$el.parentNode)\n      this.$el.parentNode.removeEventListener('resize', this.onResize)\n    },\n\n    onResize (event) {\n      const { width, height } = event.detail.contentRect\n      this.applySize(width, height)\n    },\n  },\n\n  render (h) {\n    return h(this.tag, this.$slots.default)\n  },\n}\n</script>\n", "import config from './config'\n\nimport RecycleScroller from './components/RecycleScroller.vue'\nimport DynamicScroller from './components/DynamicScroller.vue'\nimport DynamicScrollerItem from './components/DynamicScrollerItem.vue'\n\nexport { default as IdState } from './mixins/IdState'\n\nexport {\n  RecycleScroller,\n  DynamicScroller,\n  DynamicScrollerItem,\n}\n\nfunction registerComponents (Vue, prefix) {\n  Vue.component(`${prefix}recycle-scroller`, RecycleScroller)\n  Vue.component(`${prefix}RecycleScroller`, RecycleScroller)\n  Vue.component(`${prefix}dynamic-scroller`, DynamicScroller)\n  Vue.component(`${prefix}DynamicScroller`, DynamicScroller)\n  Vue.component(`${prefix}dynamic-scroller-item`, DynamicScrollerItem)\n  Vue.component(`${prefix}DynamicScrollerItem`, DynamicScrollerItem)\n}\n\nconst plugin = {\n  // eslint-disable-next-line no-undef\n  version: VERSION,\n  install (Vue, options) {\n    const finalOptions = Object.assign({}, {\n      installComponents: true,\n      componentsPrefix: '',\n    }, options)\n\n    for (const key in finalOptions) {\n      if (typeof finalOptions[key] !== 'undefined') {\n        config[key] = finalOptions[key]\n      }\n    }\n\n    if (finalOptions.installComponents) {\n      registerComponents(Vue, finalOptions.componentsPrefix)\n    }\n  },\n}\n\nexport default plugin\n\n// Auto-install\nlet GlobalVue = null\nif (typeof window !== 'undefined') {\n  GlobalVue = window.Vue\n} else if (typeof global !== 'undefined') {\n  GlobalVue = global.Vue\n}\nif (GlobalVue) {\n  GlobalVue.use(plugin)\n}\n", "import Vue from 'vue'\n\nexport default function ({\n  idProp = vm => vm.item.id,\n} = {}) {\n  const store = {}\n  const vm = new Vue({\n    data () {\n      return {\n        store,\n      }\n    },\n  })\n\n  // @vue/component\n  return {\n    data () {\n      return {\n        idState: null,\n      }\n    },\n\n    created () {\n      this.$_id = null\n      if (typeof idProp === 'function') {\n        this.$_getId = () => idProp.call(this, this)\n      } else {\n        this.$_getId = () => this[idProp]\n      }\n      this.$watch(this.$_getId, {\n        handler (value) {\n          this.$nextTick(() => {\n            this.$_id = value\n          })\n        },\n        immediate: true,\n      })\n      this.$_updateIdState()\n    },\n\n    beforeUpdate () {\n      this.$_updateIdState()\n    },\n\n    methods: {\n      /**\n       * Initialize an idState\n       * @param {number|string} id Unique id for the data\n       */\n      $_idStateInit (id) {\n        const factory = this.$options.idState\n        if (typeof factory === 'function') {\n          const data = factory.call(this, this)\n          vm.$set(store, id, data)\n          this.$_id = id\n          return data\n        } else {\n          throw new Error('[mixin IdState] Missing `idState` function on component definition.')\n        }\n      },\n\n      /**\n       * Ensure idState is created and up-to-date\n       */\n      $_updateIdState () {\n        const id = this.$_getId()\n        if (id == null) {\n          console.warn(`No id found for IdState with idProp: '${idProp}'.`)\n        }\n        if (id !== this.$_id) {\n          if (!store[id]) {\n            this.$_idStateInit(id)\n          }\n          this.idState = store[id]\n        }\n      },\n    },\n  }\n}\n"], "names": ["config", "itemsLimit", "isIE", "initCompat", "init", "ua", "window", "navigator", "userAgent", "msie", "indexOf", "parseInt", "substring", "rv", "edge", "getInternetExplorerVersion", "ResizeObserver", "render", "_h", "this", "$createElement", "_self", "_c", "staticClass", "attrs", "tabindex", "staticRenderFns", "_scopeId", "name", "methods", "compareAndNotify", "_w", "$el", "offsetWidth", "offsetHeight", "$emit", "addResizeHandlers", "_resizeObject", "contentDocument", "defaultView", "addEventListener", "removeResizeHandlers", "onload", "removeEventListener", "mounted", "_this", "$nextTick", "object", "document", "createElement", "setAttribute", "type", "append<PERSON><PERSON><PERSON>", "data", "<PERSON><PERSON><PERSON><PERSON>", "plugin", "version", "install", "<PERSON><PERSON>", "component", "GlobalVue", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_toConsumableArray", "arr", "Array", "isArray", "arr2", "_arrayWithoutHoles", "iter", "toString", "call", "from", "_iterableToArray", "TypeError", "_nonIterableSpread", "global", "use", "VisibilityState", "el", "options", "vnode", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_classCallCheck", "observer", "frozen", "createObserver", "protoProps", "staticProps", "value", "destroyObserver", "callback", "result", "entry", "once", "throttle", "_leading", "throttleOptions", "leading", "delay", "timeout", "lastState", "currentArgs", "arguments", "undefined", "throttled", "state", "_len", "args", "_key", "apply", "concat", "clearTimeout", "setTimeout", "_clear", "oldResult", "IntersectionObserver", "entries", "intersectingEntry", "find", "e", "isIntersecting", "intersectionRatio", "threshold", "intersection", "context", "observe", "disconnect", "get", "bind", "_ref2", "console", "warn", "_vue_visibilityState", "unbind", "ObserveVisibility", "update", "_ref3", "deepEqual", "val1", "val2", "oldValue", "directive", "root", "factory", "regex", "parents", "node", "ps", "parentNode", "style", "prop", "getComputedStyle", "getPropertyValue", "scroll", "test", "overflow", "HTMLElement", "SVGElement", "scrollingElement", "documentElement", "module", "exports", "Scrollparent", "items", "required", "keyField", "String", "default", "direction", "validator", "includes", "listTag", "itemTag", "simpleArray", "supportsPassive", "opts", "uid", "__vue_script__", "components", "directives", "itemSize", "Number", "gridItems", "itemSecondarySize", "minItemSize", "sizeField", "typeField", "buffer", "pageMode", "Boolean", "prerender", "emitUpdate", "skipHover", "listClass", "itemClass", "pool", "totalSize", "ready", "hoverKey", "computed", "sizes", "-1", "accumulator", "field", "current", "computedMinSize", "l", "size", "$_computedMinItemSize", "watch", "updateVisibleItems", "applyPageMode", "handler", "deep", "created", "$_startIndex", "$_endIndex", "$_views", "Map", "$_unusedViews", "$_scrollDirty", "$_lastUpdateScrollPosition", "$_prerender", "error", "activated", "lastPosition", "scrollToPosition", "removeListeners", "add<PERSON><PERSON><PERSON>", "index", "item", "view", "position", "nonReactive", "id", "used", "push", "unuseView", "fake", "unusedViews", "nr", "unusedPool", "set", "delete", "handleResize", "handleScroll", "event", "requestAnimationFrame", "continuous", "$_refreshTimout", "handleVisibilityChange", "isVisible", "boundingClientRect", "width", "height", "checkItem", "checkPositionDiff", "count", "views", "startIndex", "endIndex", "visibleStartIndex", "visibleEndIndex", "Math", "min", "getScroll", "positionDiff", "start", "end", "beforeSize", "$refs", "before", "scrollHeight", "after", "afterSize", "h", "oldI", "a", "b", "ceil", "max", "floor", "itemsLimitError", "$_continuous", "clear", "unusedIndex", "v", "Error", "pop", "offset", "$_sortTimer", "sortViews", "getListenerTarget", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "body", "isVertical", "scrollState", "bounds", "getBoundingClientRect", "boundsSize", "top", "left", "innerHeight", "innerWidth", "scrollTop", "clientHeight", "scrollLeft", "clientWidth", "addListeners", "<PERSON><PERSON><PERSON><PERSON>", "passive", "scrollToItem", "viewport", "scrollDirection", "scrollDistance", "viewportEl", "tagName", "scrollerPosition", "log", "sort", "viewA", "viewB", "RecycleScroller", "provide", "$_resizeObserver", "CustomEvent", "detail", "contentRect", "dispatchEvent", "vscrollData", "vscrollParent", "vscrollResizeObserver", "inheritAttrs", "active", "validSizes", "itemsWithSize", "$_undefinedMap", "listeners", "$listeners", "forceUpdate", "immediate", "next", "prev", "prevActiveTop", "activeTop", "beforeCreate", "$_updates", "$_undefinedSizes", "deactivated", "onScrollerResize", "scroller", "onScrollerVisible", "force", "getItemSize", "scrollToBottom", "$_scrollingToBottom", "cb", "inject", "watchData", "sizeDependencies", "emitResize", "tag", "hasOwnProperty", "finalActive", "onDataUpdate", "observeSize", "unobserveSize", "$_pendingVScrollUpdate", "updateSize", "$isServer", "$_forceNextVScrollUpdate", "updateWatchData", "k", "$watch", "$on", "onVscrollUpdate", "onVscrollUpdateSize", "$off", "$_pendingSizeUpdate", "computeSize", "$_watchData", "applySize", "$set", "onResize", "unobserve", "$slots", "VERSION", "finalOptions", "assign", "installComponents", "componentsPrefix", "prefix", "DynamicScroller", "DynamicScrollerItem", "registerComponents", "idProp", "vm", "store", "idState", "$_id", "$_getId", "$_updateIdState", "beforeUpdate", "$_idStateInit", "$options"], "mappings": "qIAAeA,EAAA,CACbC,WAAY,KCyBd,IAAIC,OAAO,EAEX,SAASC,IACHA,EAAWC,OACfD,EAAWC,MAAO,EAClBF,GAAyC,IA/B3C,WACC,IAAIG,EAAKC,OAAOC,UAAUC,UAEtBC,EAAOJ,EAAGK,QAAQ,SACtB,GAAID,EAAO,EAEV,OAAOE,SAASN,EAAGO,UAAUH,EAAO,EAAGJ,EAAGK,QAAQ,IAAKD,IAAQ,IAIhE,GADcJ,EAAGK,QAAQ,YACX,EAAG,CAEhB,IAAIG,EAAKR,EAAGK,QAAQ,OACpB,OAAOC,SAASN,EAAGO,UAAUC,EAAK,EAAGR,EAAGK,QAAQ,IAAKG,IAAM,IAG5D,IAAIC,EAAOT,EAAGK,QAAQ,SACtB,OAAII,EAAO,EAEHH,SAASN,EAAGO,UAAUE,EAAO,EAAGT,EAAGK,QAAQ,IAAKI,IAAQ,KAIxD,EAQAC,IAIT,IAAIC,EAAiB,CAAEC,OAAQ,WAC7B,IAAmBC,EAATC,KAAkBC,eAA2C,OAA7DD,KAA8CE,MAAMC,IAAMJ,GAAa,MAAO,CAAEK,YAAa,kBAAmBC,MAAO,CAAEC,SAAY,SAC7IC,gBAAiB,GAAIC,SAAU,kBAClCC,KAAM,kBAENC,QAAS,CACRC,iBAAkB,WACbX,KAAKY,KAAOZ,KAAKa,IAAIC,aAAed,KAAKD,KAAOC,KAAKa,IAAIE,eAC5Df,KAAKY,GAAKZ,KAAKa,IAAIC,YACnBd,KAAKD,GAAKC,KAAKa,IAAIE,aACnBf,KAAKgB,MAAM,YAGbC,kBAAmB,WAClBjB,KAAKkB,cAAcC,gBAAgBC,YAAYC,iBAAiB,SAAUrB,KAAKW,kBAC/EX,KAAKW,oBAENW,qBAAsB,WACjBtB,KAAKkB,eAAiBlB,KAAKkB,cAAcK,UACvCxC,GAAQiB,KAAKkB,cAAcC,iBAC/BnB,KAAKkB,cAAcC,gBAAgBC,YAAYI,oBAAoB,SAAUxB,KAAKW,yBAE5EX,KAAKkB,cAAcK,UAK7BE,QAAS,WACR,IAAIC,EAAQ1B,KAEZhB,IACAgB,KAAK2B,WAAU,WACdD,EAAMd,GAAKc,EAAMb,IAAIC,YACrBY,EAAM3B,GAAK2B,EAAMb,IAAIE,gBAEtB,IAAIa,EAASC,SAASC,cAAc,UACpC9B,KAAKkB,cAAgBU,EACrBA,EAAOG,aAAa,cAAe,QACnCH,EAAOG,aAAa,YAAa,GACjCH,EAAOL,OAASvB,KAAKiB,kBACrBW,EAAOI,KAAO,YACVjD,GACHiB,KAAKa,IAAIoB,YAAYL,GAEtBA,EAAOM,KAAO,cACTnD,GACJiB,KAAKa,IAAIoB,YAAYL,IAGvBO,cAAe,WACdnC,KAAKsB,yBAWP,IAAIc,EAAS,CAEZC,QAAS,QACTC,QATD,SAAiBC,GAChBA,EAAIC,UAAU,kBAAmB3C,GACjC0C,EAAIC,UAAU,iBAAkB3C,KAW7B4C,EAAY,KCvGhB,SAASC,EAAQC,GAWf,OATED,EADoB,mBAAXE,QAAoD,iBAApBA,OAAOC,SACtC,SAAUF,GAClB,cAAcA,GAGN,SAAUA,GAClB,OAAOA,GAAyB,mBAAXC,QAAyBD,EAAIG,cAAgBF,QAAUD,IAAQC,OAAOG,UAAY,gBAAkBJ,IAI9GA,GASjB,SAASK,EAAkBC,EAAQC,GACjC,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAME,OAAQD,IAAK,CACrC,IAAIE,EAAaH,EAAMC,GACvBE,EAAWC,WAAaD,EAAWC,aAAc,EACjDD,EAAWE,cAAe,EACtB,UAAWF,IAAYA,EAAWG,UAAW,GACjDC,OAAOC,eAAeT,EAAQI,EAAWM,IAAKN,IAUlD,SAASO,EAAmBC,GAC1B,OAGF,SAA4BA,GAC1B,GAAIC,MAAMC,QAAQF,GAAM,CACtB,IAAK,IAAIV,EAAI,EAAGa,EAAO,IAAIF,MAAMD,EAAIT,QAASD,EAAIU,EAAIT,OAAQD,IAAKa,EAAKb,GAAKU,EAAIV,GAEjF,OAAOa,GAPFC,CAAmBJ,IAW5B,SAA0BK,GACxB,GAAItB,OAAOC,YAAYY,OAAOS,IAAkD,uBAAzCT,OAAOV,UAAUoB,SAASC,KAAKF,GAAgC,OAAOJ,MAAMO,KAAKH,GAZtFI,CAAiBT,IAerD,WACE,MAAM,IAAIU,UAAU,mDAhBuCC,GDmEvC,oBAAXrF,OACVsD,EAAYtD,OAAOoD,IACS,oBAAXkC,SACjBhC,EAAYgC,OAAOlC,KAEhBE,GACHA,EAAUiC,IAAItC,GCef,IAAIuC,EAEJ,WACE,SAASA,EAAgBC,EAAIC,EAASC,IAlHxC,SAAyBC,EAAUC,GACjC,KAAMD,aAAoBC,GACxB,MAAM,IAAIT,UAAU,qCAiHpBU,CAAgBjF,KAAM2E,GAEtB3E,KAAK4E,GAAKA,EACV5E,KAAKkF,SAAW,KAChBlF,KAAKmF,QAAS,EACdnF,KAAKoF,eAAeP,EAASC,GAxGjC,IAAsBE,EAAaK,EAAYC,EAiM7C,OAjMoBN,EA2GPL,GA3GoBU,EA2GH,CAAC,CAC7B1B,IAAK,iBACL4B,MAAO,SAAwBV,EAASC,GACtC,IAAIpD,EAAQ1B,KAMZ,GAJIA,KAAKkF,UACPlF,KAAKwF,mBAGHxF,KAAKmF,OAAT,CA1FN,IAAwBI,EAwGlB,GAbAvF,KAAK6E,QAxFY,mBAHCU,EA2FYV,GAtFtB,CACRY,SAAUF,GAIFA,EAmFRvF,KAAKyF,SAAW,SAAUC,EAAQC,GAChCjE,EAAMmD,QAAQY,SAASC,EAAQC,GAE3BD,GAAUhE,EAAMmD,QAAQe,OAC1BlE,EAAMyD,QAAS,EAEfzD,EAAM8D,oBAKNxF,KAAKyF,UAAYzF,KAAK6E,QAAQgB,SAAU,CAC1C,IACIC,GADO9F,KAAK6E,QAAQkB,iBAAmB,IACvBC,QAEpBhG,KAAKyF,SA7Fb,SAAkBA,EAAUQ,GAC1B,IACIC,EACAC,EACAC,EAHAvB,EAAUwB,UAAUjD,OAAS,QAAsBkD,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,GAK9EE,EAAY,SAAmBC,GACjC,IAAK,IAAIC,EAAOJ,UAAUjD,OAAQsD,EAAO,IAAI5C,MAAM2C,EAAO,EAAIA,EAAO,EAAI,GAAIE,EAAO,EAAGA,EAAOF,EAAME,IAClGD,EAAKC,EAAO,GAAKN,UAAUM,GAI7B,GADAP,EAAcM,GACVR,GAAWM,IAAUL,EAAzB,CACA,IAAIH,EAAUnB,EAAQmB,QAEC,mBAAZA,IACTA,EAAUA,EAAQQ,EAAOL,IAGrBD,GAAWM,IAAUL,IAAcH,GACvCP,EAASmB,WAAM,EAAQ,CAACJ,GAAOK,OAAOjD,EAAmBwC,KAG3DD,EAAYK,EACZM,aAAaZ,GACbA,EAAUa,YAAW,WACnBtB,EAASmB,WAAM,EAAQ,CAACJ,GAAOK,OAAOjD,EAAmBwC,KACzDF,EAAU,IACTD,KAQL,OALAM,EAAUS,OAAS,WACjBF,aAAaZ,GACbA,EAAU,MAGLK,EAyDeV,CAAS7F,KAAKyF,SAAUzF,KAAK6E,QAAQgB,SAAU,CAC7DG,QAAS,SAAiBQ,GACxB,MAAoB,SAAbV,GAAoC,YAAbA,GAA0BU,GAAsB,WAAbV,IAA0BU,KAKjGxG,KAAKiH,eAAYX,EACjBtG,KAAKkF,SAAW,IAAIgC,sBAAqB,SAAUC,GACjD,IAAIxB,EAAQwB,EAAQ,GAEpB,GAAIA,EAAQ/D,OAAS,EAAG,CACtB,IAAIgE,EAAoBD,EAAQE,MAAK,SAAUC,GAC7C,OAAOA,EAAEC,kBAGPH,IACFzB,EAAQyB,GAIZ,GAAI1F,EAAM+D,SAAU,CAElB,IAAIC,EAASC,EAAM4B,gBAAkB5B,EAAM6B,mBAAqB9F,EAAM+F,UACtE,GAAI/B,IAAWhE,EAAMuF,UAAW,OAChCvF,EAAMuF,UAAYvB,EAElBhE,EAAM+D,SAASC,EAAQC,MAExB3F,KAAK6E,QAAQ6C,cAEhB5C,EAAM6C,QAAQhG,WAAU,WAClBD,EAAMwD,UACRxD,EAAMwD,SAAS0C,QAAQlG,EAAMkD,UAIlC,CACDjB,IAAK,kBACL4B,MAAO,WACDvF,KAAKkF,WACPlF,KAAKkF,SAAS2C,aACd7H,KAAKkF,SAAW,MAIdlF,KAAKyF,UAAYzF,KAAKyF,SAASuB,SACjChH,KAAKyF,SAASuB,SAEdhH,KAAKyF,SAAW,QAGnB,CACD9B,IAAK,YACLmE,IAAK,WACH,OAAO9H,KAAK6E,QAAQ6C,cAAgB1H,KAAK6E,QAAQ6C,aAAaD,WAAa,OA5L/DzE,EAAkBgC,EAAYjC,UAAWsC,GACrDC,GAAatC,EAAkBgC,EAAaM,GA+LzCX,EAhGT,GAmGA,SAASoD,EAAKnD,EAAIoD,EAAOlD,GACvB,IAAIS,EAAQyC,EAAMzC,MAClB,GAAKA,EAEL,GAAoC,oBAAzB2B,qBACTe,QAAQC,KAAK,0LACR,CACL,IAAI1B,EAAQ,IAAI7B,EAAgBC,EAAIW,EAAOT,GAC3CF,EAAGuD,qBAAuB3B,GAwB9B,SAAS4B,EAAOxD,GACd,IAAI4B,EAAQ5B,EAAGuD,qBAEX3B,IACFA,EAAMhB,yBACCZ,EAAGuD,sBAId,IAAIE,EAAoB,CACtBN,KAAMA,EACNO,OA/BF,SAAgB1D,EAAI2D,EAAOzD,GACzB,IAAIS,EAAQgD,EAAMhD,MAElB,IApIF,SAASiD,EAAUC,EAAMC,GACvB,GAAID,IAASC,EAAM,OAAO,EAE1B,GAAsB,WAAlBhG,EAAQ+F,GAAoB,CAC9B,IAAK,IAAI9E,KAAO8E,EACd,IAAKD,EAAUC,EAAK9E,GAAM+E,EAAK/E,IAC7B,OAAO,EAIX,OAAO,EAGT,OAAO,EAuHH6E,CAAUjD,EADCgD,EAAMI,UACrB,CACA,IAAInC,EAAQ5B,EAAGuD,qBAEV5C,EAKDiB,EACFA,EAAMpB,eAAeG,EAAOT,GAE5BiD,EAAKnD,EAAI,CACPW,MAAOA,GACNT,GATHsD,EAAOxD,KAyBTwD,OAAQA,GAYV,IAAIhG,EAAS,CAEXC,QAAS,QACTC,QAZF,SAAiBC,GACfA,EAAIqG,UAAU,qBAAsBP,KAclC5F,EAAY,KAEM,oBAAXtD,OACTsD,EAAYtD,OAAOoD,IACQ,oBAAXkC,SAChBhC,EAAYgC,OAAOlC,KAGjBE,GACFA,EAAUiC,IAAItC,uOCzSf,IAAUyG,EAAMC,EAAND,EAQT7I,EARe8I,EAQT,WACN,IAAIC,EAAQ,gBAERC,EAAU,SAAUC,EAAMC,GAC5B,OAAwB,OAApBD,EAAKE,WAA8BD,EAEhCF,EAAQC,EAAKE,WAAYD,EAAGrC,OAAO,CAACoC,MAGzCG,EAAQ,SAAUH,EAAMI,GAC1B,OAAOC,iBAAiBL,EAAM,MAAMM,iBAAiBF,IAOnDG,EAAS,SAAUP,GACtB,OAAOF,EAAMU,KALC,SAAUR,GACvB,OAAOG,EAAMH,EAAM,YAAcG,EAAMH,EAAM,cAAgBG,EAAMH,EAAM,cAIxDS,CAAST,KAmB5B,OAhBmB,SAAUA,GAC3B,GAAMA,aAAgBU,aAAeV,aAAgBW,WAArD,CAMA,IAFA,IAAIV,EAAKF,EAAQC,EAAKE,WAAY,IAEzBhG,EAAI,EAAGA,EAAI+F,EAAG9F,OAAQD,GAAK,EAClC,GAAIqG,EAAON,EAAG/F,IACZ,OAAO+F,EAAG/F,GAId,OAAOtB,SAASgI,kBAAoBhI,SAASiI,mBAvCNC,EAAOC,QAC9CD,EAAcC,QAAGlB,IAEjBD,EAAKoB,aAAenB,OCNjB,MAAM5F,EAAQ,CACnBgH,MAAO,CACLlI,KAAM8B,MACNqG,UAAU,GAGZC,SAAU,CACRpI,KAAMqI,OACNC,QAAS,MAGXC,UAAW,CACTvI,KAAMqI,OACNC,QAAS,WACTE,UAAYjF,GAAU,CAAC,WAAY,cAAckF,SAASlF,IAG5DmF,QAAS,CACP1I,KAAMqI,OACNC,QAAS,OAGXK,QAAS,CACP3I,KAAMqI,OACNC,QAAS,QAIN,SAASM,IACd,OAAO5K,KAAKkK,MAAM9G,QAAmC,iBAAlBpD,KAAKkK,MAAM,GC7BzC,IAAIW,GAAkB,EAE7B,GAAsB,oBAAX1L,OAAwB,CACjC0L,GAAkB,EAClB,IACE,IAAIC,EAAOrH,OAAOC,eAAe,GAAI,UAAW,CAC9CoE,MACE+C,GAAkB,KAGtB1L,OAAOkC,iBAAiB,OAAQ,KAAMyJ,GACtC,MAAOxD,KCwEX,IAAAyD,EAAA,uxBAjFA,MAAcC,EAmFd,CACAvK,KAAA,kBAEAwK,WAAA,CACApL,eAAAA,GAGAqL,WAAA,CACA7C,kBAAAA,GAGAnF,MAAA,IACAA,EAEAiI,SAAA,CACAnJ,KAAAoJ,OACAd,QAAA,MAGAe,UAAA,CACArJ,KAAAoJ,OACAd,aAAAhE,GAGAgF,kBAAA,CACAtJ,KAAAoJ,OACAd,aAAAhE,GAGAiF,YAAA,CACAvJ,KAAA,CAAAoJ,OAAAf,QACAC,QAAA,MAGAkB,UAAA,CACAxJ,KAAAqI,OACAC,QAAA,QAGAmB,UAAA,CACAzJ,KAAAqI,OACAC,QAAA,QAGAoB,OAAA,CACA1J,KAAAoJ,OACAd,QAAA,KAGAqB,SAAA,CACA3J,KAAA4J,QACAtB,SAAA,GAGAuB,UAAA,CACA7J,KAAAoJ,OACAd,QAAA,GAGAwB,WAAA,CACA9J,KAAA4J,QACAtB,SAAA,GAGAyB,UAAA,CACA/J,KAAA4J,QACAtB,SAAA,GAGAI,QAAA,CACA1I,KAAAqI,OACAC,QAAA,OAGAK,QAAA,CACA3I,KAAAqI,OACAC,QAAA,OAGA0B,UAAA,CACAhK,KAAA,CAAAqI,OAAA5G,OAAAK,OACAwG,QAAA,IAGA2B,UAAA,CACAjK,KAAA,CAAAqI,OAAA5G,OAAAK,OACAwG,QAAA,KAIApI,KAAA,KACA,CACAgK,KAAA,GACAC,UAAA,EACAC,OAAA,EACAC,SAAA,OAIAC,SAAA,CACAC,QACA,GAAA,OAAAvM,KAAAmL,SAAA,CACA,MAAAoB,EAAA,CACAC,KAAA,CAAAC,YAAA,IAEAvC,EAAAlK,KAAAkK,MACAwC,EAAA1M,KAAAwL,UACAD,EAAAvL,KAAAuL,YACA,IAEAoB,EAFAC,EAAA,IACAH,EAAA,EAEA,IAAA,IAAAtJ,EAAA,EAAA0J,EAAA3C,EAAA9G,OAAAD,EAAA0J,EAAA1J,IACAwJ,EAAAzC,EAAA/G,GAAAuJ,IAAAnB,EACAoB,EAAAC,IACAA,EAAAD,GAEAF,GAAAE,EACAJ,EAAApJ,GAAA,CAAAsJ,YAAAA,EAAAK,KAAAH,GAIA,OADA3M,KAAA+M,sBAAAH,EACAL,EAEA,MAAA,IAGA3B,YAAAA,GAGAoC,MAAA,CACA9C,QACAlK,KAAAiN,oBAAA,IAGAtB,WACA3L,KAAAkN,gBACAlN,KAAAiN,oBAAA,IAGAV,MAAA,CACAY,UACAnN,KAAAiN,oBAAA,IAEAG,MAAA,GAGA/B,YACArL,KAAAiN,oBAAA,IAGA3B,oBACAtL,KAAAiN,oBAAA,KAIAI,UACArN,KAAAsN,aAAA,EACAtN,KAAAuN,WAAA,EACAvN,KAAAwN,QAAA,IAAAC,IACAzN,KAAA0N,cAAA,IAAAD,IACAzN,KAAA2N,eAAA,EACA3N,KAAA4N,2BAAA,EAIA5N,KAAA6L,YACA7L,KAAA6N,aAAA,EACA7N,KAAAiN,oBAAA,IAGAjN,KAAAqL,YAAArL,KAAAmL,UACAlD,QAAA6F,MAAA,6EAIArM,UACAzB,KAAAkN,gBACAlN,KAAA2B,UAAA,KAEA3B,KAAA6N,aAAA,EACA7N,KAAAiN,oBAAA,GACAjN,KAAAoM,OAAA,KAIA2B,YACA,MAAAC,EAAAhO,KAAA4N,2BACA,iBAAAI,GACAhO,KAAA2B,UAAA,KACA3B,KAAAiO,iBAAAD,MAKA7L,gBACAnC,KAAAkO,mBAGAxN,QAAA,CACAyN,QAAAjC,EAAAkC,EAAAC,EAAA1K,EAAA3B,GACA,MAAAsM,EAAA,CACAD,KAAAA,EACAE,SAAA,GAEAC,EAAA,CACAC,GAAA1D,IACAqD,MAAAA,EACAM,MAAA,EACA/K,IAAAA,EACA3B,KAAAA,GAOA,OALAyB,OAAAC,eAAA4K,EAAA,KAAA,CACA/K,cAAA,EACAgC,MAAAiJ,IAEAtC,EAAAyC,KAAAL,GACAA,GAGAM,UAAAN,EAAAO,GAAA,GACA,MAAAC,EAAA9O,KAAA0N,cACA1L,EAAAsM,EAAAS,GAAA/M,KACA,IAAAgN,EAAAF,EAAAhH,IAAA9F,GACAgN,IACAA,EAAA,GACAF,EAAAG,IAAAjN,EAAAgN,IAEAA,EAAAL,KAAAL,GACAO,IACAP,EAAAS,GAAAL,MAAA,EACAJ,EAAAC,UAAA,KACAvO,KAAAwN,QAAA0B,OAAAZ,EAAAS,GAAApL,OAIAwL,eACAnP,KAAAgB,MAAA,UACAhB,KAAAoM,OAAApM,KAAAiN,oBAAA,IAGAmC,aAAAC,GACArP,KAAA2N,gBACA3N,KAAA2N,eAAA,EACA2B,sBAAA,KACAtP,KAAA2N,eAAA,EACA,MAAA4B,WAAAA,GAAAvP,KAAAiN,oBAAA,GAAA,GAIAsC,IACAzI,aAAA9G,KAAAwP,iBACAxP,KAAAwP,gBAAAzI,WAAA/G,KAAAoP,aAAA,UAMAK,uBAAAC,EAAA/J,GACA3F,KAAAoM,QACAsD,GAAA,IAAA/J,EAAAgK,mBAAAC,OAAA,IAAAjK,EAAAgK,mBAAAE,QACA7P,KAAAgB,MAAA,WACAsO,sBAAA,KACAtP,KAAAiN,oBAAA,MAGAjN,KAAAgB,MAAA,YAKAiM,mBAAA6C,EAAAC,GAAA,GACA,MAAA5E,EAAAnL,KAAAmL,SACAE,EAAArL,KAAAqL,WAAA,EACAC,EAAAtL,KAAAsL,mBAAAH,EACAI,EAAAvL,KAAA+M,sBACAtB,EAAAzL,KAAAyL,UACArB,EAAApK,KAAA4K,YAAA,KAAA5K,KAAAoK,SACAF,EAAAlK,KAAAkK,MACA8F,EAAA9F,EAAA9G,OACAmJ,EAAAvM,KAAAuM,MACA0D,EAAAjQ,KAAAwN,QACAsB,EAAA9O,KAAA0N,cACAxB,EAAAlM,KAAAkM,KACA,IAAAgE,EAAAC,EACAhE,EACAiE,EAAAC,EAyGA/B,EAvGA,GAAA0B,EAEA,GAAAhQ,KAAA6N,YACAqC,EAAAE,EAAA,EACAD,EAAAE,EAAAC,KAAAC,IAAAvQ,KAAA6L,UAAA3B,EAAA9G,QACA+I,EAAA,SACA,CACA,MAAA3C,EAAAxJ,KAAAwQ,YAGA,GAAAT,EAAA,CACA,IAAAU,EAAAjH,EAAAkH,MAAA1Q,KAAA4N,2BAEA,GADA6C,EAAA,IAAAA,GAAAA,GACA,OAAAtF,GAAAsF,EAAAlF,GAAAkF,EAAAtF,EACA,MAAA,CACAoE,YAAA,GAIAvP,KAAA4N,2BAAApE,EAAAkH,MAEA,MAAAhF,EAAA1L,KAAA0L,OACAlC,EAAAkH,OAAAhF,EACAlC,EAAAmH,KAAAjF,EAGA,IAAAkF,EAAA,EAOA,GANA5Q,KAAA6Q,MAAAC,SACAF,EAAA5Q,KAAA6Q,MAAAC,OAAAC,aACAvH,EAAAkH,OAAAE,GAIA5Q,KAAA6Q,MAAAG,MAAA,CACA,MAAAC,EAAAjR,KAAA6Q,MAAAG,MAAAD,aACAvH,EAAAmH,KAAAM,EAIA,GAAA,OAAA9F,EAAA,CACA,IAAA+F,EAIAC,EAHAC,EAAA,EACAC,EAAArB,EAAA,EACA7M,KAAA6M,EAAA,GAIA,GACAmB,EAAAhO,EACA+N,EAAA3E,EAAApJ,GAAAsJ,YACAyE,EAAA1H,EAAAkH,MACAU,EAAAjO,EACAA,EAAA6M,EAAA,GAAAzD,EAAApJ,EAAA,GAAAsJ,YAAAjD,EAAAkH,QACAW,EAAAlO,GAEAA,MAAAiO,EAAAC,GAAA,SACAlO,IAAAgO,GAQA,IAPAhO,EAAA,IAAAA,EAAA,GACA+M,EAAA/M,EAGAgJ,EAAAI,EAAAyD,EAAA,GAAAvD,YAGA0D,EAAAhN,EAAAgN,EAAAH,GAAAzD,EAAA4D,GAAA1D,YAAAjD,EAAAmH,IAAAR,KAUA,KATA,IAAAA,EACAA,EAAAjG,EAAA9G,OAAA,GAEA+M,IAEAA,EAAAH,IAAAG,EAAAH,IAIAI,EAAAF,EAAAE,EAAAJ,GAAAY,EAAArE,EAAA6D,GAAA3D,YAAAjD,EAAAkH,MAAAN,KAGA,IAAAC,EAAAD,EAAAC,EAAAL,GAAAY,EAAArE,EAAA8D,GAAA5D,YAAAjD,EAAAmH,IAAAN,UACA,CAEAH,KAAA1G,EAAAkH,MAAAvF,EAAAE,GAEA6E,GADAA,EAAA7E,EAEA8E,EAAAG,KAAAgB,KAAA9H,EAAAmH,IAAAxF,EAAAE,GACA+E,EAAAE,KAAAiB,IAAA,EAAAjB,KAAAkB,OAAAhI,EAAAkH,MAAAE,GAAAzF,EAAAE,IACAgF,EAAAC,KAAAkB,OAAAhI,EAAAmH,IAAAC,GAAAzF,EAAAE,GAGA6E,EAAA,IAAAA,EAAA,GACAC,EAAAH,IAAAG,EAAAH,GACAI,EAAA,IAAAA,EAAA,GACAC,EAAAL,IAAAK,EAAAL,GAEA7D,EAAAmE,KAAAgB,KAAAtB,EAAA3E,GAAAF,QA5FA+E,EAAAC,EAAAC,EAAAC,EAAAlE,EAAA,EAgGAgE,EAAAD,EAAArR,EAAAC,YACAkB,KAAAyR,kBAGAzR,KAAAmM,UAAAA,EAIA,MAAAoD,EAAAW,GAAAlQ,KAAAuN,YAAA4C,GAAAnQ,KAAAsN,aAEA,GAAAtN,KAAA0R,eAAAnC,EAAA,CACA,GAAAA,EAAA,CACAU,EAAA0B,QACA7C,EAAA6C,QACA,IAAA,IAAAxO,EAAA,EAAA0J,EAAAX,EAAA9I,OAAAD,EAAA0J,EAAA1J,IACAmL,EAAApC,EAAA/I,GACAnD,KAAA4O,UAAAN,GAGAtO,KAAA0R,aAAAnC,OACA,GAAAA,EACA,IAAA,IAAApM,EAAA,EAAA0J,EAAAX,EAAA9I,OAAAD,EAAA0J,EAAA1J,IACAmL,EAAApC,EAAA/I,GACAmL,EAAAS,GAAAL,OAEAoB,IACAxB,EAAAS,GAAAX,MAAAlE,EAAA3K,QAAA+O,EAAAD,SAKA,IAAAC,EAAAS,GAAAX,OACAE,EAAAS,GAAAX,MAAA8B,GACA5B,EAAAS,GAAAX,OAAA+B,IAEAnQ,KAAA4O,UAAAN,IAMA,MAAAsD,EAAArC,EAAA,KAAA,IAAA9B,IAEA,IAAAY,EAAArM,EAAAgN,EACA6C,EACA,IAAA,IAAA1O,EAAA+M,EAAA/M,EAAAgN,EAAAhN,IAAA,CACAkL,EAAAnE,EAAA/G,GACA,MAAAQ,EAAAyG,EAAAiE,EAAAjE,GAAAiE,EACA,GAAA,MAAA1K,EACA,MAAA,IAAAmO,MAAA,UAAAnO,2BAAAyG,OAEAkE,EAAA2B,EAAAnI,IAAAnE,GAEAwH,GAAAoB,EAAApJ,GAAA2J,MAMAwB,GA0CAA,EAAAS,GAAAL,MAAA,EACAJ,EAAAD,KAAAA,IA1CAlL,IAAA+G,EAAA9G,OAAA,GAAApD,KAAAgB,MAAA,cACA,IAAAmC,GAAAnD,KAAAgB,MAAA,gBAEAgB,EAAAqM,EAAA5C,GACAuD,EAAAF,EAAAhH,IAAA9F,GAEAuN,EAEAP,GAAAA,EAAA5L,QACAkL,EAAAU,EAAA+C,MACAzD,EAAAD,KAAAA,EACAC,EAAAS,GAAAL,MAAA,EACAJ,EAAAS,GAAAX,MAAAjL,EACAmL,EAAAS,GAAApL,IAAAA,EACA2K,EAAAS,GAAA/M,KAAAA,GAEAsM,EAAAtO,KAAAmO,QAAAjC,EAAA/I,EAAAkL,EAAA1K,EAAA3B,IAMA6P,EAAAD,EAAA9J,IAAA9F,IAAA,IAEAgN,GAAA6C,GAAA7C,EAAA5L,UACAkL,EAAAtO,KAAAmO,QAAAjC,EAAA/I,EAAAkL,EAAA1K,EAAA3B,GACAhC,KAAA4O,UAAAN,GAAA,GACAU,EAAAF,EAAAhH,IAAA9F,IAGAsM,EAAAU,EAAA6C,GACAvD,EAAAD,KAAAA,EACAC,EAAAS,GAAAL,MAAA,EACAJ,EAAAS,GAAAX,MAAAjL,EACAmL,EAAAS,GAAApL,IAAAA,EACA2K,EAAAS,GAAA/M,KAAAA,EACA4P,EAAA3C,IAAAjN,EAAA6P,EAAA,GACAA,KAEA5B,EAAAhB,IAAAtL,EAAA2K,IAOA,OAAAnD,GACAmD,EAAAC,SAAAhC,EAAApJ,EAAA,GAAAsJ,YACA6B,EAAA0D,OAAA,IAEA1D,EAAAC,SAAA+B,KAAAkB,MAAArO,EAAAkI,GAAAF,EACAmD,EAAA0D,OAAA7O,EAAAkI,EAAAC,IAzDAgD,GAAAtO,KAAA4O,UAAAN,GAuEA,OAVAtO,KAAAsN,aAAA4C,EACAlQ,KAAAuN,WAAA4C,EAEAnQ,KAAA8L,YAAA9L,KAAAgB,MAAA,SAAAkP,EAAAC,EAAAC,EAAAC,GAIAvJ,aAAA9G,KAAAiS,aACAjS,KAAAiS,YAAAlL,WAAA/G,KAAAkS,UAAA,KAEA,CACA3C,WAAAA,IAIA4C,oBACA,IAAAlP,EAAAmP,EAAApS,KAAAa,KAKA,OAHA1B,OAAA0C,UAAAoB,IAAA9D,OAAA0C,SAAAiI,iBAAA7G,IAAA9D,OAAA0C,SAAAwQ,OACApP,EAAA9D,QAEA8D,GAGAuN,YACA,MAAA3P,IAAA+D,EAAA2F,UAAAA,GAAAvK,KACAsS,EAAA,aAAA/H,EACA,IAAAgI,EAEA,GAAAvS,KAAA2L,SAAA,CACA,MAAA6G,EAAA5N,EAAA6N,wBACAC,EAAAJ,EAAAE,EAAA3C,OAAA2C,EAAA5C,MACA,IAAAc,IAAA4B,EAAAE,EAAAG,IAAAH,EAAAI,MACA9F,EAAAwF,EAAAnT,OAAA0T,YAAA1T,OAAA2T,WACApC,EAAA,IACA5D,GAAA4D,EACAA,EAAA,GAEAA,EAAA5D,EAAA4F,IACA5F,EAAA4F,EAAAhC,GAEA6B,EAAA,CACA7B,MAAAA,EACAC,IAAAD,EAAA5D,QAGAyF,EADAD,EACA,CACA5B,MAAA9L,EAAAmO,UACApC,IAAA/L,EAAAmO,UAAAnO,EAAAoO,cAGA,CACAtC,MAAA9L,EAAAqO,WACAtC,IAAA/L,EAAAqO,WAAArO,EAAAsO,aAIA,OAAAX,GAGArF,gBACAlN,KAAA2L,SACA3L,KAAAmT,eAEAnT,KAAAkO,mBAIAiF,eACAnT,KAAAoT,eAAApT,KAAAmS,oBACAnS,KAAAoT,eAAA/R,iBAAA,SAAArB,KAAAoP,eAAAvE,GACA,CACAwI,SAAA,IAGArT,KAAAoT,eAAA/R,iBAAA,SAAArB,KAAAmP,eAGAjB,kBACAlO,KAAAoT,iBAIApT,KAAAoT,eAAA5R,oBAAA,SAAAxB,KAAAoP,cACApP,KAAAoT,eAAA5R,oBAAA,SAAAxB,KAAAmP,cAEAnP,KAAAoT,eAAA,OAGAE,aAAAlF,GACA,IAAA5E,EAEAA,EADA,OAAAxJ,KAAAmL,SACAiD,EAAA,EAAApO,KAAAuM,MAAA6B,EAAA,GAAA3B,YAAA,EAEA6D,KAAAkB,MAAApD,EAAApO,KAAAqL,WAAArL,KAAAmL,SAEAnL,KAAAiO,iBAAAzE,IAGAyE,iBAAAM,GACA,MAAAhE,EAAA,aAAAvK,KAAAuK,UACA,CAAAf,OAAA,YAAAkH,MAAA,OACA,CAAAlH,OAAA,aAAAkH,MAAA,QAEA,IAAA6C,EACAC,EACAC,EAEA,GAAAzT,KAAA2L,SAAA,CACA,MAAA+H,EAAAtB,EAAApS,KAAAa,KAEAkS,EAAA,SAAAW,EAAAC,QAAA,EAAAD,EAAAnJ,EAAAf,QACAgJ,EAAAkB,EAAAjB,wBAGAmB,EADA5T,KAAAa,IAAA4R,wBACAlI,EAAAmG,OAAA8B,EAAAjI,EAAAmG,OAEA6C,EAAAG,EACAF,EAAAjJ,EAAAf,OACAiK,EAAAlF,EAAAwE,EAAAa,OAEAL,EAAAvT,KAAAa,IACA2S,EAAAjJ,EAAAf,OACAiK,EAAAlF,EAGAgF,EAAAC,GAAAC,GAGAhC,kBAKA,MAJA1K,WAAA,KACAkB,QAAA4L,IAAA,8FAAA,YAAA7T,KAAAa,KACAoH,QAAA4L,IAAA,gMAEA,IAAA/B,MAAA,iCAGAI,YACAlS,KAAAkM,KAAA4H,KAAA,CAAAC,EAAAC,IAAAD,EAAAhF,GAAAX,MAAA4F,EAAAjF,GAAAX,muDChtBA,MAAcpD,EAsCd,CACAvK,KAAA,kBAEAwK,WAAA,CACAgJ,gBAAAA,GAGAC,UAwBA,MAvBA,oBAAArU,iBACAG,KAAAmU,iBAAA,IAAAtU,eAAAsH,IACAmI,sBAAA,KACA,GAAAxL,MAAAC,QAAAoD,GAGA,IAAA,MAAAxB,KAAAwB,EACA,GAAAxB,EAAA1C,OAAA,CACA,MAAAoM,EAAA,IAAA+E,YACA,SACA,CACAC,OAAA,CACAC,YAAA3O,EAAA2O,eAIA3O,EAAA1C,OAAAsR,cAAAlF,SAOA,CACAmF,YAAAxU,KAAAwU,YACAC,cAAAzU,KACA0U,sBAAA1U,KAAAmU,mBAIAQ,cAAA,EAEAzR,MAAA,IACAA,EAEAqI,YAAA,CACAvJ,KAAA,CAAAoJ,OAAAf,QACAF,UAAA,IAIAjI,OACA,MAAA,CACAsS,YAAA,CACAI,QAAA,EACArI,MAAA,GACAsI,WAAA,GACAzK,SAAApK,KAAAoK,SACAQ,aAAA,KAKA0B,SAAA,CACA1B,YAAAA,EAEAkK,gBACA,MAAApP,EAAA,IACAwE,MAAAA,EAAAE,SAAAA,EAAAQ,YAAAA,GAAA5K,KACAuM,EAAAvM,KAAAwU,YAAAjI,MACAM,EAAA3C,EAAA9G,OACA,IAAA,IAAAD,EAAA,EAAAA,EAAA0J,EAAA1J,IAAA,CACA,MAAAkL,EAAAnE,EAAA/G,GACAsL,EAAA7D,EAAAzH,EAAAkL,EAAAjE,GACA,IAAA0C,EAAAP,EAAAkC,QACA,IAAA3B,GAAA9M,KAAA+U,eAAAtG,KACA3B,EAAA,GAEApH,EAAAiJ,KAAA,CACAN,KAAAA,EACAI,GAAAA,EACA3B,KAAAA,IAGA,OAAApH,GAGAsP,YACA,MAAAA,EAAA,GACA,IAAA,MAAArR,KAAA3D,KAAAiV,WACA,WAAAtR,GAAA,YAAAA,IACAqR,EAAArR,GAAA3D,KAAAiV,WAAAtR,IAGA,OAAAqR,IAIAhI,MAAA,CACA9C,QACAlK,KAAAkV,aAAA,IAGAtK,YAAA,CACAuC,QAAA5H,GACAvF,KAAAwU,YAAA5J,YAAArF,GAEA4P,WAAA,GAGA5K,UAAAhF,GACAvF,KAAAkV,aAAA,IAGAJ,cAAAM,EAAAC,GACA,MAAAtC,EAAA/S,KAAAa,IAAAkS,UAKA,IAAAuC,EAAA,EAAAC,EAAA,EACA,MAAAnS,EAAAkN,KAAAC,IAAA6E,EAAAhS,OAAAiS,EAAAjS,QACA,IAAA,IAAAD,EAAA,EAAAA,EAAAC,KACAkS,GAAAvC,GADA5P,IAIAmS,GAAAD,EAAAlS,GAAA2J,MAAA9M,KAAAuL,YACAgK,GAAAH,EAAAjS,GAAA2J,MAAA9M,KAAAuL,YAEA,MAAAyG,EAAAuD,EAAAD,EAEA,IAAAtD,IAIAhS,KAAAa,IAAAkS,WAAAf,KAIAwD,eACAxV,KAAAyV,UAAA,GACAzV,KAAA0V,iBAAA,EACA1V,KAAA+U,eAAA,IAGAhH,YACA/N,KAAAwU,YAAAI,QAAA,GAGAe,cACA3V,KAAAwU,YAAAI,QAAA,GAGAlU,QAAA,CACAkV,mBACA5V,KAAA6Q,MAAAgF,UAEA7V,KAAAkV,cAEAlV,KAAAgB,MAAA,WAGA8U,oBACA9V,KAAAgB,MAAA,iBAAA,CAAA+U,OAAA,IACA/V,KAAAgB,MAAA,YAGAkU,YAAAvD,GAAA,IACAA,GAAA3R,KAAA4K,eACA5K,KAAAwU,YAAAK,WAAA,IAEA7U,KAAAgB,MAAA,iBAAA,CAAA+U,OAAA,KAGAzC,aAAAlF,GACA,MAAAyH,EAAA7V,KAAA6Q,MAAAgF,SACAA,GAAAA,EAAAvC,aAAAlF,IAGA4H,YAAA3H,EAAAD,GACA,MAAAK,EAAAzO,KAAA4K,YAAA,MAAAwD,EAAAA,EAAApO,KAAAkK,MAAA3K,QAAA8O,GAAAA,EAAArO,KAAAoK,UACA,OAAApK,KAAAwU,YAAAjI,MAAAkC,IAAA,GAGAwH,iBACA,GAAAjW,KAAAkW,oBAAA,OACAlW,KAAAkW,qBAAA,EACA,MAAAtR,EAAA5E,KAAAa,IAEAb,KAAA2B,UAAA,KACAiD,EAAAmO,UAAAnO,EAAAmM,aAAA,IAEA,MAAAoF,EAAA,KACAvR,EAAAmO,UAAAnO,EAAAmM,aAAA,IACAzB,sBAAA,KACA1K,EAAAmO,UAAAnO,EAAAmM,aAAA,IACA,IAAA/Q,KAAA0V,iBACA1V,KAAAkW,qBAAA,EAEA5G,sBAAA6G,MAIA7G,sBAAA6G,g0BC/OA,oBADA,CACA1V,KAAA,sBAEA2V,OAAA,CACA,cACA,gBACA,yBAGAlT,MAAA,CAEAmL,KAAA,CACAlE,UAAA,GAGAkM,UAAA,CACArU,KAAA4J,QACAtB,SAAA,GAMAsK,OAAA,CACA5S,KAAA4J,QACAzB,UAAA,GAGAiE,MAAA,CACApM,KAAAoJ,OACAd,aAAAhE,GAGAgQ,iBAAA,CACAtU,KAAA,CAAA8B,MAAAL,QACA6G,QAAA,MAGAiM,WAAA,CACAvU,KAAA4J,QACAtB,SAAA,GAGAkM,IAAA,CACAxU,KAAAqI,OACAC,QAAA,QAIAgC,SAAA,CACAmC,KACA,GAAAzO,KAAAwU,YAAA5J,YAAA,OAAA5K,KAAAoO,MAEA,GAAApO,KAAAqO,KAAAoI,eAAAzW,KAAAwU,YAAApK,UAAA,OAAApK,KAAAqO,KAAArO,KAAAwU,YAAApK,UACA,MAAA,IAAA0H,MAAA,aAAA9R,KAAAwU,YAAApK,4FAGA0C,OACA,OAAA9M,KAAAwU,YAAAK,WAAA7U,KAAAyO,KAAAzO,KAAAwU,YAAAjI,MAAAvM,KAAAyO,KAAA,GAGAiI,cACA,OAAA1W,KAAA4U,QAAA5U,KAAAwU,YAAAI,SAIA5H,MAAA,CACAqJ,UAAA,kBAEA5H,KACAzO,KAAA8M,MACA9M,KAAA2W,gBAIAD,YAAAnR,GACAvF,KAAA8M,OACAvH,EACAvF,KAAAyU,cAAAM,eAAA/U,KAAAyO,MACAzO,KAAAyU,cAAAiB,mBACA1V,KAAAyU,cAAAM,eAAA/U,KAAAyO,KAAA,GAGAzO,KAAAyU,cAAAM,eAAA/U,KAAAyO,MACAzO,KAAAyU,cAAAiB,mBACA1V,KAAAyU,cAAAM,eAAA/U,KAAAyO,KAAA,IAKAzO,KAAA0U,sBACAnP,EACAvF,KAAA4W,cAEA5W,KAAA6W,gBAEAtR,GAAAvF,KAAA8W,yBAAA9W,KAAAyO,IACAzO,KAAA+W,eAKA1J,UACA,IAAArN,KAAAgX,YAEAhX,KAAAiX,yBAAA,KACAjX,KAAAkX,mBAEAlX,KAAA0U,uBAAA,CACA,IAAA,MAAAyC,KAAAnX,KAAAsW,iBACAtW,KAAAoX,OAAA,IAAApX,KAAAsW,iBAAAa,GAAAnX,KAAA2W,cAGA3W,KAAAyU,cAAA4C,IAAA,iBAAArX,KAAAsX,iBACAtX,KAAAyU,cAAA4C,IAAA,sBAAArX,KAAAuX,uBAIA9V,UACAzB,KAAAwU,YAAAI,SACA5U,KAAA+W,aACA/W,KAAA4W,gBAIAzU,gBACAnC,KAAAyU,cAAA+C,KAAA,iBAAAxX,KAAAsX,iBACAtX,KAAAyU,cAAA+C,KAAA,sBAAAxX,KAAAuX,qBACAvX,KAAA6W,iBAGAnW,QAAA,CACAqW,aACA/W,KAAA0W,YACA1W,KAAAyX,sBAAAzX,KAAAyO,KACAzO,KAAAyX,oBAAAzX,KAAAyO,GACAzO,KAAAiX,yBAAA,KACAjX,KAAA8W,uBAAA,KACA9W,KAAA0X,YAAA1X,KAAAyO,KAGAzO,KAAAiX,yBAAAjX,KAAAyO,IAIAyI,kBACAlX,KAAAqW,YAAArW,KAAA0U,sBACA1U,KAAA2X,YAAA3X,KAAAoX,OAAA,OAAA,KACApX,KAAA2W,gBACA,CACAvJ,MAAA,IAEApN,KAAA2X,cACA3X,KAAA2X,cACA3X,KAAA2X,YAAA,OAIAL,iBAAAvB,MAAAA,KAEA/V,KAAA0W,aAAAX,IACA/V,KAAA8W,uBAAA9W,KAAAyO,IAGAzO,KAAAiX,2BAAAjX,KAAAyO,KAAAsH,GAAA/V,KAAA8M,MACA9M,KAAA+W,cAIAJ,eACA3W,KAAA+W,cAGAW,YAAAjJ,GACAzO,KAAA2B,UAAA,KACA,GAAA3B,KAAAyO,KAAAA,EAAA,CACA,MAAAmB,EAAA5P,KAAAa,IAAAC,YACA+O,EAAA7P,KAAAa,IAAAE,aACAf,KAAA4X,UAAAhI,EAAAC,GAEA7P,KAAAyX,oBAAA,QAIAG,UAAAhI,EAAAC,GACA,MAAA/C,KAAA,aAAA9M,KAAAyU,cAAAlK,UAAAsF,EAAAD,GACA9C,GAAA9M,KAAA8M,OAAAA,IACA9M,KAAAyU,cAAAM,eAAA/U,KAAAyO,MACAzO,KAAAyU,cAAAiB,mBACA1V,KAAAyU,cAAAM,eAAA/U,KAAAyO,SAAAnI,GAEAtG,KAAA6X,KAAA7X,KAAAwU,YAAAjI,MAAAvM,KAAAyO,GAAA3B,GACA9M,KAAA6X,KAAA7X,KAAAwU,YAAAK,WAAA7U,KAAAyO,IAAA,GACAzO,KAAAuW,YAAAvW,KAAAgB,MAAA,SAAAhB,KAAAyO,MAIAmI,cACA5W,KAAA0U,uBAAA1U,KAAAa,IAAAsI,aACAnJ,KAAA0U,sBAAA9M,QAAA5H,KAAAa,IAAAsI,YACAnJ,KAAAa,IAAAsI,WAAA9H,iBAAA,SAAArB,KAAA8X,YAGAjB,gBACA7W,KAAA0U,wBACA1U,KAAA0U,sBAAAqD,UAAA/X,KAAAa,IAAAsI,YACAnJ,KAAAa,IAAAsI,WAAA3H,oBAAA,SAAAxB,KAAA8X,YAGAA,SAAAzI,GACA,MAAAO,MAAAA,EAAAC,OAAAA,GAAAR,EAAAgF,OAAAC,YACAtU,KAAA4X,UAAAhI,EAAAC,KAIA/P,OAAAoR,GACA,OAAAA,EAAAlR,KAAAwW,IAAAxW,KAAAgY,OAAA1N,yDClMA,MAAMlI,EAAS,CAEbC,QAAS4V,QACT3V,QAASC,EAAKsC,GACZ,MAAMqT,EAAezU,OAAO0U,OAAO,GAAI,CACrCC,mBAAmB,EACnBC,iBAAkB,IACjBxT,GAEH,IAAK,MAAMlB,KAAOuU,OACiB,IAAtBA,EAAavU,KACtB9E,EAAO8E,GAAOuU,EAAavU,IAI3BuU,EAAaE,mBAxBrB,SAA6B7V,EAAK+V,GAChC/V,EAAIC,UAAa8V,EAAF,mBAA4BrE,GAC3C1R,EAAIC,UAAa8V,EAAF,kBAA2BrE,GAC1C1R,EAAIC,UAAa8V,EAAF,mBAA4BC,GAC3ChW,EAAIC,UAAa8V,EAAF,kBAA2BC,GAC1ChW,EAAIC,UAAa8V,EAAF,wBAAiCE,GAChDjW,EAAIC,UAAa8V,EAAF,sBAA+BE,GAmB1CC,CAAmBlW,EAAK2V,EAAaG,oBAQ3C,IAAI5V,EAAY,WACM,oBAAXtD,OACTsD,EAAYtD,OAAOoD,IACQ,oBAAXkC,SAChBhC,EAAYgC,OAAOlC,KAEjBE,GACFA,EAAUiC,IAAItC,yDCpDD,UAAUsW,OACvBA,EAASC,CAAAA,GAAMA,EAAGtK,KAAKI,KACrB,IACF,MAAMmK,EAAQ,GACRD,EAAK,IAAIpW,UAAI,CACjBL,KAAI,KACK,CACL0W,MAAAA,MAMN,MAAO,CACL1W,KAAI,KACK,CACL2W,QAAS,OAIbxL,UACErN,KAAK8Y,KAAO,KAEV9Y,KAAK+Y,QADe,mBAAXL,EACM,IAAMA,EAAOtU,KAAKpE,KAAMA,MAExB,IAAMA,KAAK0Y,GAE5B1Y,KAAKoX,OAAOpX,KAAK+Y,QAAS,CACxB5L,QAAS5H,GACPvF,KAAK2B,UAAU,KACb3B,KAAK8Y,KAAOvT,KAGhB4P,WAAW,IAEbnV,KAAKgZ,mBAGPC,eACEjZ,KAAKgZ,mBAGPtY,QAAS,CAKPwY,cAAezK,GACb,MAAM3F,EAAU9I,KAAKmZ,SAASN,QAC9B,GAAuB,mBAAZ/P,EAAwB,CACjC,MAAM5G,EAAO4G,EAAQ1E,KAAKpE,KAAMA,MAGhC,OAFA2Y,EAAGd,KAAKe,EAAOnK,EAAIvM,GACnBlC,KAAK8Y,KAAOrK,EACLvM,EAEP,MAAM,IAAI4P,MAAM,wEAOpBkH,kBACE,MAAMvK,EAAKzO,KAAK+Y,UACN,MAANtK,GACFxG,QAAQC,KAAM,yCAAwCwQ,OAEpDjK,IAAOzO,KAAK8Y,OACTF,EAAMnK,IACTzO,KAAKkZ,cAAczK,GAErBzO,KAAK6Y,QAAUD,EAAMnK"}