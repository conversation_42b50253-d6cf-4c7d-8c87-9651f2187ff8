<template>
<div class="nurse-log">
    <el-button type="primary" @click="goBack">
        返回工作台
      </el-button>
    <h2 class="titleLog">护士日志</h2>
    <table class="table-style">
        <tbody>
            <tr>
                <td style="text-align: center;">所属部门:护士部</td>
                <td>护士姓名：王</td>
                <td style="text-align: center;">日志日期： <el-date-picker v-model="nurseLog.roundTime" type="date" placeholder="选择日期" style="width: 80%" value-format="YYYY-MM-DD"></el-date-picker>
                </td>
            </tr>
            <tr>
                <td style="text-align: center;">工作内容</td>
                <td colspan="2">
                    <el-input placeholder="请输入" v-model="nurseLog.recorder" type="textarea" :autosize="{ minRows: 4, maxRows:8}"></el-input>
                </td>
            </tr>
            <tr>
                <td style="text-align: center;">工作计划</td>
                <td colspan="2">
                    <el-input placeholder="请输入" v-model="nurseLog.recorder2" type="textarea" :autosize="{ minRows: 4, maxRows: 8}"></el-input>
                </td>
            </tr>
            <tr>
                <td style="text-align: center;">工作建议</td>
                <td colspan="2">
                    <el-input placeholder="请输入" v-model="nurseLog.recorder3" type="textarea" :autosize="{ minRows: 4, maxRows: 8}"></el-input>
                </td>
            </tr>
        </tbody>
    </table>
    <div style="text-align: center;margin-top: 20px;">
        <el-button type="primary" @click="submit">提交</el-button>
        <el-button @click="goBack">取消</el-button>
    </div>
</div>
</template>

<script setup>
import moment from 'moment';
const router = useRouter()
const nurseLog = ref({
    roundTime:moment().format('YYYY-MM-DD'),
})
const submit = () => {
    
}
 // 返回工作台
const goBack = () => {
 router.push('/work/nurseworkstation')
}
</script>

<style lang="scss" scoped>
.nurse-log {
    padding: 20px;

    .titleLog {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #D9001B;
        text-align: center;
    }
}

.table-style {
    border: 1px solid #ddd;
    border-collapse: collapse;
    width: 100%;

    td {
        border: 1px solid #ddd;
        padding: 8px;
        font-size: 14px;
    }
}
</style>
