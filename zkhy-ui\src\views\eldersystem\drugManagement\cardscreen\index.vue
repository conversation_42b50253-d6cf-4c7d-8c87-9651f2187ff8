<template>
  <div class="medication-screen">
    <!-- 顶部操作栏 -->
    <div class="toolbar">
      <!-- 第一行：选项卡 -->
      <div class="toolbar-row">
        <div class="view-tabs">
          <button
            v-for="tab in tabs"
            :key="tab.value"
            :class="['tab-btn', { 'active': activeTab === tab.value }]"
            @click="activeTab = tab.value"
          >
            {{ tab.label }}
          </button>
        </div>
      </div>

      <!-- 第二行：搜索条件 -->
      <div class="toolbar-row search-row">
        <!-- 左侧：日期导航 -->
        <div class="date-navigation">
          <el-button type="primary" size="small" @click="navigate(-1)">
            <i class="el-icon-arrow-left"></i>
          </el-button>
          <div class="date-display">
            {{ activeTab === 'day' ? currentDate : currentWeekRange }}
          </div>
          <el-button type="primary" size="small" @click="navigate(1)">
            <i class="el-icon-arrow-right"></i>
          </el-button>
        </div>

        <!-- 中间：搜索条件 -->
        <div class="search-filters">
          <el-input
            v-model="searchQuery"
            placeholder="请输入人员姓名"
            prefix-icon="el-icon-search"
            clearable
            size="small"
            @input="handleFilterChange"
          />

          <el-input
            v-model="roomQuery"
            placeholder="请输入房间号"
            prefix-icon="el-icon-search"
            clearable
            size="small"
            @input="handleFilterChange"
          />

          <el-select
            v-model="selectedBuilding"
            placeholder="楼栋选择"
            clearable
            size="small"
            @change="handleBuildingChange"
          >
            <el-option
              v-for="building in buildings"
              :key="building.value"
              :label="building.label"
              :value="building.value"
            />
          </el-select>
        </div>

        <!-- 右侧：时间段筛选和统计 -->
        <div class="time-filter">
          <div class="time-buttons">
            <el-button
              v-for="timeSlot in timeSlots"
              :key="timeSlot.value"
              :type="selectedTimeSlot === timeSlot.value ? 'primary' : 'default'"
              size="small"
              @click="selectTimeSlot(timeSlot.value)"
            >
              {{ timeSlot.label }}
            </el-button>
          </div>
          <div class="time-stats">
            <div
              v-for="timeSlot in timeSlots"
              :key="timeSlot.value"
              class="stat-item"
            >
              <span class="stat-label">{{ timeSlot.label }}：</span>
              <span class="stat-count">{{ getTimeSlotStats(timeSlot.value) }}人</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <!-- 日视图 -->
      <div v-if="activeTab === 'day'" class="day-view">
        <div
          v-for="elder in filteredElders"
          :key="elder.id"
          class="elder-card"
        >
          <div class="elder-header">
            <div class="elder-avatar">
              <el-avatar :src="elder.avatar" size="medium"></el-avatar>
            </div>
            <div class="elder-info">
              <div class="name">{{ elder.name }}</div>
              <div class="room">{{ elder.roomNo }} - {{ elder.bedNo }}</div>
            </div>
          </div>

          <div class="medication-times">
            <div
              v-for="timeSlot in timeSlots"
              :key="timeSlot.value"
              class="time-slot"
              :style="{ borderColor: timeSlot.color }"
            >
              <div class="time-label" :style="{ color: timeSlot.color }">
                {{ timeSlot.label }}
              </div>
              <div class="medication-list">
                <template v-if="getMedications(elder.id, timeSlot.value).length > 0">
                  <div
                    v-for="med in getMedications(elder.id, timeSlot.value)"
                    :key="med.id"
                    class="medication-item"
                  >
                    <span class="med-name">{{ med.name }}</span>
                    <div class="med-right">
                      <span class="med-dose">{{ med.dose }}</span>
                      <span
                        class="med-status"
                        :class="{ 'taken': med.taken }"
                      >
                        {{ med.taken ? '已服用' : '未服用' }}
                      </span>
                    </div>
                  </div>
                </template>
                <div v-else class="no-medication">无</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 周视图 -->
      <div v-else class="week-view">
        <el-table
          :data="filteredElders"
          style="width: 100%"
          v-loading="loading"
          element-loading-text="加载中..."
          border
        >
          <el-table-column prop="name" label="老人信息" width="180" fixed>
            <template #default="{ row }">
              <div class="elder-cell">
                <el-avatar :src="row.avatar" size="small"></el-avatar>
                <div class="elder-details">
                  <div class="name">{{ row.name }}</div>
                  <div class="room">{{ row.roomNo }} - {{ row.bedNo }}</div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            v-for="day in weekDays"
            :key="day.date"
            :label="day.label"
            min-width="180"
          >
            <template #default="{ row }">
              <div class="day-cell">
                <div
                  v-for="timeSlot in timeSlots"
                  :key="timeSlot.value"
                  class="week-time-slot"
                >
                  <div class="time-label" :style="{ color: timeSlot.color }">
                    {{ timeSlot.label }}
                  </div>
                  <div class="medication-list">
                    <template v-if="getMedications(row.id, timeSlot.value, day.date).length > 0">
                      <div
                        v-for="med in getMedications(row.id, timeSlot.value, day.date)"
                        :key="med.id"
                        class="medication-item"
                      >
                        <span class="med-name">{{ med.name }}</span>
                        <div class="med-right">
                          <span class="med-dose">{{ med.dose }}</span>
                          <span
                            class="med-status"
                            :class="{ 'taken': med.taken }"
                          >
                            {{ med.taken ? '已服用' : '未服用' }}
                          </span>
                        </div>
                      </div>
                    </template>
                    <div v-else class="no-medication">-</div>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 响应式状态
const activeTab = ref('day');
const searchQuery = ref('');
const roomQuery = ref('');
const selectedBuilding = ref('');
const selectedTimeSlot = ref('all');
const selectedDate = ref(new Date().toISOString().split('T')[0]);
const selectedWeek = ref(new Date());
const loading = ref(false);

// 静态数据
const tabs = ref([
  { label: '日视图', value: 'day' },
  { label: '周视图', value: 'week' }
]);

const timeSlots = ref([
  { label: '早上', value: 'morning', color: '#6d8cff' },
  { label: '中午', value: 'noon', color: '#b07a1b' },
  { label: '晚上', value: 'evening', color: '#5e0fa3' }
]);

const buildings = ref([
  { label: 'A栋', value: 'A' },
  { label: 'B栋', value: 'B' },
  { label: 'C栋', value: 'C' },
  { label: 'D栋', value: 'D' }
]);

// 模拟数据
const elders = ref([
  {
    id: 1,
    name: '张三',
    roomNo: '101',
    bedNo: 'A床',
    building: 'A',
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg'
  },
  {
    id: 2,
    name: '李四',
    roomNo: '102',
    bedNo: 'B床',
    building: 'A',
    avatar: 'https://randomuser.me/api/portraits/women/1.jpg'
  },
  {
    id: 3,
    name: '王五',
    roomNo: '201',
    bedNo: 'A床',
    building: 'B',
    avatar: 'https://randomuser.me/api/portraits/men/2.jpg'
  },
  {
    id: 4,
    name: '赵六',
    roomNo: '202',
    bedNo: 'B床',
    building: 'B',
    avatar: 'https://randomuser.me/api/portraits/women/2.jpg'
  },
  {
    id: 5,
    name: '钱七',
    roomNo: '301',
    bedNo: 'A床',
    building: 'C',
    avatar: 'https://randomuser.me/api/portraits/men/3.jpg'
  },
  {
    id: 6,
    name: '孙八',
    roomNo: '302',
    bedNo: 'B床',
    building: 'C',
    avatar: 'https://randomuser.me/api/portraits/women/3.jpg'
  }
]);

const medications = ref([
  // 今天的用药记录 - 2025年6月26日
  { id: 1, elderId: 1, date: '2025-06-26', time: 'morning', name: '阿司匹林', dose: '1片', taken: true },
  { id: 2, elderId: 1, date: '2025-06-26', time: 'morning', name: '维生素C', dose: '2片', taken: true },
  { id: 3, elderId: 1, date: '2025-06-26', time: 'noon', name: '二甲双胍', dose: '1片', taken: false },
  { id: 4, elderId: 1, date: '2025-06-26', time: 'evening', name: '钙片', dose: '1片', taken: false },

  { id: 5, elderId: 2, date: '2025-06-26', time: 'morning', name: '降压药', dose: '1片', taken: true },
  { id: 6, elderId: 2, date: '2025-06-26', time: 'noon', name: '胰岛素', dose: '10单位', taken: true },
  { id: 7, elderId: 2, date: '2025-06-26', time: 'evening', name: '安眠药', dose: '1片', taken: false },

  { id: 8, elderId: 3, date: '2025-06-26', time: 'morning', name: '心脏病药', dose: '1片', taken: true },
  { id: 9, elderId: 3, date: '2025-06-26', time: 'noon', name: '消化药', dose: '2片', taken: false },
  { id: 10, elderId: 3, date: '2025-06-26', time: 'evening', name: '维生素D', dose: '1片', taken: false },

  { id: 11, elderId: 4, date: '2025-06-26', time: 'morning', name: '关节炎药', dose: '1片', taken: true },
  { id: 12, elderId: 4, date: '2025-06-26', time: 'noon', name: '钙片', dose: '2片', taken: true },
  { id: 13, elderId: 4, date: '2025-06-26', time: 'evening', name: '维生素B', dose: '1片', taken: false },

  { id: 14, elderId: 5, date: '2025-06-26', time: 'morning', name: '血脂药', dose: '1片', taken: false },
  { id: 15, elderId: 5, date: '2025-06-26', time: 'noon', name: '护肝药', dose: '2片', taken: true },
  { id: 16, elderId: 5, date: '2025-06-26', time: 'evening', name: '安神药', dose: '1片', taken: false },

  { id: 17, elderId: 6, date: '2025-06-26', time: 'morning', name: '降糖药', dose: '1片', taken: true },
  { id: 18, elderId: 6, date: '2025-06-26', time: 'noon', name: '维生素E', dose: '1片', taken: true },
  { id: 19, elderId: 6, date: '2025-06-26', time: 'evening', name: '钙片', dose: '1片', taken: false },
]);

// 计算属性
const currentDate = computed(() => {
  return formatDate(selectedDate.value, 'yyyy年MM月dd日');
});

const currentWeekRange = computed(() => {
  try {
    // 确保使用正确的日期对象
    const current = new Date(selectedWeek.value || new Date());
    // 获取本周一
    const start = new Date(current);
    start.setDate(start.getDate() - (start.getDay() || 7) + 1);

    // 获取本周日
    const end = new Date(start);
    end.setDate(start.getDate() + 6);

    // 格式化日期为 MM/dd 格式
    const formatDate = (date) => {
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}`;
    };

    return `${formatDate(start)} - ${formatDate(end)}`;
  } catch (error) {
    console.error('计算周范围时出错:', error);
    return '日期错误';
  }
});

const weekDays = computed(() => {
  const days = [];
  try {
    // 确保使用正确的日期对象
    const current = new Date(selectedWeek.value || new Date());
    // 获取本周一
    const firstDay = new Date(current);
    firstDay.setDate(firstDay.getDate() - (firstDay.getDay() || 7) + 1);

    // 生成一周的日期（周一到周日）
    for (let i = 0; i < 7; i++) {
      const date = new Date(firstDay);
      date.setDate(date.getDate() + i);

      // 格式化为 YYYY-MM-DD
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const dateStr = `${year}-${month}-${day}`;

      // 格式化为 MM-DD 周X
      const weekDay = '日一二三四五六'[date.getDay()];
      const label = `${month}-${day} 周${weekDay}`;

      days.push({
        date: dateStr,
        label: label,
        isToday: isToday(dateStr)
      });
    }
  } catch (error) {
    console.error('生成周日期时出错:', error);
  }

  return days;
});

const filteredElders = computed(() => {
  let filtered = elders.value;

  // 楼栋筛选
  if (selectedBuilding.value) {
    filtered = filtered.filter(elder => elder.building === selectedBuilding.value);
  }

  // 姓名搜索
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(elder =>
      elder.name.toLowerCase().includes(query)
    );
  }

  // 房间号搜索
  if (roomQuery.value) {
    const query = roomQuery.value.toLowerCase();
    filtered = filtered.filter(elder =>
      elder.roomNo.toLowerCase().includes(query)
    );
  }

  return filtered;
});

// 方法
const formatDate = (date, format) => {
  if (!date) return '';

  let d;
  if (typeof date === 'string') {
    // 处理 YYYY-MM-DD 格式的日期字符串
    const parts = date.split('-').map(Number);
    d = new Date(parts[0], parts[1] - 1, parts[2]);
  } else {
    d = new Date(date);
  }

  if (isNaN(d.getTime())) return '';

  // 创建格式化映射
  const map = {
    'yyyy': d.getFullYear(),
    'MM': (d.getMonth() + 1).toString().padStart(2, '0'),
    'dd': d.getDate().toString().padStart(2, '0'),
    'HH': d.getHours().toString().padStart(2, '0'),
    'mm': d.getMinutes().toString().padStart(2, '0'),
    'ss': d.getSeconds().toString().padStart(2, '0')
  };

  let result = format;
  for (const [key, value] of Object.entries(map)) {
    result = result.replace(new RegExp(key, 'g'), value);
  }

  return result;
};

const isToday = (date) => {
  return date === formatDate(new Date(), 'yyyy-MM-dd');
};

const navigate = (days) => {
  try {
    if (activeTab.value === 'day') {
      const date = new Date(selectedDate.value);
      date.setDate(date.getDate() + days);
      selectedDate.value = formatDate(date, 'yyyy-MM-dd');
    } else {
      const week = new Date(selectedWeek.value);
      week.setDate(week.getDate() + (days * 7));
      selectedWeek.value = week;
    }
  } catch (error) {
    console.error('Error navigating:', error);
    // 出错时重置为当前日期
    if (activeTab.value === 'day') {
      selectedDate.value = new Date().toISOString().split('T')[0];
    } else {
      selectedWeek.value = new Date();
    }
  }
};

const handleDateChange = (date) => {
  selectedDate.value = date;
};

const handleWeekChange = (week) => {
  if (week) {
    try {
      // 确保我们有一个有效的日期对象
      // 周选择器返回的是周一的日期
      selectedWeek.value = new Date(week);
    } catch (error) {
      console.error('Error handling week change:', error);
      selectedWeek.value = new Date();
    }
  }
};

const getMedications = (elderId, timeSlot, date = null) => {
  const targetDate = date || selectedDate.value;
  // 确保日期格式一致
  const formattedDate = formatDate(new Date(targetDate), 'yyyy-MM-dd');
  let filtered = medications.value.filter(med =>
    med.elderId === elderId &&
    med.time === timeSlot &&
    formatDate(new Date(med.date), 'yyyy-MM-dd') === formattedDate
  );

  // 如果选择了特定时间段，只显示该时间段的药物
  if (selectedTimeSlot.value !== 'all' && selectedTimeSlot.value !== timeSlot) {
    return [];
  }

  return filtered;
};

// 新增方法
const handleFilterChange = () => {
  // 筛选条件改变时的处理逻辑
};

const handleBuildingChange = () => {
  // 楼栋改变时的处理逻辑
};

const selectTimeSlot = (timeSlot) => {
  selectedTimeSlot.value = timeSlot;
};

const getTimeSlotStats = (timeSlot) => {
  const targetDate = selectedDate.value;
  const formattedDate = formatDate(new Date(targetDate), 'yyyy-MM-dd');

  // 获取当前筛选条件下的老人
  const currentElders = filteredElders.value;

  // 统计该时间段已服用药物的老人数量
  let count = 0;
  currentElders.forEach(elder => {
    const elderMeds = medications.value.filter(med =>
      med.elderId === elder.id &&
      med.time === timeSlot &&
      formatDate(new Date(med.date), 'yyyy-MM-dd') === formattedDate &&
      med.taken === true
    );
    if (elderMeds.length > 0) {
      count++;
    }
  });

  return count;
};

// 生命周期钩子
onMounted(() => {
  // 初始化时加载数据，使用示例日期
  selectedDate.value = '2025-06-26';
  selectedWeek.value = new Date('2025-06-26');
});
</script>

<style lang="scss" scoped>
.medication-screen {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  padding: 20px;

  .toolbar {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 24px;
    overflow: hidden;

    .toolbar-row {
      padding: 16px 24px;

      &:first-child {
        display: flex;
        justify-content: center;
        border-bottom: 1px solid #f0f2f5;
      }

      &.search-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 20px;
      }
    }

    .view-tabs {
      display: flex;

      .tab-btn {
        padding: 12px 32px;
        background: #f8f9fa;
        border: none;
        outline: none;
        cursor: pointer;
        font-size: 16px;
        font-weight: 500;
        color: #606266;
        transition: all 0.3s ease;
        border-radius: 8px;
        margin: 0 4px;

        &:hover {
          color: #409eff;
          background: #ecf5ff;
        }

        &.active {
          background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%);
          color: #fff;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }
      }
    }

    .date-navigation {
      display: flex;
      align-items: center;
      gap: 12px;

      .el-button {
        border-radius: 6px;
        font-weight: 500;

        &:hover {
          transform: translateY(-1px);
        }
      }

      .date-display {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        min-width: 200px;
        text-align: center;
        padding: 0 16px;
        background: #f8f9fa;
        border-radius: 6px;
        line-height: 32px;
      }
    }

    .search-filters {
      display: flex;
      align-items: center;
      gap: 12px;
      flex: 1;
      justify-content: center;

      .el-input,
      .el-select {
        width: 160px;
      }
    }

    .time-filter {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 8px;

      .time-buttons {
        display: flex;
        gap: 8px;

        .el-button {
          border-radius: 6px;
          font-weight: 500;

          &:hover {
            transform: translateY(-1px);
          }
        }
      }

      .time-stats {
        display: flex;
        gap: 16px;
        font-size: 13px;

        .stat-item {
          display: flex;
          align-items: center;

          .stat-label {
            color: #606266;
            font-weight: 500;
          }

          .stat-count {
            color: #409eff;
            font-weight: 600;
            margin-left: 2px;
          }
        }
      }
    }
  }

  .content {
    flex: 1;
    overflow: auto;

    .day-view {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 20px;

      .elder-card {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        overflow: hidden;
        transition: all 0.3s;

        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .elder-header {
          display: flex;
          align-items: center;
          padding: 15px 20px;
          border-bottom: 1px solid #ebeef5;

          .elder-avatar {
            margin-right: 15px;
          }

          .elder-info {
            flex: 1;

            .name {
              font-size: 16px;
              font-weight: 600;
              margin-bottom: 4px;
              color: #303133;
            }

            .room {
              font-size: 13px;
              color: #909399;
            }
          }
        }

        .medication-times {
          padding: 15px 20px 20px;

          .time-slot {
            margin-bottom: 15px;
            border-left: 3px solid;
            padding-left: 10px;

            &:last-child {
              margin-bottom: 0;
            }

            .time-label {
              font-weight: 600;
              font-size: 14px;
              margin-bottom: 8px;
            }

            .medication-list {
              .medication-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 4px 0;
                border-bottom: 1px dashed #ebeef5;
                font-size: 12px;

                &:last-child {
                  border-bottom: none;
                }

                .med-name {
                  flex: 1;
                  color: #303133;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  margin-right: 8px;
                }

                .med-right {
                  display: flex;
                  align-items: center;
                  white-space: nowrap;

                  .med-dose {
                    margin-right: 10px;
                    color: #606266;
                    font-size: 11px;
                  }

                  .med-status {
                    font-size: 11px;
                    color: #f56c6c;
                    padding: 1px 6px;
                    border-radius: 8px;
                    background-color: #fef0f0;

                    &.taken {
                      color: #67c23a;
                      background-color: #f0f9eb;
                    }
                  }
                }
              }

              .no-medication {
                color: #c0c4cc;
                font-size: 13px;
                padding: 8px 0;
              }
            }
          }
        }
      }
    }

    .week-view {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      overflow: hidden;

      ::v-deep .el-table {
        th {
          background-color: #f5f7fa;
          color: #303133;
          font-weight: 600;
        }

        .elder-cell {
          display: flex;
          align-items: center;

          .el-avatar {
            margin-right: 10px;
          }

          .elder-details {
            .name {
              font-weight: 600;
              margin-bottom: 2px;
            }

            .room {
              font-size: 12px;
              color: #909399;
            }
          }
        }

        .day-cell {
          padding: 5px 0;
          min-height: 120px;

          .week-time-slot {
            margin-bottom: 10px;

            &:last-child {
              margin-bottom: 0;
            }

            .time-label {
              font-size: 14px;
              font-weight: 600;
              margin-bottom: 4px;
              color: inherit;
              padding: 2px 0;
            }

            .medication-list {
              .medication-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 2px 0;
                font-size: 12px;
                margin: 1px 0;

                .med-name {
                  flex: 1;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  margin-right: 8px;
                  color: #303133;
                }

                .med-right {
                  display: flex;
                  align-items: center;

                  .med-dose {
                    margin-right: 8px;
                    color: #606266;
                  }

                  .med-status {
                    font-size: 11px;
                    color: #f56c6c;
                    padding: 1px 6px;
                    border-radius: 8px;
                    background-color: #fef0f0;
                    white-space: nowrap;

                    &.taken {
                      color: #67c23a;
                      background-color: #f0f9eb;
                    }
                  }
                }
              }

              .no-medication {
                color: #c0c4cc;
                font-size: 12px;
                text-align: center;
                padding: 5px 0;
              }
            }
          }
        }
      }
    }
  }
}

// 响应式调整
@media screen and (max-width: 1200px) {
  .day-view {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media screen and (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 15px;

    .view-tabs {
      width: 100%;
      justify-content: center;
    }

    .date-navigation {
      width: 100%;
      justify-content: center;
    }

    .search-box {
      width: 100%;
    }
  }

  .day-view {
    grid-template-columns: 1fr !important;
  }
}
</style>