<template>
  <div class="medication-screen">
    <!-- 工具栏 -->
    <div class="toolbar-2row">
      <!-- 第一行工具栏 -->
      <div class="toolbar-row toolbar-row-1">
        <div class="toolbar-left">
          <div class="view-tabs">
            <button 
              v-for="tab in tabs" 
              :key="tab.value"
              class="tab-btn"
              :class="{ active: activeTab === tab.value }"
              @click="activeTab = tab.value"
            >
              {{ tab.label }}
            </button>
          </div>
        </div>
        
        <div class="toolbar-center">
          <div class="date-navigation">
            <div class="quick-nav">
              <el-button-group>
                <el-button @click="quickNavigate('prev')" icon="ArrowLeft" size="small">上一{{ activeTab === 'day' ? '天' : '周' }}</el-button>
                <el-button @click="quickNavigate('current')" size="small">今{{ activeTab === 'day' ? '天' : '周' }}</el-button>
                <el-button @click="quickNavigate('next')" icon="ArrowRight" size="small">下一{{ activeTab === 'day' ? '天' : '周' }}</el-button>
              </el-button-group>
            </div>
            
            <div class="date-display">
              {{ activeTab === 'day' ? currentDate : currentWeekRange }}
            </div>
            
            <div class="date-picker">
              <el-date-picker
                v-if="activeTab === 'day'"
                v-model="selectedDate"
                type="date"
                placeholder="选择日期"
                size="small"
                @change="handleDateChange"
              />
              <el-date-picker
                v-else
                v-model="selectedWeek"
                type="week"
                placeholder="选择周"
                size="small"
                value-format="YYYY-MM-DD"
                @change="handleWeekChange"
              />
            </div>
          </div>
        </div>
        
        <div class="toolbar-right">
          <div class="time-buttons">
            <el-button
              v-for="timeSlot in timeSlots"
              :key="timeSlot.value"
              :class="['time-btn', timeSlot.value, { active: activeTime === timeSlot.value }]"
              @click="toggleTimeFilter(timeSlot.value)"
              size="small"
              plain
            >
              {{ timeSlot.label }}
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 第二行工具栏 -->
      <div class="toolbar-row toolbar-row-2">
        <div class="toolbar-left">
          <el-input
            v-model="nameQuery"
            placeholder="搜索老人姓名"
            clearable
            size="small"
          />
          <el-input
            v-model="roomQuery"
            placeholder="搜索房间号"
            clearable
            size="small"
          />
          <el-select v-model="selectedBuilding" placeholder="选择楼栋" clearable size="small">
            <el-option
              v-for="building in buildingOptions"
              :key="building.value"
              :label="building.label"
              :value="building.value"
            />
          </el-select>
          <el-select v-model="selectedFloor" placeholder="选择楼层" clearable size="small">
            <el-option
              v-for="floor in floorOptions"
              :key="floor.value"
              :label="floor.label"
              :value="floor.value"
            />
          </el-select>
        </div>
        
        <div class="toolbar-right">
          <div class="statistics">
            <span class="stat-item morning">早上: {{ statMorning }}人</span>
            <span class="stat-item noon">中午: {{ statNoon }}人</span>
            <span class="stat-item evening">晚上: {{ statEvening }}人</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content">
      <!-- 日视图 -->
      <div v-if="activeTab === 'day'" class="day-view" ref="dayViewContainer" @scroll="handleDayViewScroll">
        <div class="elder-cards-container">
          <div 
            v-for="elder in displayElders" 
            :key="elder.id" 
            class="elder-card"
          >
            <div class="elder-header">
              <el-avatar :src="elder.avatar" size="default" class="elder-avatar"></el-avatar>
              <div class="elder-info">
                <div class="name">{{ elder.name }}</div>
                <div class="room">{{ elder.roomNo }} - {{ elder.bedNo }}</div>
              </div>
            </div>
            
            <div class="medication-times">
              <div 
                v-for="timeSlot in timeSlots" 
                :key="timeSlot.value"
                class="time-slot"
                :style="{ borderLeftColor: timeSlot.color }"
              >
                <div class="time-label" :style="{ color: timeSlot.color }">
                  {{ timeSlot.label }}
                </div>
                <div class="medication-list">
                  <template v-if="getMedications(elder.id, timeSlot.value).length > 0">
                    <div 
                      v-for="med in getMedications(elder.id, timeSlot.value)" 
                      :key="med.id"
                      class="medication-item"
                    >
                      <span class="med-name">{{ med.name }}</span>
                      <div class="med-right">
                        <span class="med-dose">{{ med.dose }}</span>
                        <span 
                          class="med-status" 
                          :class="{ 'taken': med.taken }"
                        >
                          {{ med.taken ? '已服用' : '未服用' }}
                        </span>
                      </div>
                    </div>
                  </template>
                  <div v-else class="no-medication">暂无用药</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 滚动加载提示 -->
        <div v-if="hasMore" class="scroll-load-tip" ref="dayLoadTrigger">
          <div class="loading-indicator" v-if="loading">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>正在加载更多...</span>
          </div>
          <div v-else class="load-tip">
            滚动到底部加载更多 ({{ displayElders.length }}/{{ totalFilteredCount }})
          </div>
        </div>
        <div v-else class="load-complete-tip">
          已显示全部 {{ totalFilteredCount }} 个老人
        </div>
      </div>
      
      <!-- 周视图 -->
      <div v-else class="week-view" ref="weekViewContainer" @scroll="handleWeekViewScroll">
        <el-table
          :data="displayElders"
          style="width: 100%"
          v-loading="loading"
          element-loading-text="加载中..."
          border
          ref="weekTable"
          max-height="600"
        >
          <el-table-column prop="name" label="老人信息" width="180" fixed>
            <template #default="{ row }">
              <div class="elder-cell">
                <el-avatar :src="row.avatar" size="small"></el-avatar>
                <div class="elder-details">
                  <div class="name">{{ row.name }}</div>
                  <div class="room">{{ row.roomNo }} - {{ row.bedNo }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column 
            v-for="day in weekDays" 
            :key="day.date"
            :label="day.label"
            min-width="180"
          >
            <template #default="{ row }">
              <div class="day-cell">
                <div 
                  v-for="timeSlot in timeSlots" 
                  :key="timeSlot.value"
                  class="week-time-slot"
                >
                  <div class="time-label" :style="{ color: timeSlot.color }">
                    {{ timeSlot.label }}
                  </div>
                  <div class="medication-list">
                    <template v-if="getMedications(row.id, timeSlot.value, day.date).length > 0">
                      <div 
                        v-for="med in getMedications(row.id, timeSlot.value, day.date)" 
                        :key="med.id"
                        class="medication-item"
                      >
                        <span class="med-name">{{ med.name }}</span>
                        <div class="med-right">
                          <span class="med-dose">{{ med.dose }}</span>
                          <span 
                            class="med-status" 
                            :class="{ 'taken': med.taken }"
                          >
                            {{ med.taken ? '已服用' : '未服用' }}
                          </span>
                        </div>
                      </div>
                    </template>
                    <div v-else class="no-medication">-</div>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 滚动加载提示 -->
        <div v-if="hasMore" class="scroll-load-tip" ref="weekLoadTrigger">
          <div class="loading-indicator" v-if="loading">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>正在加载更多...</span>
          </div>
          <div v-else class="load-tip">
            滚动到底部加载更多 ({{ displayElders.length }}/{{ totalFilteredCount }})
          </div>
        </div>
        <div v-else class="load-complete-tip">
          已显示全部 {{ totalFilteredCount }} 个老人
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick, onBeforeUnmount } from 'vue';
import { Loading } from '@element-plus/icons-vue';

// 日期格式化工具函数
function formatDate(date, format = 'YYYY-MM-DD') {
  if (!date) return '';

  let d;
  if (typeof date === 'string') {
    const parts = date.split('-').map(Number);
    d = new Date(parts[0], parts[1] - 1, parts[2]);
  } else {
    d = new Date(date);
  }

  if (isNaN(d.getTime())) return '';

  const map = {
    'YYYY': d.getFullYear(),
    'yyyy': d.getFullYear(),
    'MM': (d.getMonth() + 1).toString().padStart(2, '0'),
    'DD': d.getDate().toString().padStart(2, '0'),
    'dd': d.getDate().toString().padStart(2, '0'),
    'HH': d.getHours().toString().padStart(2, '0'),
    'mm': d.getMinutes().toString().padStart(2, '0'),
    'ss': d.getSeconds().toString().padStart(2, '0')
  };

  let result = format;
  for (const [key, value] of Object.entries(map)) {
    result = result.replace(new RegExp(key, 'g'), value);
  }

  return result;
}

// 响应式状态
const activeTab = ref('day');
const selectedDate = ref(formatDate(new Date()));
const selectedWeek = ref(formatDate(new Date()));
const loading = ref(false);
const activeTime = ref('');
const nameQuery = ref('');
const roomQuery = ref('');
const selectedFloor = ref('');
const selectedBuilding = ref('');

// 分页相关
const pageSize = 10; // 默认显示10个老人
const currentLoadedCount = ref(pageSize);

// 表格高度和容器引用
const tableHeight = ref(500);
const weekViewContainer = ref(null);
const dayViewContainer = ref(null);
const dayLoadTrigger = ref(null);
const weekLoadTrigger = ref(null);

// 静态数据
const tabs = ref([
  { label: '日视图', value: 'day' },
  { label: '周视图', value: 'week' }
]);

const timeSlots = ref([
  { label: '早上', value: 'morning', color: '#6d8cff' },
  { label: '中午', value: 'noon', color: '#b07a1b' },
  { label: '晚上', value: 'evening', color: '#5e0fa3' }
]);

const floorOptions = [
  { label: '1层', value: '1' },
  { label: '2层', value: '2' },
  { label: '3层', value: '3' }
];

const buildingOptions = [
  { label: '1号楼', value: '1' },
  { label: '2号楼', value: '2' },
  { label: '3号楼', value: '3' }
];

// 轻量级老人数据生成
const generateElders = () => {
  const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十', '郑十一', '王十二'];
  const buildings = ['1', '2', '3'];
  const floors = ['1', '2', '3'];
  const beds = ['A床', 'B床', 'C床', 'D床'];

  const elders = [];

  // 只生成50个老人，减少数据量
  for (let i = 1; i <= 50; i++) {
    const building = buildings[i % buildings.length];
    const floor = floors[i % floors.length];
    const roomNum = String(Math.floor(i / 4) + 101);
    const bed = beds[i % beds.length];
    const name = names[i % names.length] + i;
    const gender = i % 2 === 0 ? 'men' : 'women';
    const avatarNum = (i % 50) + 1;

    elders.push({
      id: i,
      name: name,
      buildingNo: building,
      floorNo: floor,
      roomNo: roomNum,
      bedNo: bed,
      avatar: `https://randomuser.me/api/portraits/${gender}/${avatarNum}.jpg`
    });
  }

  return elders;
};

const elders = ref(generateElders());

// 轻量级用药数据生成
const generateMedicationData = () => {
  const today = new Date();
  const medications = [];
  let id = 1;

  // 只为前10个老人生成当前周的数据
  const limitedElders = elders.value.slice(0, 10);

  // 生成本周7天的日期
  const weekMonday = new Date(today);
  weekMonday.setDate(today.getDate() - (today.getDay() || 7) + 1);

  const weekDates = [];
  for (let i = 0; i < 7; i++) {
    const date = new Date(weekMonday);
    date.setDate(weekMonday.getDate() + i);
    weekDates.push(formatDate(date, 'yyyy-MM-dd'));
  }

  // 为每个老人生成用药数据
  limitedElders.forEach(elder => {
    weekDates.forEach(dateStr => {
      medications.push(
        { id: id++, elderId: elder.id, date: dateStr, time: 'morning', name: '阿司匹林', dose: '1片', taken: Math.random() > 0.5 },
        { id: id++, elderId: elder.id, date: dateStr, time: 'noon', name: '二甲双胍', dose: '1片', taken: Math.random() > 0.5 },
        { id: id++, elderId: elder.id, date: dateStr, time: 'evening', name: '钙片', dose: '1片', taken: Math.random() > 0.5 }
      );
    });
  });

  return medications;
};

const medications = ref(generateMedicationData());

// 动态生成更多用药数据
const generateMoreMedicationData = (elderIds) => {
  const today = new Date();
  const newMedications = [];
  let id = medications.value.length + 1;

  // 生成本周7天的日期
  const weekMonday = new Date(today);
  weekMonday.setDate(today.getDate() - (today.getDay() || 7) + 1);

  const weekDates = [];
  for (let i = 0; i < 7; i++) {
    const date = new Date(weekMonday);
    date.setDate(weekMonday.getDate() + i);
    weekDates.push(formatDate(date, 'yyyy-MM-dd'));
  }

  elderIds.forEach(elderId => {
    weekDates.forEach(dateStr => {
      newMedications.push(
        { id: id++, elderId: elderId, date: dateStr, time: 'morning', name: '阿司匹林', dose: '1片', taken: Math.random() > 0.5 },
        { id: id++, elderId: elderId, date: dateStr, time: 'noon', name: '二甲双胍', dose: '1片', taken: Math.random() > 0.5 },
        { id: id++, elderId: elderId, date: dateStr, time: 'evening', name: '钙片', dose: '1片', taken: Math.random() > 0.5 }
      );
    });
  });

  return newMedications;
};

// 已加载用药数据的老人ID集合
const loadedElderIds = ref(new Set(elders.value.slice(0, 10).map(e => e.id)));

// 计算属性
const currentDate = computed(() => {
  return formatDate(selectedDate.value, 'yyyy年MM月dd日');
});

const currentWeekRange = computed(() => {
  try {
    const monday = new Date(selectedWeek.value || new Date());
    const dayOfWeek = monday.getDay();
    if (dayOfWeek !== 1) {
      monday.setDate(monday.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1));
    }

    const sunday = new Date(monday);
    sunday.setDate(monday.getDate() + 6);

    const formatWeekDate = (date) => {
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}`;
    };

    // 计算是第几周
    const year = monday.getFullYear();
    const startOfYear = new Date(year, 0, 1);
    const dayOfYear = Math.floor((monday - startOfYear) / (24 * 60 * 60 * 1000)) + 1;
    const weekNumber = Math.ceil(dayOfYear / 7);

    return `${year}年第${weekNumber}周 (${formatWeekDate(monday)} - ${formatWeekDate(sunday)})`;
  } catch (error) {
    console.error('计算周范围时出错:', error);
    return '日期错误';
  }
});

const weekDays = computed(() => {
  const days = [];
  try {
    const monday = new Date(selectedWeek.value || new Date());
    const dayOfWeek = monday.getDay();
    if (dayOfWeek !== 1) {
      monday.setDate(monday.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1));
    }

    for (let i = 0; i < 7; i++) {
      const date = new Date(monday);
      date.setDate(monday.getDate() + i);

      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const dateStr = `${year}-${month}-${day}`;

      const weekDay = '日一二三四五六'[date.getDay()];
      const label = `${month}-${day} 周${weekDay}`;

      days.push({
        date: dateStr,
        label: label,
        isToday: isToday(dateStr)
      });
    }
  } catch (error) {
    console.error('生成周日期时出错:', error);
  }

  return days;
});

// 筛选后的老人列表
const filteredElders = computed(() => {
  let filtered = elders.value;

  // 基础筛选条件
  if (nameQuery.value) {
    const query = nameQuery.value.toLowerCase();
    filtered = filtered.filter(elder => elder.name.toLowerCase().includes(query));
  }

  if (roomQuery.value) {
    const room = roomQuery.value.toLowerCase();
    filtered = filtered.filter(elder => elder.roomNo.toLowerCase().includes(room));
  }

  if (selectedFloor.value) {
    filtered = filtered.filter(elder => elder.floorNo === selectedFloor.value);
  }

  if (selectedBuilding.value) {
    filtered = filtered.filter(elder => elder.buildingNo === selectedBuilding.value);
  }

  // 时间段筛选
  if (activeTime.value) {
    const elderIdsWithMeds = new Set();
    medications.value.forEach(med => {
      if (med.time === activeTime.value) {
        if (activeTab.value === 'day') {
          if (formatDate(new Date(med.date)) === selectedDate.value) {
            elderIdsWithMeds.add(med.elderId);
          }
        } else {
          if (isDateInSelectedWeek(med.date)) {
            elderIdsWithMeds.add(med.elderId);
          }
        }
      }
    });
    filtered = filtered.filter(elder => elderIdsWithMeds.has(elder.id));
  }

  return filtered;
});

// 当前显示的老人列表（分页）
const displayElders = computed(() => {
  return filteredElders.value.slice(0, currentLoadedCount.value);
});

// 总筛选数量
const totalFilteredCount = computed(() => filteredElders.value.length);

// 是否还有更多数据
const hasMore = computed(() => currentLoadedCount.value < totalFilteredCount.value);

// 统计数据
const statMorning = computed(() => calculateStats('morning'));
const statNoon = computed(() => calculateStats('noon'));
const statEvening = computed(() => calculateStats('evening'));

// 方法
const isToday = (date) => {
  return date === formatDate(new Date());
};

const isDateInSelectedWeek = (date) => {
  const targetDate = new Date(date);
  const weekStart = new Date(selectedWeek.value);
  weekStart.setDate(weekStart.getDate() - (weekStart.getDay() || 7) + 1);
  const weekEnd = new Date(weekStart);
  weekEnd.setDate(weekStart.getDate() + 6);

  return targetDate >= weekStart && targetDate <= weekEnd;
};

const getMedications = (elderId, timeSlot, date = null) => {
  const targetDate = date || selectedDate.value;
  const formattedDate = formatDate(new Date(targetDate), 'yyyy-MM-dd');
  return medications.value.filter(med =>
    med.elderId === elderId &&
    med.time === timeSlot &&
    formatDate(new Date(med.date), 'yyyy-MM-dd') === formattedDate
  );
};

const calculateStats = (timeSlot) => {
  const elderIdsWithMeds = new Set();
  medications.value.forEach(med => {
    if (med.time === timeSlot) {
      const isDateMatch = activeTab.value === 'day'
        ? formatDate(new Date(med.date)) === selectedDate.value
        : isDateInSelectedWeek(med.date);

      if (isDateMatch) {
        elderIdsWithMeds.add(med.elderId);
      }
    }
  });
  return elderIdsWithMeds.size;
};

// 懒加载更多数据
const loadMoreMedicationData = (elderIds) => {
  const newElderIds = elderIds.filter(id => !loadedElderIds.value.has(id));

  if (newElderIds.length > 0) {
    const newMedications = generateMoreMedicationData(newElderIds);
    medications.value.push(...newMedications);

    newElderIds.forEach(id => loadedElderIds.value.add(id));
  }
};

// 加载更多老人
const loadMore = () => {
  if (hasMore.value && !loading.value) {
    loading.value = true;

    // 模拟异步加载
    setTimeout(() => {
      const nextBatch = filteredElders.value.slice(currentLoadedCount.value, currentLoadedCount.value + pageSize);
      const nextElderIds = nextBatch.map(elder => elder.id);

      // 为新的老人生成用药数据
      loadMoreMedicationData(nextElderIds);

      currentLoadedCount.value = Math.min(currentLoadedCount.value + pageSize, totalFilteredCount.value);
      loading.value = false;
    }, 300);
  }
};

// Intersection Observer 滚动监听
const weekTable = ref(null);
let dayObserver = null;
let weekObserver = null;

// 设置 Intersection Observer
const setupIntersectionObserver = () => {
  // 创建观察器
  const observerOptions = {
    root: null,
    rootMargin: '100px',
    threshold: 0.1
  };

  const observerCallback = (entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting && hasMore.value && !loading.value) {
        console.log('触发器进入视口，开始加载更多');
        loadMore();
      }
    });
  };

  dayObserver = new IntersectionObserver(observerCallback, observerOptions);
  weekObserver = new IntersectionObserver(observerCallback, observerOptions);
};

// 开始观察
const startObserving = () => {
  nextTick(() => {
    if (activeTab.value === 'day' && dayLoadTrigger.value) {
      dayObserver?.observe(dayLoadTrigger.value);
      console.log('开始观察日视图加载触发器');
    } else if (activeTab.value === 'week' && weekLoadTrigger.value) {
      weekObserver?.observe(weekLoadTrigger.value);
      console.log('开始观察周视图加载触发器');
    }
  });
};

// 停止观察
const stopObserving = () => {
  if (dayObserver && dayLoadTrigger.value) {
    dayObserver.unobserve(dayLoadTrigger.value);
  }
  if (weekObserver && weekLoadTrigger.value) {
    weekObserver.unobserve(weekLoadTrigger.value);
  }
};

// 备用滚动处理（保留作为fallback）
const handleDayViewScroll = (event) => {
  // 这个函数保留但不使用，主要使用 Intersection Observer
};

const handleWeekViewScroll = (event) => {
  // 这个函数保留但不使用，主要使用 Intersection Observer
};

// 时间段切换
const toggleTimeFilter = (timeSlot) => {
  if (activeTime.value === timeSlot) {
    activeTime.value = '';
  } else {
    activeTime.value = timeSlot;
  }
  // 重置加载数量
  currentLoadedCount.value = pageSize;
};

// 日期处理
const handleDateChange = (date) => {
  if (date) {
    selectedDate.value = date;
  } else {
    selectedDate.value = formatDate(new Date());
  }
};

const handleWeekChange = (week) => {
  if (week) {
    selectedWeek.value = week;
  } else {
    const today = new Date();
    const monday = new Date(today);
    const dayOfWeek = today.getDay();
    monday.setDate(today.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1));
    selectedWeek.value = formatDate(monday);
  }
};

// 快速导航
const quickNavigate = (type) => {
  if (activeTab.value === 'day') {
    let date = new Date(selectedDate.value || new Date());
    switch (type) {
      case 'prev':
        date.setDate(date.getDate() - 1);
        break;
      case 'current':
        date = new Date();
        break;
      case 'next':
        date.setDate(date.getDate() + 1);
        break;
    }
    selectedDate.value = formatDate(date);
  } else {
    let monday = new Date(selectedWeek.value || new Date());
    switch (type) {
      case 'prev':
        monday.setDate(monday.getDate() - 7);
        break;
      case 'current':
        const today = new Date();
        monday = new Date(today);
        monday.setDate(today.getDate() - ((today.getDay() || 7) - 1));
        break;
      case 'next':
        monday.setDate(monday.getDate() + 7);
        break;
    }
    selectedWeek.value = formatDate(monday);
  }
};

// 初始化日期
const initializeDates = () => {
  const today = new Date();
  selectedDate.value = formatDate(today);

  const monday = new Date(today);
  const dayOfWeek = today.getDay();
  monday.setDate(today.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1));
  selectedWeek.value = formatDate(monday);
};

// 监听器
watch(activeTab, (newTab, oldTab) => {
  // 切换标签时重置加载数量
  currentLoadedCount.value = pageSize;

  // 停止旧的观察
  stopObserving();

  // 开始新的观察
  setTimeout(() => {
    startObserving();
  }, 100);

  console.log('切换标签:', oldTab, '->', newTab);
});

watch([
  selectedWeek,
  nameQuery,
  roomQuery,
  selectedFloor,
  selectedBuilding,
  activeTime
], () => {
  // 筛选条件变化时重置加载数量
  currentLoadedCount.value = pageSize;

  // 重新开始观察
  setTimeout(() => {
    stopObserving();
    startObserving();
  }, 100);
});

// 生命周期
onMounted(() => {
  initializeDates();

  // 设置 Intersection Observer
  setupIntersectionObserver();

  // 延迟开始观察，确保DOM已渲染
  setTimeout(() => {
    startObserving();
  }, 500);

  console.log('组件已挂载，初始数据:', {
    totalElders: elders.value.length,
    currentLoadedCount: currentLoadedCount.value,
    activeTab: activeTab.value
  });
});

onBeforeUnmount(() => {
  // 清理观察器
  stopObserving();
  if (dayObserver) {
    dayObserver.disconnect();
  }
  if (weekObserver) {
    weekObserver.disconnect();
  }
});
</script>

<style lang="scss" scoped>
.medication-screen {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  padding: 20px;

  .toolbar-2row {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 16px 20px;
    margin-bottom: 20px;

    .toolbar-row {
      display: flex;
      align-items: center;
      justify-content: space-between;

      &.toolbar-row-1 {
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #ebeef5;

        .toolbar-left {
          .view-tabs {
            display: flex;
            border: 1px solid #409eff;
            border-radius: 4px;
            overflow: hidden;

            .tab-btn {
              padding: 8px 24px;
              border: none;
              background: #fff;
              color: #409eff;
              cursor: pointer;
              font-size: 14px;
              transition: all 0.2s;

              &.active {
                background: #409eff;
                color: #fff;
              }

              & + .tab-btn {
                border-left: 1px solid #409eff;
              }
            }
          }
        }

        .toolbar-center {
          .date-navigation {
            display: flex;
            align-items: center;
            gap: 16px;

            .date-display {
              min-width: 150px;
              text-align: center;
              font-size: 16px;
              font-weight: 500;
              padding: 0 12px;
              background: #f5f7fa;
              border-radius: 4px;
              height: 32px;
              line-height: 32px;
            }
          }
        }

        .toolbar-right {
          .time-buttons {
            display: flex;
            gap: 8px;

            .time-btn {
              transition: all 0.2s;

              &.morning {
                color: #409eff;
                border-color: #409eff;
                &.active { background: #409eff; color: #fff; }
              }
              &.noon {
                color: #b07a1b;
                border-color: #b07a1b;
                &.active { background: #b07a1b; color: #fff; }
              }
              &.evening {
                color: #5e0fa3;
                border-color: #5e0fa3;
                &.active { background: #5e0fa3; color: #fff; }
              }
            }
          }
        }
      }

      &.toolbar-row-2 {
        .toolbar-left {
          display: flex;
          gap: 12px;

          .el-input {
            width: 200px;
          }

          .el-select {
            width: 120px;
          }
        }

        .toolbar-right {
          .statistics {
            display: flex;
            gap: 16px;
            font-size: 15px;
            font-weight: 500;

            .stat-item {
              &.morning { color: #409eff; }
              &.noon { color: #b07a1b; }
              &.evening { color: #5e0fa3; }
            }
          }
        }
      }
    }
  }

  .content {
    flex: 1;
    overflow: hidden;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .day-view {
      height: 100%;
      overflow-y: auto;
      padding: 20px;

      .elder-cards-container {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
      }

      .elder-card {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        overflow: hidden;
        transition: all 0.3s;
        border: 1px solid #ebeef5;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .elder-header {
          display: flex;
          align-items: center;
          padding: 15px 20px;
          border-bottom: 1px solid #ebeef5;
          background: #fafbfc;

          .elder-avatar {
            margin-right: 15px;
          }

          .elder-info {
            flex: 1;

            .name {
              font-size: 16px;
              font-weight: 600;
              margin-bottom: 4px;
              color: #303133;
            }

            .room {
              font-size: 13px;
              color: #909399;
            }
          }
        }

        .medication-times {
          padding: 15px 20px 20px;

          .time-slot {
            margin-bottom: 15px;
            border-left: 3px solid;
            padding-left: 10px;

            &:last-child {
              margin-bottom: 0;
            }

            .time-label {
              font-weight: 600;
              font-size: 14px;
              margin-bottom: 8px;
            }

            .medication-list {
              .medication-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 4px 0;
                border-bottom: 1px dashed #ebeef5;
                font-size: 12px;

                &:last-child {
                  border-bottom: none;
                }

                .med-name {
                  flex: 1;
                  color: #303133;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  margin-right: 8px;
                }

                .med-right {
                  display: flex;
                  align-items: center;
                  white-space: nowrap;

                  .med-dose {
                    margin-right: 10px;
                    color: #606266;
                    font-size: 11px;
                  }

                  .med-status {
                    font-size: 11px;
                    color: #f56c6c;
                    padding: 1px 6px;
                    border-radius: 8px;
                    background-color: #fef0f0;

                    &.taken {
                      color: #67c23a;
                      background-color: #f0f9eb;
                    }
                  }
                }
              }

              .no-medication {
                color: #c0c4cc;
                font-size: 13px;
                padding: 8px 0;
                text-align: center;
              }
            }
          }
        }
      }

      .scroll-load-tip {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px 0;
        color: #909399;
        font-size: 14px;

        .loading-indicator {
          display: flex;
          align-items: center;
          gap: 8px;

          .el-icon {
            font-size: 16px;
          }
        }

        .load-tip {
          text-align: center;
        }
      }

      .load-complete-tip {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px 0;
        color: #67c23a;
        font-size: 14px;
        font-weight: 500;
      }
    }

    .week-view {
      height: 100%;
      overflow-y: auto;
      padding: 20px;

      .el-table {
        height: calc(100vh - 300px);

        ::v-deep .el-table__header {
          th {
            background-color: #f5f7fa;
            color: #303133;
            font-weight: 600;
          }
        }

        ::v-deep .el-table__body {
          .elder-cell {
            display: flex;
            align-items: center;

            .el-avatar {
              margin-right: 10px;
            }

            .elder-details {
              .name {
                font-weight: 600;
                margin-bottom: 2px;
              }

              .room {
                font-size: 12px;
                color: #909399;
              }
            }
          }

          .day-cell {
            padding: 5px 0;
            min-height: 120px;

            .week-time-slot {
              margin-bottom: 10px;

              &:last-child {
                margin-bottom: 0;
              }

              .time-label {
                font-size: 14px;
                font-weight: 600;
                margin-bottom: 4px;
                padding: 2px 0;
              }

              .medication-list {
                .medication-item {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  padding: 2px 0;
                  font-size: 12px;
                  margin: 1px 0;

                  .med-name {
                    flex: 1;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    margin-right: 8px;
                    color: #303133;
                  }

                  .med-right {
                    display: flex;
                    align-items: center;

                    .med-dose {
                      margin-right: 8px;
                      color: #606266;
                    }

                    .med-status {
                      font-size: 11px;
                      color: #f56c6c;
                      padding: 1px 6px;
                      border-radius: 8px;
                      background-color: #fef0f0;
                      white-space: nowrap;

                      &.taken {
                        color: #67c23a;
                        background-color: #f0f9eb;
                      }
                    }
                  }
                }

                .no-medication {
                  color: #c0c4cc;
                  font-size: 12px;
                  text-align: center;
                  padding: 5px 0;
                }
              }
            }
          }
        }
      }

      .scroll-load-tip {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px 0;
        border-top: 1px solid #ebeef5;
        background: #fafbfc;
        margin-top: 16px;
        color: #909399;
        font-size: 14px;

        .loading-indicator {
          display: flex;
          align-items: center;
          gap: 8px;

          .el-icon {
            font-size: 16px;
          }
        }

        .load-tip {
          text-align: center;
        }
      }

      .load-complete-tip {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px 0;
        border-top: 1px solid #ebeef5;
        background: #fafbfc;
        margin-top: 16px;
        color: #67c23a;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}

// 响应式调整
@media screen and (max-width: 1200px) {
  .medication-screen {
    .toolbar-2row {
      .toolbar-row-1 {
        .toolbar-center {
          .date-navigation {
            .date-display {
              min-width: 100px;
            }
          }
        }
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .medication-screen {
    padding: 10px;

    .toolbar-2row {
      .toolbar-row {
        flex-direction: column;
        gap: 16px;

        &.toolbar-row-1 {
          .toolbar-center {
            width: 100%;
            .date-navigation {
              justify-content: center;
            }
          }

          .toolbar-right {
            width: 100%;
            .time-buttons {
              justify-content: center;
            }
          }
        }

        &.toolbar-row-2 {
          .toolbar-left {
            width: 100%;
            flex-wrap: wrap;
            justify-content: center;
          }

          .toolbar-right {
            width: 100%;
            .statistics {
              justify-content: center;
            }
          }
        }
      }
    }

    .content {
      .day-view {
        padding: 10px;

        .elder-cards-container {
          grid-template-columns: 1fr;
        }
      }

      .week-view {
        padding: 10px;
      }
    }
  }
}
</style>
