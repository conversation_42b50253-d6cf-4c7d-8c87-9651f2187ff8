<template>
  <div class="medication-screen">
    <!-- 顶部操作栏（两行） -->
    <div class="toolbar-2row">
      <!-- 第一行 -->
      <div class="toolbar-row toolbar-row-1">
        <div class="toolbar-left">
          <div class="view-tabs">
            <button 
              v-for="tab in tabs" 
              :key="tab.value"
              :class="['tab-btn', { 'active': activeTab === tab.value }]"
              @click="activeTab = tab.value"
            >
              {{ tab.label === '日视图' ? '天' : '周' }}
            </button>
          </div>
        </div>
        <div class="toolbar-center">
          <div class="date-navigation">
            <el-button-group class="quick-nav">
              <el-button type="primary" plain size="small" @click="quickNavigate('prev')">
                {{ activeTab === 'day' ? '前一天' : '上一周' }}
              </el-button>
              <el-button type="primary" plain size="small" @click="quickNavigate('current')">
                {{ activeTab === 'day' ? '今天' : '本周' }}
              </el-button>
              <el-button type="primary" plain size="small" @click="quickNavigate('next')">
                {{ activeTab === 'day' ? '后一天' : '下一周' }}
              </el-button>
            </el-button-group>
            <div class="date-display">{{ activeTab === 'day' ? currentDate : currentWeekRange }}</div>
            <el-date-picker
              v-if="activeTab === 'day'"
              v-model="selectedDate"
              type="date"
              placeholder="选择日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              size="small"
              style="width: 150px;"
              @change="handleDateChange"
            />
            <el-date-picker
              v-else
              v-model="selectedWeek"
              type="week"
              format="YYYY 第 ww 周"
              value-format="YYYY-MM-DD"
              placeholder="选择周"
              size="small"
              style="width: 150px;"
              @change="handleWeekChange"
            />
          </div>
        </div>
        <div class="toolbar-right">
          <div class="time-buttons">
            <el-button :class="['time-btn', 'morning', { active: activeTime === 'morning' }]" @click="activeTime = 'morning'">早上</el-button>
            <el-button :class="['time-btn', 'noon', { active: activeTime === 'noon' }]" @click="activeTime = 'noon'">中午</el-button>
            <el-button :class="['time-btn', 'evening', { active: activeTime === 'evening' }]" @click="activeTime = 'evening'">晚上</el-button>
          </div>
        </div>
      </div>
      <!-- 第二行 -->
      <div class="toolbar-row toolbar-row-2">
        <div class="toolbar-left">
          <el-select v-model="selectedBuilding" placeholder="楼栋选择">
            <el-option v-for="item in buildingOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select v-model="selectedFloor" placeholder="楼层选择">
            <el-option v-for="item in floorOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-input v-model="roomQuery" placeholder="请输入房间号" clearable />
          <el-input v-model="nameQuery" placeholder="请输入老人姓名" clearable />
        </div>
        <div class="toolbar-right">
          <div class="statistics">
            <span class="stat-item morning">早上：{{ statMorning }}人</span>
            <span class="stat-item noon">中午：{{ statNoon }}人</span>
            <span class="stat-item evening">晚上：{{ statEvening }}人</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content">
      <!-- 日视图 -->
      <div v-if="activeTab === 'day'" class="day-view">
        <div 
          v-for="elder in filteredElders" 
          :key="elder.id" 
          class="elder-card"
        >
          <div class="elder-header">
            <div class="elder-avatar">
              <el-avatar :src="elder.avatar" size="default"></el-avatar>
            </div>
            <div class="elder-info">
              <div class="name">{{ elder.name }}</div>
              <div class="room">{{ elder.roomNo }} - {{ elder.bedNo }}</div>
            </div>
          </div>
          
          <div class="medication-times">
            <div 
              v-for="timeSlot in timeSlots" 
              :key="timeSlot.value" 
              class="time-slot"
              :style="{ borderColor: timeSlot.color }"
            >
              <div class="time-label" :style="{ color: timeSlot.color }">
                {{ timeSlot.label }}
              </div>
              <div class="medication-list">
                <template v-if="getMedications(elder.id, timeSlot.value).length > 0">
                  <div 
                    v-for="med in getMedications(elder.id, timeSlot.value)" 
                    :key="`${elder.id}-${timeSlot.value}-${med.id || med.name || Math.random().toString(36).substr(2, 9)}`"
                    class="medication-item"
                  >
                    <span class="med-name">{{ med.name }}</span>
                    <div class="med-right">
                      <span class="med-dose">{{ med.dose }}</span>
                      <span 
                        class="med-status" 
                        :class="{ 'taken': med.taken }"
                      >
                        {{ med.taken ? '已服用' : '未服用' }}
                      </span>
                    </div>
                  </div>
                </template>
                <div v-else class="no-medication">无</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 周视图 -->
      <div v-else class="week-view">
        <el-table 
          :data="filteredElders" 
          style="width: 100%"
          v-loading="loading"
          element-loading-text="加载中..."
          border
        >
          <el-table-column prop="name" label="老人信息" width="180" fixed>
            <template #default="{ row }">
              <div class="elder-cell">
                <el-avatar :src="row.avatar" size="small"></el-avatar>
                <div class="elder-details">
                  <div class="name">{{ row.name }}</div>
                  <div class="room">{{ row.roomNo }} - {{ row.bedNo }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column 
            v-for="day in weekDays" 
            :key="day.date"
            :label="day.label"
            min-width="180"
          >
            <template #default="{ row }">
              <div class="day-cell">
                <div 
                  v-for="timeSlot in timeSlots" 
                  :key="timeSlot.value"
                  class="week-time-slot"
                >
                  <div class="time-label" :style="{ color: timeSlot.color }">
                    {{ timeSlot.label }}
                  </div>
                  <div class="medication-list">
                    <template v-if="getMedications(row.id, timeSlot.value, day.date).length > 0">
                      <div 
                        v-for="med in getMedications(row.id, timeSlot.value, day.date)" 
                        :key="`${row.id}-${timeSlot.value}-${day.date}-${med.id || med.name || Math.random().toString(36).substr(2, 9)}`"
                        class="medication-item"
                      >
                        <span class="med-name">{{ med.name }}</span>
                        <div class="med-right">
                          <span class="med-dose">{{ med.dose }}</span>
                          <span 
                            class="med-status" 
                            :class="{ 'taken': med.taken }"
                          >
                            {{ med.taken ? '已服用' : '未服用' }}
                          </span>
                        </div>
                      </div>
                    </template>
                    <div v-else class="no-medication">-</div>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';

// 日期格式化工具函数
function formatDate(date, format = 'YYYY-MM-DD') {
  if (!date) return '';

  let d;
  if (typeof date === 'string') {
    // 处理 YYYY-MM-DD 格式的日期字符串
    const parts = date.split('-').map(Number);
    d = new Date(parts[0], parts[1] - 1, parts[2]);
  } else {
    d = new Date(date);
  }

  if (isNaN(d.getTime())) return '';

  // 创建格式化映射
  const map = {
    'YYYY': d.getFullYear(),
    'yyyy': d.getFullYear(),
    'MM': (d.getMonth() + 1).toString().padStart(2, '0'),
    'DD': d.getDate().toString().padStart(2, '0'),
    'dd': d.getDate().toString().padStart(2, '0'),
    'HH': d.getHours().toString().padStart(2, '0'),
    'mm': d.getMinutes().toString().padStart(2, '0'),
    'ss': d.getSeconds().toString().padStart(2, '0')
  };

  let result = format;
  for (const [key, value] of Object.entries(map)) {
    result = result.replace(new RegExp(key, 'g'), value);
  }

  return result;
};

// 响应式状态
const activeTab = ref('day');
const selectedDate = ref(formatDate(new Date()));
const selectedWeek = ref(formatDate(new Date()));
const loading = ref(false);
const activeTime = ref('morning');
const nameQuery = ref('');
const roomQuery = ref('');
const selectedFloor = ref('');
const selectedBuilding = ref('');


// 静态数据
const tabs = ref([
  { label: '日视图', value: 'day' },
  { label: '周视图', value: 'week' }
]);

const timeSlots = ref([
  { label: '早上', value: 'morning', color: '#6d8cff' },
  { label: '中午', value: 'noon', color: '#b07a1b' },
  { label: '晚上', value: 'evening', color: '#5e0fa3' }
]);

const floorOptions = [
  { label: '1层', value: '1' },
  { label: '2层', value: '2' },
  { label: '3层', value: '3' }
];
const buildingOptions = [
  { label: '1号楼', value: '1' },
  { label: '2号楼', value: '2' },
  { label: '3号楼', value: '3' }
];


// 统计数据
const statMorning = computed(() => calculateStats('morning'));
const statNoon = computed(() => calculateStats('noon'));
const statEvening = computed(() => calculateStats('evening'));

// 模拟数据
const elders = ref([
  { 
    id: 1, 
    name: '张三', 
    roomNo: '101', 
    bedNo: 'A床',
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg'
  },
  { 
    id: 2, 
    name: '李四', 
    roomNo: '102', 
    bedNo: 'B床',
    avatar: 'https://randomuser.me/api/portraits/women/1.jpg'
  },
  { 
    id: 3, 
    name: '王五', 
    roomNo: '103', 
    bedNo: 'A床',
    avatar: 'https://randomuser.me/api/portraits/men/2.jpg'
  },
  { 
    id: 4, 
    name: '赵六', 
    roomNo: '104', 
    bedNo: 'B床',
    avatar: 'https://randomuser.me/api/portraits/women/2.jpg'
  }
]);

// 生成当前日期和周数据
const generateMedicationData = () => {
  const today = new Date();
  const todayStr = formatDate(today, 'yyyy-MM-dd');

  // 生成本周的日期
  const weekDates = [];
  const monday = new Date(today);
  monday.setDate(today.getDate() - (today.getDay() || 7) + 1);

  for (let i = 0; i < 7; i++) {
    const date = new Date(monday);
    date.setDate(monday.getDate() + i);
    weekDates.push(formatDate(date, 'yyyy-MM-dd'));
  }

  const medications = [];
  let id = 1;

  // 为每个老人生成今天和本周的用药数据
  elders.value.forEach(elder => {
    // 今天的用药数据
    medications.push(
      { id: id++, elderId: elder.id, date: todayStr, time: 'morning', name: '阿司匹林阿司匹林阿司匹林', dose: '1片', taken: true },
      { id: id++, elderId: elder.id, date: todayStr, time: 'morning', name: '维生素C', dose: '2片', taken: Math.random() > 0.5 },
      { id: id++, elderId: elder.id, date: todayStr, time: 'noon', name: '二甲双胍', dose: '1片', taken: Math.random() > 0.3 },
      { id: id++, elderId: elder.id, date: todayStr, time: 'evening', name: '钙片', dose: '1片', taken: Math.random() > 0.4 }
    );

    // 本周其他天的用药数据
    weekDates.forEach(dateStr => {
      if (dateStr !== todayStr) {
        medications.push(
          { id: id++, elderId: elder.id, date: dateStr, time: 'morning', name: '阿司匹林', dose: '1片', taken: Math.random() > 0.3 },
          { id: id++, elderId: elder.id, date: dateStr, time: 'noon', name: '二甲双胍', dose: '1片', taken: Math.random() > 0.4 },
          { id: id++, elderId: elder.id, date: dateStr, time: 'evening', name: '钙片', dose: '1片', taken: Math.random() > 0.5 }
        );
      }
    });
  });

  return medications;
};

const medications = ref(generateMedicationData());

// 计算属性
const currentDate = computed(() => {
  return formatDate(selectedDate.value, 'yyyy年MM月dd日');
});

const currentWeekRange = computed(() => {
  try {
    // selectedWeek.value 存储的是周一的日期字符串
    const monday = new Date(selectedWeek.value || new Date());

    // 如果不是周一，调整到周一
    const dayOfWeek = monday.getDay();
    if (dayOfWeek !== 1) {
      monday.setDate(monday.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1));
    }

    // 获取本周日
    const sunday = new Date(monday);
    sunday.setDate(monday.getDate() + 6);

    // 格式化日期为 MM/dd 格式
    const formatWeekDate = (date) => {
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}`;
    };

    return `${formatWeekDate(monday)} - ${formatWeekDate(sunday)}`;
  } catch (error) {
    console.error('计算周范围时出错:', error);
    return '日期错误';
  }
});

const weekDays = computed(() => {
  const days = [];
  try {
    // selectedWeek.value 存储的是周一的日期字符串
    const monday = new Date(selectedWeek.value || new Date());

    // 如果不是周一，调整到周一
    const dayOfWeek = monday.getDay();
    if (dayOfWeek !== 1) {
      monday.setDate(monday.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1));
    }

    // 生成一周的日期（周一到周日）
    for (let i = 0; i < 7; i++) {
      const date = new Date(monday);
      date.setDate(monday.getDate() + i);

      // 格式化为 YYYY-MM-DD
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const dateStr = `${year}-${month}-${day}`;

      // 格式化为 MM-DD 周X
      const weekDay = '日一二三四五六'[date.getDay()];
      const label = `${month}-${day} 周${weekDay}`;

      days.push({
        date: dateStr,
        label: label,
        isToday: isToday(dateStr)
      });
    }
  } catch (error) {
    console.error('生成周日期时出错:', error);
  }

  return days;
});

const filteredElders = computed(() => {
  if (!nameQuery.value && !roomQuery.value && !selectedFloor.value&&!selectedBuilding.value) {
    return elders.value;
  }
  
  const query = (nameQuery.value || '').toLowerCase();
  const room = (roomQuery.value || '').toLowerCase();
  const floor = (selectedFloor.value || '').toLowerCase();
  const building = (selectedBuilding.value || '').toLowerCase();

  
  return elders.value.filter(elder => 
    elder.name.toLowerCase().includes(query) ||
    elder.roomNo.includes(room) ||
    elder.bedNo.includes(floor)||elder.buildingNo.includes(building)
  );
});

// 方法

const isToday = (date) => {
  return date === formatDate(new Date());
};

const navigate = (days) => {
  try {
    if (activeTab.value === 'day') {
      const date = new Date(selectedDate.value);
      date.setDate(date.getDate() + days);
      selectedDate.value = formatDate(date, 'yyyy-MM-dd');
    } else {
      const week = new Date(selectedWeek.value);
      week.setDate(week.getDate() + (days * 7));
      selectedWeek.value = week;
    }
  } catch (error) {
    console.error('Error navigating:', error);
    // 出错时重置为当前日期
    if (activeTab.value === 'day') {
      selectedDate.value = new Date().toISOString().split('T')[0];
    } else {
      selectedWeek.value = new Date();
    }
  }
};

function handleDateChange(date) {
  console.log('Date changed:', date);
  if (date) {
    selectedDate.value = date;
  } else {
    selectedDate.value = formatDate(new Date());
  }
}

function handleWeekChange(week) {
  console.log('Week changed:', week);
  if (week) {
    // Element Plus的week picker返回的是周一的日期字符串
    selectedWeek.value = week;
  } else {
    // 重置为本周一
    const today = new Date();
    const monday = new Date(today);
    const dayOfWeek = today.getDay();
    monday.setDate(today.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1));
    selectedWeek.value = formatDate(monday);
  }
}

const getMedications = (elderId, timeSlot, date = null) => {
  const targetDate = date || selectedDate.value;
  // 确保日期格式一致
  const formattedDate = formatDate(new Date(targetDate), 'yyyy-MM-dd');
  return medications.value.filter(med => 
    med.elderId === elderId && 
    med.time === timeSlot && 
    formatDate(new Date(med.date), 'yyyy-MM-dd') === formattedDate
  );
};

// 快速导航方法
const quickNavigate = (type) => {
  if (activeTab.value === 'day') {
    let date = new Date(selectedDate.value || new Date());
    switch (type) {
      case 'prev':
        date.setDate(date.getDate() - 1);
        break;
      case 'current':
        date = new Date();
        break;
      case 'next':
        date.setDate(date.getDate() + 1);
        break;
    }
    selectedDate.value = formatDate(date);
  } else {
    // 周视图导航
    let monday = new Date(selectedWeek.value || new Date());

    switch (type) {
      case 'prev':
        monday.setDate(monday.getDate() - 7);
        break;
      case 'current':
        const today = new Date();
        monday = new Date(today);
        monday.setDate(today.getDate() - ((today.getDay() || 7) - 1));
        break;
      case 'next':
        monday.setDate(monday.getDate() + 7);
        break;
    }
    selectedWeek.value = formatDate(monday);
  }
};

// 根据时间段筛选的计算属性
const filteredMedications = computed(() => {
  return medications.value.filter(med => {
    // 日期匹配
    const isDateMatch = activeTab.value === 'day' 
      ? med.date === selectedDate.value
      : isDateInSelectedWeek(med.date);
    
    // 时间段匹配
    const isTimeMatch = !activeTime.value || med.time === activeTime.value;
    
    return isDateMatch && isTimeMatch;
  });
});

const calculateStats = (timeSlot) => {
  return filteredMedications.value.filter(med => med.time === timeSlot).length;
};

// 判断日期是否在选中的周内
const isDateInSelectedWeek = (date) => {
  const targetDate = new Date(date);
  const weekStart = new Date(selectedWeek.value);
  weekStart.setDate(weekStart.getDate() - (weekStart.getDay() || 7) + 1);
  const weekEnd = new Date(weekStart);
  weekEnd.setDate(weekStart.getDate() + 6);
  
  return targetDate >= weekStart && targetDate <= weekEnd;
};

// 初始化日期函数
function initializeDates() {
  const today = new Date();

  // 设置当天日期
  selectedDate.value = formatDate(today);

  // 获取本周一的日期作为周视图的初始值
  const monday = new Date(today);
  const dayOfWeek = today.getDay();
  monday.setDate(today.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1));
  selectedWeek.value = formatDate(monday);
}

// 监听标签切换
watch(activeTab, (newTab, oldTab) => {
  if (newTab !== oldTab) {
    // 标签切换时，确保日期控件显示正确的值
    if (newTab === 'day') {
      // 切换到天视图时，确保selectedDate有值
      if (!selectedDate.value) {
        selectedDate.value = formatDate(new Date());
      }
    } else {
      // 切换到周视图时，确保selectedWeek是周一的日期
      if (!selectedWeek.value) {
        const today = new Date();
        const monday = new Date(today);
        const dayOfWeek = today.getDay();
        monday.setDate(today.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1));
        selectedWeek.value = formatDate(monday);
      }
    }
  }
});

// 生命周期钩子
onMounted(() => {
  // 初始化日期
  initializeDates();
});
</script>

<style lang="scss" scoped>
.medication-screen {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  padding: 20px;
  
  .toolbar-2row {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 16px 20px;
    margin-bottom: 20px; // 增加与内容区的间距
    
    .toolbar-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      &.toolbar-row-1 {
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #ebeef5;
        
        .toolbar-left {
          .view-tabs {
            display: flex;
            border: 1px solid #409eff;
            border-radius: 4px;
            overflow: hidden;
            
            .tab-btn {
              padding: 8px 24px;
              border: none;
              background: #fff;
              color: #409eff;
              cursor: pointer;
              font-size: 14px;
              
              &.active {
                background: #409eff;
                color: #fff;
              }
              
              & + .tab-btn {
                border-left: 1px solid #409eff;
              }
            }
          }
        }
        
        .toolbar-center {
          .date-navigation {
            display: flex;
            align-items: center;
            gap: 16px;
            
            .quick-nav {
              .el-button {
                padding: 8px 12px;
                &:not(:last-child) {
                  margin-right: -1px;
                }
              }
            }
            
            .date-display {
              min-width: 150px;
              text-align: center;
              font-size: 16px;
              font-weight: 500;
              padding: 0 12px;
              background: #f5f7fa;
              border-radius: 4px;
              height: 32px;
              line-height: 32px;
            }
          }
        }
        
        .toolbar-right {
          .time-buttons {
            display: flex;
            gap: 8px;
            
            .time-btn {
              padding: 8px 16px;
              border-radius: 4px;
              
              &.morning {
                color: #409eff;
                border-color: #409eff;
                &.active { background: #409eff; color: #fff; }
              }
              &.noon {
                color: #b07a1b;
                border-color: #b07a1b;
                &.active { background: #b07a1b; color: #fff; }
              }
              &.evening {
                color: #5e0fa3;
                border-color: #5e0fa3;
                &.active { background: #5e0fa3; color: #fff; }
              }
            }
          }
        }
      }
      
      &.toolbar-row-2 {
        .toolbar-left {
          display: flex;
          gap: 12px;
          
          .el-input {
            width: 200px;
          }
          
          .el-select {
            width: 120px;
          }
        }
        
        .toolbar-right {
          .statistics {
            display: flex;
            gap: 16px;
            font-size: 15px;
            font-weight: 500;
            
            .stat-item {
              &.morning { color: #409eff; }
              &.noon { color: #b07a1b; }
              &.evening { color: #5e0fa3; }
            }
          }
        }
      }
    }
  }
  
  .content {
    flex: 1;
    overflow: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 20px;
    
    .day-view {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
      padding: 10px;
      
      .elder-card {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        overflow: hidden;
        transition: all 0.3s;
        
        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .elder-header {
          display: flex;
          align-items: center;
          padding: 15px 20px;
          border-bottom: 1px solid #ebeef5;
          
          .elder-avatar {
            margin-right: 15px;
          }
          
          .elder-info {
            flex: 1;
            
            .name {
              font-size: 16px;
              font-weight: 600;
              margin-bottom: 4px;
              color: #303133;
            }
            
            .room {
              font-size: 13px;
              color: #909399;
            }
          }
        }
        
        .medication-times {
          padding: 15px 20px 20px;
          
          .time-slot {
            margin-bottom: 15px;
            border-left: 3px solid;
            padding-left: 10px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .time-label {
              font-weight: 600;
              font-size: 14px;
              margin-bottom: 8px;
            }
            
            .medication-list {
              .medication-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 4px 0;
                border-bottom: 1px dashed #ebeef5;
                font-size: 12px;
                
                &:last-child {
                  border-bottom: none;
                }
                
                .med-name {
                  flex: 1;
                  color: #303133;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  margin-right: 8px;
                }
                
                .med-right {
                  display: flex;
                  align-items: center;
                  white-space: nowrap;
                  
                  .med-dose {
                    margin-right: 10px;
                    color: #606266;
                    font-size: 11px;
                  }
                  
                  .med-status {
                    font-size: 11px;
                    color: #f56c6c;
                    padding: 1px 6px;
                    border-radius: 8px;
                    background-color: #fef0f0;
                    
                    &.taken {
                      color: #67c23a;
                      background-color: #f0f9eb;
                    }
                  }
                }
              }
              
              .no-medication {
                color: #c0c4cc;
                font-size: 13px;
                padding: 8px 0;
              }
            }
          }
        }
      }
    }
    
    .week-view {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      overflow: hidden;
      
      ::v-deep .el-table {
        th {
          background-color: #f5f7fa;
          color: #303133;
          font-weight: 600;
        }
        
        .elder-cell {
          display: flex;
          align-items: center;
          
          .el-avatar {
            margin-right: 10px;
          }
          
          .elder-details {
            .name {
              font-weight: 600;
              margin-bottom: 2px;
            }
            
            .room {
              font-size: 12px;
              color: #909399;
            }
          }
        }
        
        .day-cell {
          padding: 5px 0;
          min-height: 120px;
          
          .week-time-slot {
            margin-bottom: 10px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .time-label {
              font-size: 14px;
              font-weight: 600;
              margin-bottom: 4px;
              color: inherit;
              padding: 2px 0;
            }
            
            .medication-list {
              .medication-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 2px 0;
                font-size: 12px;
                margin: 1px 0;
                
                .med-name {
                  flex: 1;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  margin-right: 8px;
                  color: #303133;
                }
                
                .med-right {
                  display: flex;
                  align-items: center;
                  
                  .med-dose {
                    margin-right: 8px;
                    color: #606266;
                  }
                  
                  .med-status {
                    font-size: 11px;
                    color: #f56c6c;
                    padding: 1px 6px;
                    border-radius: 8px;
                    background-color: #fef0f0;
                    white-space: nowrap;
                    
                    &.taken {
                      color: #67c23a;
                      background-color: #f0f9eb;
                    }
                  }
                }
              }
              
              .no-medication {
                color: #c0c4cc;
                font-size: 12px;
                text-align: center;
                padding: 5px 0;
              }
            }
          }
        }
      }
    }
  }
}

// 响应式调整
@media screen and (max-width: 1200px) {
  .toolbar-2row {
    .toolbar-row-1 {
      .toolbar-center {
        .date-navigation {
          .date-display {
            min-width: 100px;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .toolbar-2row {
    .toolbar-row {
      flex-direction: column;
      gap: 16px;
      
      &.toolbar-row-1 {
        .toolbar-center {
          width: 100%;
          .date-navigation {
            justify-content: center;
          }
        }
        
        .toolbar-right {
          width: 100%;
          .time-buttons {
            justify-content: center;
          }
        }
      }
      
      &.toolbar-row-2 {
        .toolbar-left {
          width: 100%;
          flex-wrap: wrap;
          justify-content: center;
        }
        
        .toolbar-right {
          width: 100%;
          .statistics {
            justify-content: center;
          }
        }
      }
    }
  }
}
</style>