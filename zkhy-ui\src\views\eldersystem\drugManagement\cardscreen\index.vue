<template>
  <div class="medication-screen">
    <!-- 顶部操作栏 -->
    <div class="toolbar">
      <div class="view-tabs">
        <button 
          v-for="tab in tabs" 
          :key="tab.value"
          :class="['tab-btn', { 'active': activeTab === tab.value }]"
          @click="activeTab = tab.value"
        >
          {{ tab.label }}
        </button>
      </div>
      
      <div class="date-navigation">
        <el-button type="text" @click="navigate(-1)">
          <i class="el-icon-arrow-left"></i>
        </el-button>
        <div class="date-display">
          {{ activeTab === 'day' ? currentDate : currentWeekRange }}
        </div>
        <el-button type="text" @click="navigate(1)">
          <i class="el-icon-arrow-right"></i>
        </el-button>
        <el-date-picker
          v-if="activeTab === 'day'"
          v-model="selectedDate"
          type="date"
          placeholder="选择日期"
          value-format="yyyy-MM-dd"
          @change="handleDateChange"
        />
        <el-date-picker
          v-else
          v-model="selectedWeek"
          type="week"
          format="YYYY 第 ww 周"
          value-format="YYYY-MM-DD"
          placeholder="选择周"
          @change="handleWeekChange"
        />
      </div>
      
      <div class="search-box">
        <el-input
          v-model="searchQuery"
          placeholder="搜索老人姓名/房间号"
          prefix-icon="el-icon-search"
          clearable
        />
      </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content">
      <!-- 日视图 -->
      <div v-if="activeTab === 'day'" class="day-view">
        <div 
          v-for="elder in filteredElders" 
          :key="elder.id" 
          class="elder-card"
        >
          <div class="elder-header">
            <div class="elder-avatar">
              <el-avatar :src="elder.avatar" size="medium"></el-avatar>
            </div>
            <div class="elder-info">
              <div class="name">{{ elder.name }}</div>
              <div class="room">{{ elder.roomNo }} - {{ elder.bedNo }}</div>
            </div>
          </div>
          
          <div class="medication-times">
            <div 
              v-for="timeSlot in timeSlots" 
              :key="timeSlot.value" 
              class="time-slot"
              :style="{ borderColor: timeSlot.color }"
            >
              <div class="time-label" :style="{ color: timeSlot.color }">
                {{ timeSlot.label }}
              </div>
              <div class="medication-list">
                <template v-if="getMedications(elder.id, timeSlot.value).length > 0">
                  <div 
                    v-for="med in getMedications(elder.id, timeSlot.value)" 
                    :key="med.id"
                    class="medication-item"
                  >
                    <span class="med-name">{{ med.name }}</span>
                    <div class="med-right">
                      <span class="med-dose">{{ med.dose }}</span>
                      <span 
                        class="med-status" 
                        :class="{ 'taken': med.taken }"
                      >
                        {{ med.taken ? '已服用' : '未服用' }}
                      </span>
                    </div>
                  </div>
                </template>
                <div v-else class="no-medication">无</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 周视图 -->
      <div v-else class="week-view">
        <el-table 
          :data="filteredElders" 
          style="width: 100%"
          v-loading="loading"
          element-loading-text="加载中..."
          border
        >
          <el-table-column prop="name" label="老人信息" width="180" fixed>
            <template #default="{ row }">
              <div class="elder-cell">
                <el-avatar :src="row.avatar" size="small"></el-avatar>
                <div class="elder-details">
                  <div class="name">{{ row.name }}</div>
                  <div class="room">{{ row.roomNo }} - {{ row.bedNo }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column 
            v-for="day in weekDays" 
            :key="day.date"
            :label="day.label"
            min-width="180"
          >
            <template #default="{ row }">
              <div class="day-cell">
                <div 
                  v-for="timeSlot in timeSlots" 
                  :key="timeSlot.value"
                  class="week-time-slot"
                >
                  <div class="time-label" :style="{ color: timeSlot.color }">
                    {{ timeSlot.label }}
                  </div>
                  <div class="medication-list">
                    <template v-if="getMedications(row.id, timeSlot.value, day.date).length > 0">
                      <div 
                        v-for="med in getMedications(row.id, timeSlot.value, day.date)" 
                        :key="med.id"
                        class="medication-item"
                      >
                        <span class="med-name">{{ med.name }}</span>
                        <div class="med-right">
                          <span class="med-dose">{{ med.dose }}</span>
                          <span 
                            class="med-status" 
                            :class="{ 'taken': med.taken }"
                          >
                            {{ med.taken ? '已服用' : '未服用' }}
                          </span>
                        </div>
                      </div>
                    </template>
                    <div v-else class="no-medication">-</div>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 响应式状态
const activeTab = ref('day');
const searchQuery = ref('');
const selectedDate = ref(new Date().toISOString().split('T')[0]);
const selectedWeek = ref(new Date());
const loading = ref(false);

// 静态数据
const tabs = ref([
  { label: '日视图', value: 'day' },
  { label: '周视图', value: 'week' }
]);

const timeSlots = ref([
  { label: '早上', value: 'morning', color: '#6d8cff' },
  { label: '中午', value: 'noon', color: '#b07a1b' },
  { label: '晚上', value: 'evening', color: '#5e0fa3' }
]);

// 模拟数据
const elders = ref([
  { 
    id: 1, 
    name: '张三', 
    roomNo: '101', 
    bedNo: 'A床',
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg'
  },
  { 
    id: 2, 
    name: '李四', 
    roomNo: '102', 
    bedNo: 'B床',
    avatar: 'https://randomuser.me/api/portraits/women/1.jpg'
  },
  { 
    id: 3, 
    name: '王五', 
    roomNo: '103', 
    bedNo: 'A床',
    avatar: 'https://randomuser.me/api/portraits/men/2.jpg'
  },
  { 
    id: 4, 
    name: '赵六', 
    roomNo: '104', 
    bedNo: 'B床',
    avatar: 'https://randomuser.me/api/portraits/women/2.jpg'
  }
]);

const medications = ref([
  { id: 1, elderId: 1, date: '2025-05-27', time: 'morning', name: '阿司匹林', dose: '1片', taken: true },
  { id: 2, elderId: 1, date: '2025-05-27', time: 'morning', name: '维生素C', dose: '2片', taken: true },
  { id: 3, elderId: 1, date: '2025-05-27', time: 'noon', name: '二甲双胍', dose: '1片', taken: false },
  { id: 4, elderId: 1, date: '2025-05-27', time: 'evening', name: '钙片', dose: '1片', taken: false },
  { id: 5, elderId: 2, date: '2025-05-27', time: 'morning', name: '降压药', dose: '1片', taken: true },
  { id: 6, elderId: 2, date: '2025-05-28', time: 'morning', name: '降压药', dose: '1片', taken: false },
  { id: 7, elderId: 1, date: '2025-05-30', time: 'morning', name: '阿司匹林', dose: '1片', taken: true },
  { id: 8, elderId: 1, date: '2025-05-30', time: 'morning', name: '维生素C', dose: '2片', taken: true },
  { id: 9, elderId: 1, date: '2025-05-30', time: 'noon', name: '二甲双胍', dose: '1片', taken: false },
  { id: 10, elderId: 1, date: '2025-05-30', time: 'evening', name: '钙片', dose: '1片', taken: false },
]);

// 计算属性
const currentDate = computed(() => {
  return formatDate(selectedDate.value, 'yyyy年MM月dd日');
});

const currentWeekRange = computed(() => {
  try {
    // 确保使用正确的日期对象
    const current = new Date(selectedWeek.value || new Date());
    // 获取本周一
    const start = new Date(current);
    start.setDate(start.getDate() - (start.getDay() || 7) + 1);
    
    // 获取本周日
    const end = new Date(start);
    end.setDate(start.getDate() + 6);
    
    // 格式化日期为 MM/dd 格式
    const formatDate = (date) => {
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}`;
    };
    
    return `${formatDate(start)} - ${formatDate(end)}`;
  } catch (error) {
    console.error('计算周范围时出错:', error);
    return '日期错误';
  }
});

const weekDays = computed(() => {
  const days = [];
  try {
    // 确保使用正确的日期对象
    const current = new Date(selectedWeek.value || new Date());
    // 获取本周一
    const firstDay = new Date(current);
    firstDay.setDate(firstDay.getDate() - (firstDay.getDay() || 7) + 1);
    
    // 生成一周的日期（周一到周日）
    for (let i = 0; i < 7; i++) {
      const date = new Date(firstDay);
      date.setDate(date.getDate() + i);
      
      // 格式化为 YYYY-MM-DD
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const dateStr = `${year}-${month}-${day}`;
      
      // 格式化为 MM-DD 周X
      const weekDay = '日一二三四五六'[date.getDay()];
      const label = `${month}-${day} 周${weekDay}`;
      
      days.push({
        date: dateStr,
        label: label,
        isToday: isToday(dateStr)
      });
    }
  } catch (error) {
    console.error('生成周日期时出错:', error);
  }
  
  return days;
});

const filteredElders = computed(() => {
  if (!searchQuery.value) {
    return elders.value;
  }
  
  const query = searchQuery.value.toLowerCase();
  return elders.value.filter(elder => 
    elder.name.toLowerCase().includes(query) ||
    elder.roomNo.includes(query) ||
    elder.bedNo.includes(query)
  );
});

// 方法
const formatDate = (date, format) => {
  if (!date) return '';
  
  let d;
  if (typeof date === 'string') {
    // 处理 YYYY-MM-DD 格式的日期字符串
    const parts = date.split('-').map(Number);
    d = new Date(parts[0], parts[1] - 1, parts[2]);
  } else {
    d = new Date(date);
  }
  
  if (isNaN(d.getTime())) return '';
  
  // 创建格式化映射
  const map = {
    'yyyy': d.getFullYear(),
    'MM': (d.getMonth() + 1).toString().padStart(2, '0'),
    'dd': d.getDate().toString().padStart(2, '0'),
    'HH': d.getHours().toString().padStart(2, '0'),
    'mm': d.getMinutes().toString().padStart(2, '0'),
    'ss': d.getSeconds().toString().padStart(2, '0')
  };
  
  let result = format;
  for (const [key, value] of Object.entries(map)) {
    result = result.replace(new RegExp(key, 'g'), value);
  }
  
  return result;
};

const isToday = (date) => {
  return date === formatDate(new Date(), 'yyyy-MM-dd');
};

const navigate = (days) => {
  try {
    if (activeTab.value === 'day') {
      const date = new Date(selectedDate.value);
      date.setDate(date.getDate() + days);
      selectedDate.value = formatDate(date, 'yyyy-MM-dd');
    } else {
      const week = new Date(selectedWeek.value);
      week.setDate(week.getDate() + (days * 7));
      selectedWeek.value = week;
    }
  } catch (error) {
    console.error('Error navigating:', error);
    // 出错时重置为当前日期
    if (activeTab.value === 'day') {
      selectedDate.value = new Date().toISOString().split('T')[0];
    } else {
      selectedWeek.value = new Date();
    }
  }
};

const handleDateChange = (date) => {
  selectedDate.value = date;
};

const handleWeekChange = (week) => {
  if (week) {
    try {
      // 确保我们有一个有效的日期对象
      // 周选择器返回的是周一的日期
      selectedWeek.value = new Date(week);
    } catch (error) {
      console.error('Error handling week change:', error);
      selectedWeek.value = new Date();
    }
  }
};

const getMedications = (elderId, timeSlot, date = null) => {
  const targetDate = date || selectedDate.value;
  // 确保日期格式一致
  const formattedDate = formatDate(new Date(targetDate), 'yyyy-MM-dd');
  return medications.value.filter(med => 
    med.elderId === elderId && 
    med.time === timeSlot && 
    formatDate(new Date(med.date), 'yyyy-MM-dd') === formattedDate
  );
};

// 生命周期钩子
onMounted(() => {
  // 初始化时加载数据
  selectedDate.value = new Date().toISOString().split('T')[0];
  selectedWeek.value = new Date();
});
</script>

<style lang="scss" scoped>
.medication-screen {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  padding: 20px;
  
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    background: #fff;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    
    .view-tabs {
      display: flex;
      border-radius: 4px;
      overflow: hidden;
      border: 1px solid #dcdfe6;
      
      .tab-btn {
        padding: 8px 20px;
        background: #fff;
        border: none;
        outline: none;
        cursor: pointer;
        font-size: 14px;
        color: #606266;
        transition: all 0.3s;
        
        &:hover {
          color: #409eff;
        }
        
        &.active {
          background: #409eff;
          color: #fff;
        }
        
        & + .tab-btn {
          border-left: 1px solid #dcdfe6;
        }
      }
    }
    
    .date-navigation {
      display: flex;
      align-items: center;
      
      .el-button {
        font-size: 16px;
        padding: 8px 10px;
      }
      
      .date-display {
        margin: 0 15px;
        font-size: 16px;
        font-weight: 500;
        min-width: 280px;
        text-align: center;
      }
    }
    
    .search-box {
      width: 250px;
      
      ::v-deep .el-input__inner {
        border-radius: 20px;
        padding-left: 35px;
      }
      
      ::v-deep .el-input__prefix {
        left: 10px;
      }
    }
  }
  
  .content {
    flex: 1;
    overflow: auto;
    
    .day-view {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 20px;
      
      .elder-card {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        overflow: hidden;
        transition: all 0.3s;
        
        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .elder-header {
          display: flex;
          align-items: center;
          padding: 15px 20px;
          border-bottom: 1px solid #ebeef5;
          
          .elder-avatar {
            margin-right: 15px;
          }
          
          .elder-info {
            flex: 1;
            
            .name {
              font-size: 16px;
              font-weight: 600;
              margin-bottom: 4px;
              color: #303133;
            }
            
            .room {
              font-size: 13px;
              color: #909399;
            }
          }
        }
        
        .medication-times {
          padding: 15px 20px 20px;
          
          .time-slot {
            margin-bottom: 15px;
            border-left: 3px solid;
            padding-left: 10px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .time-label {
              font-weight: 600;
              font-size: 14px;
              margin-bottom: 8px;
            }
            
            .medication-list {
              .medication-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 4px 0;
                border-bottom: 1px dashed #ebeef5;
                font-size: 12px;
                
                &:last-child {
                  border-bottom: none;
                }
                
                .med-name {
                  flex: 1;
                  color: #303133;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  margin-right: 8px;
                }
                
                .med-right {
                  display: flex;
                  align-items: center;
                  white-space: nowrap;
                  
                  .med-dose {
                    margin-right: 10px;
                    color: #606266;
                    font-size: 11px;
                  }
                  
                  .med-status {
                    font-size: 11px;
                    color: #f56c6c;
                    padding: 1px 6px;
                    border-radius: 8px;
                    background-color: #fef0f0;
                    
                    &.taken {
                      color: #67c23a;
                      background-color: #f0f9eb;
                    }
                  }
                }
              }
              
              .no-medication {
                color: #c0c4cc;
                font-size: 13px;
                padding: 8px 0;
              }
            }
          }
        }
      }
    }
    
    .week-view {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      overflow: hidden;
      
      ::v-deep .el-table {
        th {
          background-color: #f5f7fa;
          color: #303133;
          font-weight: 600;
        }
        
        .elder-cell {
          display: flex;
          align-items: center;
          
          .el-avatar {
            margin-right: 10px;
          }
          
          .elder-details {
            .name {
              font-weight: 600;
              margin-bottom: 2px;
            }
            
            .room {
              font-size: 12px;
              color: #909399;
            }
          }
        }
        
        .day-cell {
          padding: 5px 0;
          min-height: 120px;
          
          .week-time-slot {
            margin-bottom: 10px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .time-label {
              font-size: 14px;
              font-weight: 600;
              margin-bottom: 4px;
              color: inherit;
              padding: 2px 0;
            }
            
            .medication-list {
              .medication-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 2px 0;
                font-size: 12px;
                margin: 1px 0;
                
                .med-name {
                  flex: 1;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  margin-right: 8px;
                  color: #303133;
                }
                
                .med-right {
                  display: flex;
                  align-items: center;
                  
                  .med-dose {
                    margin-right: 8px;
                    color: #606266;
                  }
                  
                  .med-status {
                    font-size: 11px;
                    color: #f56c6c;
                    padding: 1px 6px;
                    border-radius: 8px;
                    background-color: #fef0f0;
                    white-space: nowrap;
                    
                    &.taken {
                      color: #67c23a;
                      background-color: #f0f9eb;
                    }
                  }
                }
              }
              
              .no-medication {
                color: #c0c4cc;
                font-size: 12px;
                text-align: center;
                padding: 5px 0;
              }
            }
          }
        }
      }
    }
  }
}

// 响应式调整
@media screen and (max-width: 1200px) {
  .day-view {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media screen and (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 15px;
    
    .view-tabs {
      width: 100%;
      justify-content: center;
    }
    
    .date-navigation {
      width: 100%;
      justify-content: center;
    }
    
    .search-box {
      width: 100%;
    }
  }
  
  .day-view {
    grid-template-columns: 1fr !important;
  }
}
</style>