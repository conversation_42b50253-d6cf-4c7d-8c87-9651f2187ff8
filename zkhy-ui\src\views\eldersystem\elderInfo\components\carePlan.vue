<template>
  <el-card shadow="never">
    <div class="cardDetailTop">
      <div
        :class="isSelectTopTag == '01' ? 'cardDetailTopDivSelect' : ''"
        class="cardDetailTopDiv"
        @click="showTagDetail('01')"
      >
        护理记录
      </div>
      <div
        :class="isSelectTopTag == '02' ? 'cardDetailTopDivSelect' : ''"
        class="cardDetailTopDiv"
        @click="showTagDetail('02')"
      >
        照护记录
      </div>
      <div
        :class="isSelectTopTag == '03' ? 'cardDetailTopDivSelect' : ''"
        class="cardDetailTopDiv"
        @click="showTagDetail('03')"
      >
        服药记录
      </div>
      <div
        :class="isSelectTopTag == '04' ? 'cardDetailTopDivSelect' : ''"
        class="cardDetailTopDiv"
        @click="showTagDetail('04')"
      >
        用餐记录
      </div>
    </div>
    <el-row>
      <el-col :span="24">
        <div v-if="isSelectTopTag == '01'" style="margin-top: 20px">
          <el-form
            v-show="showSearch"
            ref="queryRef1"
            :inline="true"
            :model="queryParams1"
            label-width="68px"
          >
            <el-row :gutter="15">
              <el-col :span="6">
                <el-form-item label="护理人员" prop="nursingStaffName">
                  <el-input
                    v-model="queryParams1.nursingStaffName"
                    clearable
                    placeholder="请输入护理人员"
                    @keyup.enter="handleQuery1"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="护理日期" prop="recordTime">
                  <el-date-picker
                    v-model="queryParams1.recordTime"
                    clearable
                    placeholder="请选择护理日期"
                    type="date"
                    value-format="YYYY-MM-DD "
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="护理类型" prop="nursingType">
                  <el-select
                    v-model="queryParams1.nursingType"
                    :disabled="isShowOrEdit"
                    clearable
                    placeholder="请选择"
                    style="width: 200px"
                    @keyup.enter="handleQuery1"
                  >
                    <el-option
                      v-for="dict in nursing_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="护理名称" prop="nursingItemName">
                  <el-input
                    v-model="queryParams1.nursingItemName"
                    clearable
                    placeholder="请输入护理名称"
                    @keyup.enter="handleQuery1"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-form-item>
                <el-button icon="Search" type="primary" @click="handleQuery1"
                  >搜索</el-button
                >
                <el-button icon="Refresh" @click="resetQuery1">重置</el-button>
                <el-button
                  icon="plus"
                  plain
                  type="primary"
                  @click="handleAdd1"
                  v-show="!props.isShow"
                  >新增</el-button
                >
              </el-form-item>
            </el-row>
          </el-form>
          <el-table v-loading="loading1" :data="careRecordList" border stripe>
            <el-table-column align="center" type="index" width="55" />
            <el-table-column
              align="center"
              label="护理日期"
              prop="recordTime"
              width="180"
            >
              <template #default="scope">
                <span>{{ parseTime(scope.row.recordTime, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="护理时间"
              prop="recordTime"
              width="180"
            >
              <template #default="scope">
                <span>{{ parseTime(scope.row.recordTime, "{h}:{m}") }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="护理名称" prop="nursingItemName" />
            <el-table-column align="center" label="护理类型" prop="nursingType">
              <template #default="scope">
                <dict-tag :options="nursing_type" :value="scope.row.nursingType" />
              </template>
            </el-table-column>
            <el-table-column align="center" label="护理人员" prop="nursingStaffName" />
            <el-table-column align="center" label="备注" prop="remark" />
            <el-table-column
              v-if="false"
              align="center"
              class-name="small-padding fixed-width"
              label="操作"
            >
              <template #default="scope">
                <el-button
                  icon="Edit"
                  link
                  type="primary"
                  @click="handleUpdate1(scope.row)"
                  >修改</el-button
                >
                <el-button
                  v-if="false"
                  icon="Delete"
                  link
                  type="primary"
                  @click="handleDelete1(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total1 > 0"
            v-model:limit="queryParams1.pageSize"
            v-model:page="queryParams1.pageNum"
            :total="total1"
            @pagination="getList1"
          />
          <!-- 添加或修改护理记录对话框 -->
          <el-dialog v-model="open1" :title="title1" append-to-body width="50%">
            <el-form
              ref="nursingRecordRef"
              :model="form1"
              :rules="rules1"
              label-width="120px"
            >
              <el-row>
                <el-col :span="12">
                  <el-form-item label="护理记录时间" prop="recordTime">
                    <el-date-picker
                      v-model="form1.recordTime"
                      clearable
                      placeholder="请选择护理记录时间"
                      style="width: 100%"
                      type="datetime"
                      value-format="YYYY-MM-DD hh:mm:ss"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="护理类型" prop="nursingType">
                    <el-select
                      v-model="form1.nursingType"
                      :disabled="isShowOrEdit"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="dict in nursing_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="护理项目名称" prop="nursingItemName">
                    <el-input
                      v-model="form1.nursingItemName"
                      placeholder="请输入护理项目名称"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="护理人员姓名" prop="nursingStaffName">
                    <el-input
                      v-model="form1.nursingStaffName"
                      placeholder="请输入护理人员姓名"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="备注" prop="remark">
                    <el-input
                      v-model="form1.remark"
                      placeholder="请输入内容"
                      type="textarea"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item v-if="false" label="护理人员工号" prop="nursingStaffCode">
                <el-input
                  v-model="form1.nursingStaffCode"
                  placeholder="请输入护理人员工号"
                />
              </el-form-item>
            </el-form>
            <template #footer>
              <div class="dialog-footer">
                <el-button type="primary" @click="submitForm1">确 定</el-button>
                <el-button @click="cancel1">取 消</el-button>
              </div>
            </template>
          </el-dialog>
        </div>
        <div v-if="isSelectTopTag == '02'" style="margin-top: 20px">
          <el-form
            v-show="showSearch"
            ref="queryRef2"
            :inline="true"
            :model="queryParams2"
            label-width="68px"
          >
            <el-row :gutter="15">
              <el-col :span="6">
                <el-form-item label="照护人员" prop="careStaffName">
                  <el-input
                    v-model="queryParams2.careStaffName"
                    clearable
                    placeholder="请输入照护人员"
                    @keyup.enter="handleQuery2"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="照护日期" prop="recordTime">
                  <el-date-picker
                    v-model="queryParams2.recordTime"
                    clearable
                    placeholder="请选择照护记录时间"
                    type="datetime"
                    value-format="YYYY-MM-DD"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="照护名称" prop="careItemName">
                  <el-input
                    v-model="queryParams2.careItemName"
                    clearable
                    placeholder="请输入照护名称"
                    @keyup.enter="handleQuery2"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item>
                  <el-button icon="Search" type="primary" @click="handleQuery2"
                    >搜索</el-button
                  >
                  <el-button icon="Refresh" @click="resetQuery2">重置</el-button>
                  <el-button
                    icon="plus"
                    plain
                    type="primary"
                    @click="handleAdd2"
                    v-show="!props.isShow"
                    >新增</el-button
                  >
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-table v-loading="loading2" :data="nursingRecordList" border stripe>
            <el-table-column align="center" type="index" width="55" />
            <el-table-column
              align="center"
              label="照护日期"
              prop="recordTime"
              width="180"
            >
              <template #default="scope">
                <span>{{ parseTime(scope.row.recordTime, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="照护时间"
              prop="recordTime"
              width="180"
            >
              <template #default="scope">
                <span>{{ parseTime(scope.row.recordTime, "{h}:{m}") }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="照护名称" prop="careItemName" />
            <el-table-column align="center" label="照护人员" prop="careStaffName" />
            <el-table-column align="center" label="备注" prop="remark" />
            <el-table-column
              v-if="false"
              align="center"
              class-name="small-padding fixed-width"
              label="操作"
            >
              <template #default="scope">
                <el-button
                  icon="Edit"
                  link
                  type="primary"
                  @click="handleUpdate2(scope.row)"
                  >修改</el-button
                >
                <el-button
                  icon="Delete"
                  link
                  type="primary"
                  @click="handleDelete2(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total2 > 0"
            v-model:limit="queryParams2.pageSize"
            v-model:page="queryParams2.pageNum"
            :total="total2"
            @pagination="getList2"
          />
          <!-- 添加或修改照护记录对话框 -->
          <el-dialog v-model="open2" :title="title2" append-to-body width="50%">
            <el-form
              ref="careRecordRef"
              :model="form2"
              :rules="rules2"
              label-width="120px"
            >
              <el-row>
                <el-col :span="12"></el-col>
              </el-row>
              <el-form-item label="照护记录时间" prop="recordTime">
                <el-date-picker
                  v-model="form2.recordTime"
                  clearable
                  placeholder="请选择照护记录时间"
                  type="datetime"
                  value-format="YYYY-MM-DD hh:mm:ss"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label="照护项目" prop="careItemName">
                <el-input v-model="form2.careItemName" placeholder="请输入照护项目" />
              </el-form-item>
              <el-form-item label="照护人员" prop="careStaffName">
                <el-input v-model="form2.careStaffName" placeholder="请输入照护人员" />
              </el-form-item>
              <el-form-item v-if="false" label="照护人员工号" prop="careStaffCode">
                <el-input
                  v-model="form2.careStaffCode"
                  placeholder="请输入照护人员工号"
                />
              </el-form-item>
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="form2.remark"
                  placeholder="请输入内容"
                  type="textarea"
                />
              </el-form-item>
            </el-form>
            <template #footer>
              <div class="dialog-footer">
                <el-button type="primary" @click="submitForm2">确 定</el-button>
                <el-button @click="cancel2">取 消</el-button>
              </div>
            </template>
          </el-dialog>
        </div>
        <div v-if="isSelectTopTag == '03'" style="margin-top: 20px">
          <el-form
            v-show="showSearch"
            ref="queryRef3"
            :inline="true"
            :model="queryParams3"
            label-width="68px"
          >
            <el-row :gutter="15">
              <el-col :span="6">
                <el-form-item label="药品名称" prop="medicationName">
                  <el-input
                    v-model="queryParams3.medicationName"
                    clearable
                    placeholder="请输入药品名称"
                    @keyup.enter="handleQuery3"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="服药日期" prop="medicationTime">
                  <el-date-picker
                    v-model="queryParams3.medicationTime"
                    clearable
                    placeholder="请选择服药日期"
                    type="date"
                    value-format="YYYY-MM-DD"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="服用剂量" prop="dosage">
                  <el-input
                    v-model="queryParams3.dosage"
                    clearable
                    placeholder="请输入服用剂量"
                    @keyup.enter="handleQuery3"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="给药时段" prop="medicationMethod">
                  <el-input
                    v-model="queryParams3.medicationMethod"
                    clearable
                    placeholder="请输入给药时段"
                    @keyup.enter="handleQuery3"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item>
                  <el-button icon="Search" type="primary" @click="handleQuery3"
                    >搜索</el-button
                  >
                  <el-button icon="Refresh" @click="resetQuery3">重置</el-button>
                  <el-button
                    icon="plus"
                    plain
                    type="primary"
                    @click="handleAdd3"
                    v-show="!props.isShow"
                    >新增</el-button
                  >
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-table v-loading="loading3" :data="medicationRecordList" border stripe>
            <el-table-column align="center" type="index" width="55" />
            <el-table-column
              align="center"
              label="服药日期"
              prop="medicationTime"
              width="180"
            >
              <template #default="scope">
                <span>{{ parseTime(scope.row.medicationTime, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="服药时间"
              prop="medicationTime"
              width="180"
            >
              <template #default="scope">
                <span>{{ parseTime(scope.row.medicationTime, "{h}:{m}") }}</span>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="服药时段"
              prop="medicationTime"
              width="180"
            >
              <template #default="scope">
                <dict-tag
                  :options="medication_period"
                  :value="scope.row.medicationMethod"
                />
              </template>
            </el-table-column>
            <el-table-column align="center" label="药品名称" prop="medicationName" />
            <el-table-column align="center" label="服用剂量" prop="dosage" />
            <el-table-column
              v-if="false"
              align="center"
              label="给药途径"
              prop="medicationMethod"
            >
              <template #default="scope">
                <dict-tag
                  :options="medication_route"
                  :value="scope.row.medicationMethod"
                />
              </template>
            </el-table-column>
            <el-table-column align="center" label="姓名" prop="executorName" />
            <el-table-column v-if="false" align="center" label="服药状态" prop="status">
              <template #default="scope">
                <dict-tag :options="medication_status" :value="scope.row.status" />
              </template>
            </el-table-column>
            <el-table-column align="center" label="备注" prop="remark" />
            <el-table-column
              v-if="false"
              align="center"
              class-name="small-padding fixed-width"
              label="操作"
            >
              <template #default="scope">
                <el-button
                  icon="Edit"
                  link
                  type="primary"
                  @click="handleUpdate3(scope.row)"
                  >修改</el-button
                >
                <el-button
                  icon="Delete"
                  link
                  type="primary"
                  @click="handleDelete3(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total3 > 0"
            v-model:limit="queryParams3.pageSize"
            v-model:page="queryParams3.pageNum"
            :total="total3"
            @pagination="getList3"
          />
          <!-- 添加或修改老人服药记录对话框 -->
          <el-dialog v-model="open3" :title="title3" append-to-body width="50%">
            <el-form
              ref="medicationRecordRef"
              :model="form3"
              :rules="rules3"
              label-width="120px"
            >
              <el-row>
                <el-col :span="12">
                  <el-form-item label="服药时间" prop="medicationTime">
                    <el-date-picker
                      v-model="form3.medicationTime"
                      clearable
                      placeholder="请选择服药时间"
                      style="width: 100%"
                      type="datetime"
                      value-format="YYYY-MM-DD hh:mm:ss"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="给药途径" prop="medicationMethod">
                    <el-select
                      v-model="form3.medicationMethod"
                      :disabled="isShowOrEdit"
                      clearable
                      placeholder="请选择"
                      style="width: 100%"
                      @keyup.enter="handleQuery1"
                    >
                      <el-option
                        v-for="dict in medication_route"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="药品名称" prop="medicationName">
                    <el-input
                      v-model="form3.medicationName"
                      placeholder="请输入药品名称"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="服用剂量" prop="dosage">
                    <el-input v-model="form3.dosage" placeholder="请输入服用剂量" />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="姓名" prop="executorName">
                    <el-input v-model="form3.executorName" placeholder="请输入姓名" />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="服药状态" prop="status">
                    <el-select
                      v-model="form3.status"
                      :disabled="isShowOrEdit"
                      clearable
                      placeholder="请选择"
                      style="width: 100%"
                      @keyup.enter="handleQuery1"
                    >
                      <el-option
                        v-for="dict in medication_status"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="备注" prop="remark">
                    <el-input
                      v-model="form3.remark"
                      placeholder="请输入内容"
                      type="textarea"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item v-if="false" label="执行人员工号" prop="executorCode">
                <el-input v-model="form3.executorCode" placeholder="请输入执行人员工号" />
              </el-form-item>
              <el-form-item v-if="false" label="执行人员ID" prop="executorId">
                <el-input v-model="form3.executorId" placeholder="请输入执行人员ID" />
              </el-form-item>
            </el-form>
            <template #footer>
              <div class="dialog-footer">
                <el-button type="primary" @click="submitForm3">确 定</el-button>
                <el-button @click="cancel3">取 消</el-button>
              </div>
            </template>
          </el-dialog>
        </div>
        <div v-if="isSelectTopTag == '04'" style="margin-top: 20px">
          <el-form
            v-show="showSearch"
            ref="queryRef4"
            :inline="true"
            :model="queryParams4"
            label-width="68px"
          >
            <el-row :gutter="15">
              <el-col :span="6">
                <el-form-item label="餐量名称" prop="mealAmount">
                  <el-input
                    v-model="queryParams4.foodItems"
                    clearable
                    placeholder="请输入实际用餐量"
                    @keyup.enter="handleQuery4"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="喂食人" prop="recorderName">
                  <el-input
                    v-model="queryParams4.recorderName"
                    clearable
                    placeholder="请输入喂食人"
                    @keyup.enter="handleQuery4"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="用餐时间" prop="mealTime">
                  <el-date-picker
                    v-model="queryParams4.mealTime"
                    clearable
                    placeholder="请选择用餐时间"
                    type="datetime"
                    value-format="YYYY-MM-DD hh:mm:ss"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="用餐时段" prop="mealType">
                  <el-select
                    v-model="form4.mealType"
                    :disabled="isShowOrEdit"
                    clearable
                    placeholder="请选择"
                    style="width: 200px"
                    @keyup.enter="handleQuery4"
                  >
                    <el-option
                      v-for="dict in dining_period"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item>
                  <el-button icon="Search" type="primary" @click="handleQuery4"
                    >搜索</el-button
                  >
                  <el-button icon="Refresh" @click="resetQuery4">重置</el-button>
                  <el-button
                    icon="Plus"
                    plain
                    type="primary"
                    @click="handleAdd4"
                    v-show="!props.isShow"
                    >新增</el-button
                  >
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-table v-loading="loading4" :data="mealRecordList" border stripe>
            <el-table-column align="center" type="index" width="55" />
            <el-table-column align="center" label="用餐日期" prop="mealTime" width="180">
              <template #default="scope">
                <span>{{ parseTime(scope.row.mealTime, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="用餐时间" prop="mealTime" width="180">
              <template #default="scope">
                <span>{{ parseTime(scope.row.mealTime, "{h}:{m}") }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="餐次" prop="mealType">
              <template #default="scope">
                <dict-tag :options="dining_period" :value="scope.row.mealType" />
              </template>
            </el-table-column>
            <el-table-column align="center" label="用餐名称" prop="foodItems" />
            <el-table-column align="center" label="实际用餐量" prop="mealAmount" />
            <el-table-column align="center" label="喂食/加饲" prop="feedingAssistance" />
            <el-table-column align="center" label="记录姓名" prop="recorderName" />
            <el-table-column align="center" label="备注" prop="remark" />
            <el-table-column
              v-if="false"
              align="center"
              class-name="small-padding fixed-width"
              label="操作"
            >
              <template #default="scope">
                <el-button
                  icon="Edit"
                  link
                  type="primary"
                  @click="handleUpdate4(scope.row)"
                  >修改</el-button
                >
                <el-button
                  icon="Delete"
                  link
                  type="primary"
                  @click="handleDelete4(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total4 > 0"
            v-model:limit="queryParams4.pageSize"
            v-model:page="queryParams4.pageNum"
            :total="total4"
            @pagination="getList4"
          />
          <!-- 添加或修改老人用餐记录对话框 -->
          <el-dialog v-model="open4" :title="title4" append-to-body width="50%">
            <el-form
              ref="mealRecordRef"
              :model="form4"
              :rules="rules4"
              label-width="120px"
            >
              <el-row>
                <el-col :span="12">
                  <el-form-item label="用餐时间" prop="mealTime">
                    <el-date-picker
                      v-model="form4.mealTime"
                      clearable
                      placeholder="请选择用餐时间"
                      style="width: 100%"
                      type="datetime"
                      value-format="YYYY-MM-DD hh:mm:ss"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="用餐时段" prop="mealType">
                    <el-select
                      v-model="form4.mealType"
                      :disabled="isShowOrEdit"
                      clearable
                      placeholder="请选择"
                      style="width: 100%"
                      @keyup.enter="handleQuery4"
                    >
                      <el-option
                        v-for="dict in dining_period"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="用餐名称/内容" prop="foodItems">
                <el-input
                  v-model="form4.foodItems"
                  placeholder="请输入内容"
                  type="textarea"
                />
              </el-form-item>
              <el-form-item label="实际用餐量" prop="mealAmount">
                <el-input v-model="form4.mealAmount" placeholder="请输入实际用餐量" />
              </el-form-item>
              <el-form-item label="喂食/加饲情况" prop="feedingAssistance">
                <el-input
                  v-model="form4.feedingAssistance"
                  placeholder="请输入喂食/加饲情况"
                />
              </el-form-item>
              <el-form-item label="用餐地点" prop="diningLocation">
                <el-input v-model="form4.diningLocation" placeholder="请输入用餐地点" />
              </el-form-item>
              <el-form-item label="喂食人" prop="recorderName">
                <el-input v-model="form4.recorderName" placeholder="请输入喂食人" />
              </el-form-item>
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="form4.remark"
                  placeholder="请输入内容"
                  type="textarea"
                />
              </el-form-item>
            </el-form>
            <template #footer>
              <div class="dialog-footer">
                <el-button type="primary" @click="submitForm4">确 定</el-button>
                <el-button @click="cancel4">取 消</el-button>
              </div>
            </template>
          </el-dialog>
        </div>
      </el-col>
    </el-row>
  </el-card>
</template>
<script name="healthRecords" setup>
import {
  addCareRecord,
  getCareRecord,
  listCareRecord,
  updateCareRecord,
} from "@/api/ReceptionManagement/tcareRecord"; //照护记录2
import {
  addMealRecord,
  getMealRecord,
  listMealRecord,
  updateMealRecord,
} from "@/api/ReceptionManagement/tmealRecord";
import {
  addMedicationRecord,
  getMedicationRecord,
  listMedicationRecord,
  updateMedicationRecord,
} from "@/api/ReceptionManagement/tmedicationRecord"; //服药记录3
import {
  addNursingRecord,
  getNursingRecord,
  listNursingRecord,
  updateNursingRecord,
} from "@/api/ReceptionManagement/tnursingRecord"; //护理记录1

const { proxy } = getCurrentInstance();

const {
  nursing_type, //护理类型
  medication_period, //服药时段
  dining_period, //用餐时段
  assessment_manager, //评估管理
  nursing_grade,
  medication_route, //服药途径
  medication_status, //服药状态
} = proxy.useDict(
  "nursing_type",
  "medication_period",
  "dining_period",
  "assessment_manager",
  "nursing_grade",
  "medication_route",
  "medication_status"
);

const careRecordList = ref([]);
const nursingRecordList = ref([]);
const medicationRecordList = ref([]);
const mealRecordList = ref([]);
const isSelectTopTag = ref("01");
const open1 = ref(false);
const open2 = ref(false);
const open3 = ref(false);
const open4 = ref(false);
const loading1 = ref(true);
const loading2 = ref(true);
const loading3 = ref(true);
const loading4 = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total1 = ref(0);
const total2 = ref(0);
const total3 = ref(0);
const total4 = ref(0);
const title1 = ref("");
const title2 = ref("");
const title3 = ref("");
const title4 = ref("");

const isShowOrEdit = ref(false);

const props = defineProps({
  // 老人的id
  elderId: {
    type: String,
    default: null,
  },
  isShow: {
    type: Boolean,
    default: false,
  },
});

const data = reactive({
  form1: {},
  queryParams1: {
    pageNum: 1,
    pageSize: 10,
    elderId: null,
    recordTime: null,
    careItemName: null,
    careStaffName: null,
    careStaffCode: null,
    careStaffId: null,
  },
  rules1: {
    elderId: [
      {
        required: true,
        message: "关联的老人ID不能为空",
        trigger: "blur",
      },
    ],
    recordTime: [
      {
        required: true,
        message: "照护记录时间(包含日期和时间)不能为空",
        trigger: "blur",
      },
    ],
    careItemName: [
      {
        required: true,
        message: "照护项目名称(如: 分餐, 巡视, 测量生命体征)不能为空",
        trigger: "blur",
      },
    ],
  },
  form2: {},
  queryParams2: {
    pageNum: 1,
    pageSize: 10,
    elderId: null,
    recordTime: null,
    nursingType: null,
    nursingItemName: null,
    nursingStaffName: null,
    nursingStaffCode: null,
    nursingStaffId: null,
  },
  rules2: {
    elderId: [
      {
        required: true,
        message: "关联的老人ID不能为空",
        trigger: "blur",
      },
    ],
    recordTime: [
      {
        required: true,
        message: "护理记录时间(包含日期和时间)不能为空",
        trigger: "blur",
      },
    ],
    nursingItemName: [
      {
        required: true,
        message: "护理项目名称(如: 面部清洁, 口腔清洁, 翻身叩背)不能为空",
        trigger: "blur",
      },
    ],
  },
  form3: {},
  queryParams3: {
    pageNum: 1,
    pageSize: 10,
    elderId: null,
    medicationTime: null,
    medicationName: null,
    medicationMethod: null,
    dosage: null,
    executorName: null,
    executorCode: null,
    executorId: null,
    status: null,
  },
  rules3: {
    elderId: [
      {
        required: true,
        message: "关联的老人ID不能为空",
        trigger: "blur",
      },
    ],
    medicationTime: [
      {
        required: true,
        message: "服药时间(包含日期和时间)不能为空",
        trigger: "blur",
      },
    ],
    medicationName: [
      {
        required: true,
        message: "药品名称不能为空",
        trigger: "blur",
      },
    ],
  },
  form4: {},
  queryParams4: {
    pageNum: 1,
    pageSize: 10,
    elderId: null,
    mealTime: null,
    mealType: null,
    foodItems: null,
    mealAmount: null,
    feedingAssistance: null,
    diningLocation: null,
    recorderName: null,
    recorderCode: null,
    recorderId: null,
  },
  rules4: {
    elderId: [
      {
        required: true,
        message: "关联的老人ID不能为空",
        trigger: "blur",
      },
    ],
    mealTime: [
      {
        required: true,
        message: "用餐时间(包含日期和时间)不能为空",
        trigger: "blur",
      },
    ],
    mealType: [
      {
        required: true,
        message: "餐次(早餐/午餐/晚餐/加餐)不能为空",
        trigger: "change",
      },
    ],
  },
});

const {
  queryParams1,
  form1,
  rules1,
  queryParams2,
  form2,
  rules2,
  queryParams3,
  form3,
  rules3,
  queryParams4,
  form4,
  rules4,
} = toRefs(data);

/** 查询照护记录列表 */
function getList1() {
  loading1.value = true;
  queryParams1.value.elderId = props.elderId;
  listNursingRecord(queryParams1.value).then((response) => {
    careRecordList.value = response.rows;
    total1.value = response.total;
    loading1.value = false;
  });
}

// 取消按钮
function cancel1() {
  open1.value = false;
  reset1();
}

// 表单重置
function reset1() {
  form1.value = {
    id: null,
    elderId: null,
    recordTime: null,
    careItemName: null,
    careStaffName: null,
    careStaffCode: null,
    careStaffId: null,
    remark: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
  };
  proxy.resetForm("nursingRecordRef");
}

/** 搜索按钮操作 */
function handleQuery1() {
  queryParams1.value.pageNum = 1;
  getList1();
}

/** 重置按钮操作 */
function resetQuery1() {
  proxy.resetForm("queryRef1");
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd1() {
  reset1();
  open1.value = true;
  title1.value = "添加护理记录";
}

/** 修改按钮操作 */
function handleUpdate1(row) {
  reset1();
  const _id = row.id || ids.value;
  getCareRecord(_id).then((response) => {
    form1.value = response.data;
    open1.value = true;
    title1.value = "修改护理记录";
  });
}

/** 提交按钮 */
function submitForm1() {
  proxy.$refs["nursingRecordRef"].validate((valid) => {
    if (valid) {
      form1.value.elderId = props.elderId;
      if (form1.value.id != null) {
        updateNursingRecord(form1.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open1.value = false;
          getList1();
        });
      } else {
        addNursingRecord(form1.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open1.value = false;
          getList1();
        });
      }
    }
  });
}

//-------------------------------2222-------------------------------//
function getList2() {
  loading2.value = true;
  queryParams2.value.elderId = props.elderId;
  listCareRecord(queryParams2.value).then((response) => {
    nursingRecordList.value = response.rows;
    total2.value = response.total;
    loading2.value = false;
  });
}

// 取消按钮
function cancel2() {
  open2.value = false;
  reset2();
}

// 表单重置
function reset2() {
  form2.value = {
    id: null,
    elderId: null,
    recordTime: null,
    careItemName: null,
    careStaffName: null,
    careStaffCode: null,
    careStaffId: null,
    remark: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
  };
  proxy.resetForm("careRecordRef");
}

/** 搜索按钮操作 */
function handleQuery2() {
  queryParams2.value.pageNum = 1;
  getList2();
}

/** 重置按钮操作 */
function resetQuery2() {
  proxy.resetForm("queryRef2");
  handleQuery2();
}

/** 新增按钮操作 */
function handleAdd2() {
  reset2();
  open2.value = true;
  title2.value = "添加照护记录";
}

/** 修改按钮操作 */
function handleUpdate2(row) {
  reset2();
  const _id = row.id || ids.value;
  getNursingRecord(_id).then((response) => {
    form2.value = response.data;
    open2.value = true;
    title2.value = "修改照护记录";
  });
}

/** 提交按钮 */
function submitForm2() {
  proxy.$refs["careRecordRef"].validate((valid) => {
    if (valid) {
      form2.value.elderId = props.elderId;
      if (form2.value.id != null) {
        updateCareRecord(form2.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open2.value = false;
          getList2();
        });
      } else {
        addCareRecord(form2.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open2.value = false;
          getList2();
        });
      }
    }
  });
}

//-------------------------------3333-------------------------------//
/** 查询老人服药记录列表 */
function getList3() {
  loading3.value = true;
  queryParams3.value.elderId = props.elderId;
  listMedicationRecord(queryParams3.value).then((response) => {
    medicationRecordList.value = response.rows;
    total3.value = response.total;
    loading3.value = false;
  });
}

// 取消按钮
function cancel3() {
  open3.value = false;
  reset3();
}

// 表单重置
function reset3() {
  form3.value = {
    id: null,
    elderId: null,
    medicationTime: null,
    medicationName: null,
    medicationMethod: null,
    executorName: null,
    executorCode: null,
    executorId: null,
    status: null,
    remark: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
  };
  proxy.resetForm("medicationRecordRef");
}

/** 搜索按钮操作 */
function handleQuery3() {
  queryParams3.value.pageNum = 1;
  getList3();
}

/** 重置按钮操作 */
function resetQuery3() {
  proxy.resetForm("queryRef3");
  handleQuery3();
}

/** 新增按钮操作 */
function handleAdd3() {
  reset3();
  open3.value = true;
  title3.value = "添加老人服药记录";
}

/** 修改按钮操作 */
function handleUpdate3(row) {
  reset3();
  const _id = row.id || ids.value;
  getMedicationRecord(_id).then((response) => {
    form3.value = response.data;
    open3.value = true;
    title3.value = "修改老人服药记录";
  });
}

/** 提交按钮 */
function submitForm3() {
  proxy.$refs["medicationRecordRef"].validate((valid) => {
    if (valid) {
      form3.value.elderId = props.elderId;
      if (form3.value.id != null) {
        updateMedicationRecord(form3.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open3.value = false;
          getList3();
        });
      } else {
        addMedicationRecord(form3.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open3.value = false;
          getList3();
        });
      }
    }
  });
}

//-------------------------------4444-------------------------------//
/** 查询老人用餐记录列表 */
function getList4() {
  loading4.value = true;
  queryParams4.value.elderId = props.elderId;
  listMealRecord(queryParams4.value).then((response) => {
    mealRecordList.value = response.rows;
    total4.value = response.total;
    loading4.value = false;
  });
}

// 取消按钮
function cancel4() {
  open4.value = false;
  reset4();
}

// 表单重置
function reset4() {
  form4.value = {
    id: null,
    elderId: null,
    mealTime: null,
    mealType: null,
    foodItems: null,
    mealAmount: null,
    feedingAssistance: null,
    diningLocation: null,
    recorderName: null,
    recorderCode: null,
    recorderId: null,
    remark: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
  };
  proxy.resetForm("mealRecordRef");
}

/** 搜索按钮操作 */
function handleQuery4() {
  queryParams4.value.pageNum = 1;
  getList4();
}

/** 重置按钮操作 */
function resetQuery4() {
  proxy.resetForm("queryRef4");
  handleQuery4();
}

/** 新增按钮操作 */
function handleAdd4() {
  reset4();
  open4.value = true;
  title4.value = "添加老人用餐记录";
}

/** 修改按钮操作 */
function handleUpdate4(row) {
  reset4();
  const _id = row.id || ids.value;
  getMealRecord(_id).then((response) => {
    form4.value = response.data;
    open4.value = true;
    title4.value = "修改老人用餐记录";
  });
}

/** 提交按钮 */
function submitForm4() {
  proxy.$refs["mealRecordRef"].validate((valid) => {
    if (valid) {
      form4.value.elderId = props.elderId;
      if (form4.value.id != null) {
        updateMealRecord(form4.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open4.value = false;
          getList4();
        });
      } else {
        addMealRecord(form4.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open4.value = false;
          getList4();
        });
      }
    }
  });
}

//-------------------------------all-------------------------------//
function showTagDetail(type) {
  if (type == "01") {
    isSelectTopTag.value = "01";
    getList1();
  } else if (type == "02") {
    isSelectTopTag.value = "02";
    getList2();
  } else if (type == "03") {
    isSelectTopTag.value = "03";
    getList3();
  } else if (type == "04") {
    isSelectTopTag.value = "04";
    getList4();
  }
}

function init() {
  getList1();
}

init();
</script>
<style lang="scss" scoped>
.cardDetailTop {
  display: flex;
  flex-direction: row;
  justify-content: center;
}

.cardDetailcenter {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.cardDetailTopDiv {
  width: 120px;
  height: 40px;

  text-align: center;
  margin-right: 5px;
  font-size: 16px;
  color: #999;
  font-weight: 600;
}

.cardDetailTopDivSelect {
  border-bottom: 2px solid rgb(64, 158, 255);
}

.healthyTitle {
  margin: 10px 0px;
  font-size: 16px;
  font-weight: 600;
  color: #999;
}

.healthyDivCss {
  width: 100%;
  height: 300px;
}

.timelineProcessBox {
  .timeline {
    display: flex;
    width: 95%;
    height: 80px;
    margin: 10px 0px;

    .lineitem {
      transform: translateX(50%);
      width: 25%;
    }
  }
}

:deep(.el-timeline-item__tail) {
  border-left: none;
  border-top: 2px solid #e4e7ed;
  width: 100%;
  position: absolute;
  top: 6px;
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 0;
  position: absolute;
  top: 20px;
  transform: translateX(-50%);
  text-align: center;
}

:deep(.el-timeline-item__timestamp) {
  font-size: 14px;
}

.active {
  :deep(.el-timeline-item__node) {
    background-color: #dad8d8;
  }

  :deep(.el-timeline-item__tail) {
    border-color: #dad8d8;
  }
}

// 有active样式的下一个li
.active + li {
  :deep(.el-timeline-item__node) {
    background-color: #dad8d8;
  }
}

.historyCss {
  color: #606266;
  font-weight: 600;
  font-size: 14px;
}

.historyDiv {
  width: 100%;
  height: 420px;
  margin-left: 50px;
  padding-top: 10px;
}

.subtitleCss {
  font-size: 18px;
  color: rgb(64, 158, 225);
  font-weight: 600;
}
</style>
